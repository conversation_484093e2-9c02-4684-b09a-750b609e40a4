"filename", "language", "JSON", "TypeScript", "JSON with Comments", "CSS", "JavaScript", "Markdown", "TypeScript React", "XML", "comment", "blank", "total"
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.eslintrc.json", "JSON", 3, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.prettierrc", "JSON", 5, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\README.md", "Markdown", 0, 0, 0, 0, 0, 20, 0, 0, 0, 15, 35
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\next-env.d.ts", "TypeScript", 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 6
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\next.config.js", "JavaScript", 0, 0, 0, 0, 10, 0, 0, 0, 1, 2, 13
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\package.json", "JSON", 87, 0, 0, 0, 0, 0, 0, 0, 0, 1, 88
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\postcss.config.js", "JavaScript", 0, 0, 0, 0, 6, 0, 0, 0, 0, 1, 7
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\public\vercel.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 4
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\hook.ts", "TypeScript", 0, 4, 0, 0, 0, 0, 0, 0, 0, 1, 5
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\index.ts", "TypeScript", 0, 21, 0, 0, 0, 0, 0, 0, 0, 3, 24
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\function.ts", "TypeScript", 0, 60, 0, 0, 0, 0, 0, 0, 2, 8, 70
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\index.ts", "TypeScript", 0, 262, 0, 0, 0, 0, 0, 0, 2, 17, 281
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\thunk.ts", "TypeScript", 0, 16, 0, 0, 0, 0, 0, 0, 0, 2, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\index.ts", "TypeScript", 0, 91, 0, 0, 0, 0, 0, 0, 0, 6, 97
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\thunks.ts", "TypeScript", 0, 52, 0, 0, 0, 0, 0, 0, 0, 7, 59
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\techBooking\index.ts", "TypeScript", 0, 57, 0, 0, 0, 0, 0, 0, 0, 6, 63
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\techBooking\thunk.ts", "TypeScript", 0, 8, 0, 0, 0, 0, 0, 0, 0, 2, 10
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Layout\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 189, 0, 25, 9, 223
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\ConfirmCancelModal.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 60, 0, 0, 4, 64
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 10, 0, 0, 3, 13
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Stepper\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 103, 0, 0, 5, 108
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\AuthInput\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 19, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\BorderSelect.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 20, 0, 0, 3, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AccentTextButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 18, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AuthButton\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 38, 0, 0, 4, 42
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundMiddleButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 33, 0, 0, 4, 37
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundSmallButton\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 33, 0, 0, 5, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BorderSmallButton\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 22, 0, 0, 4, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\MenuButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 24, 0, 0, 4, 28
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\MiddleButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 37, 0, 0, 4, 41
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\SmallButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 32, 0, 0, 4, 36
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\TextButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 20, 0, 0, 3, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ImageFileInput.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 40, 0, 0, 8, 48
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\NoPaddingPaper.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 19, 0, 0, 3, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Paper.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 20, 0, 0, 4, 24
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ProfilePlaceholder.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 28, 0, 0, 5, 33
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TextInput.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 41, 0, 0, 4, 45
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayParagraph\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 18, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayText14.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 18, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\InstructionText\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 22, 0, 0, 4, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\MenuSectionTitle\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 16, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ModalTitle\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 16, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileDescription\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 16, 0, 0, 3, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileFieldTitle\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 17, 0, 0, 4, 21
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileFieldValue\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 14, 0, 0, 3, 17
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileTitle\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 11, 0, 0, 4, 15
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Paragraph14.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text12.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text14.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text16.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 18, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title18.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title24.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title32.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 19, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TimeSelect.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 32, 0, 0, 5, 37
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\AdminSubHeader\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 23, 0, 0, 4, 27
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\CalendarHeader\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 52, 0, 0, 4, 56
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\MobileStepper\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ModalConfirmCancelBar.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 32, 0, 0, 4, 36
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddCarModal\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 253, 0, 15, 13, 281
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AdminLayout\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 63, 0, 0, 4, 67
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AdminSideModal\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 19, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\BookingInfoModal\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 83, 0, 30, 3, 116
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\CustomerLayout\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 71, 0, 0, 4, 75
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 202, 0, 0, 9, 211
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\mobile.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 52, 0, 0, 5, 57
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Menu\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 12, 0, 0, 4, 16
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TechnicianLayout\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 52, 0, 45, 4, 101
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TimeTable\DateRow.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 44, 0, 0, 4, 48
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TimeTable\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 336, 0, 4, 21, 361
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\context\global.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 20, 0, 0, 6, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\addon.ts", "TypeScript", 0, 58, 0, 0, 0, 0, 0, 0, 0, 8, 66
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\availability.ts", "TypeScript", 0, 92, 0, 0, 0, 0, 0, 0, 0, 8, 100
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\booking.ts", "TypeScript", 0, 43, 0, 0, 0, 0, 0, 0, 0, 4, 47
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\calendar.ts", "TypeScript", 0, 18, 0, 0, 0, 0, 0, 0, 0, 2, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\car.ts", "TypeScript", 0, 51, 0, 0, 0, 0, 0, 0, 0, 8, 59
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\request.ts", "TypeScript", 0, 37, 0, 0, 0, 0, 0, 0, 0, 7, 44
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\service.ts", "TypeScript", 0, 31, 0, 0, 0, 0, 0, 0, 0, 3, 34
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\stripe.ts", "TypeScript", 0, 7, 0, 0, 0, 0, 0, 0, 1, 3, 11
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\technician.ts", "TypeScript", 0, 17, 0, 0, 0, 0, 0, 0, 0, 2, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\user.ts", "TypeScript", 0, 40, 0, 0, 0, 0, 0, 0, 1, 3, 44
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\appointment.ts", "TypeScript", 0, 70, 0, 0, 0, 0, 0, 0, 8, 12, 90
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\calendar.ts", "TypeScript", 0, 20, 0, 0, 0, 0, 0, 0, 0, 7, 27
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\index.ts", "TypeScript", 0, 102, 0, 0, 0, 0, 0, 0, 0, 13, 115
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\pool.ts", "TypeScript", 0, 7, 0, 0, 0, 0, 0, 0, 0, 2, 9
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\util.ts", "TypeScript", 0, 25, 0, 0, 0, 0, 0, 0, 0, 4, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\local\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\util.ts", "TypeScript", 0, 23, 0, 0, 0, 0, 0, 0, 0, 6, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAvailability.ts", "TypeScript", 0, 118, 0, 0, 0, 0, 0, 0, 1, 12, 131
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCalendarController.ts", "TypeScript", 0, 40, 0, 0, 0, 0, 0, 0, 0, 7, 47
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCategories.ts", "TypeScript", 0, 19, 0, 0, 0, 0, 0, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianSearch.ts", "TypeScript", 0, 45, 0, 0, 0, 0, 0, 0, 1, 6, 52
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTimer.ts", "TypeScript", 0, 25, 0, 0, 0, 0, 0, 0, 0, 5, 30
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CarItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CategoryServiceItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 25, 0, 0, 4, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\ReviewItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 82, 0, 0, 4, 86
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SelectedTechnicianInfo.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 66, 0, 0, 4, 70
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SideMenu.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 132, 0, 0, 7, 139
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 122, 0, 0, 4, 126
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianList.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 186, 0, 0, 7, 193
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\AddressBlock.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 210, 0, 0, 5, 215
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\InformationBlock.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 208, 0, 0, 8, 216
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleBlock.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 43, 0, 0, 5, 48
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 30, 0, 0, 4, 34
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\PaymentBlock.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 118, 0, 0, 8, 126
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\ServiceItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 46, 0, 1, 5, 52
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\TimeBlock.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 23, 0, 0, 3, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\AddCarButton.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 26, 0, 0, 4, 30
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 72, 0, 0, 3, 75
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarMenu.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 38, 0, 0, 4, 42
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\MobileServiceItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 55, 0, 0, 4, 59
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceItem.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 77, 0, 0, 3, 80
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceTotalViewer.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 89, 0, 10, 5, 104
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\Services.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 109, 0, 9, 8, 126
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityBlock.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 34, 0, 0, 4, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityModal.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 354, 0, 0, 19, 373
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilitySection.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 26, 0, 12, 5, 43
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\CalendarSection.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 54, 0, 0, 6, 60
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_app.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 15, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_document.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 30, 0, 0, 4, 34
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\account.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 82, 0, 1, 8, 91
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_addons\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 237, 0, 0, 14, 251
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_service\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 193, 0, 2, 16, 211
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_technicians\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 212, 0, 1, 5, 218
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\availability\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 149, 0, 0, 11, 160
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\cancel.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 23, 0, 0, 4, 27
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\confirm.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 71, 0, 0, 7, 78
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_booking.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 194, 0, 37, 12, 243
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_profile\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 61, 0, 0, 4, 65
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 122, 0, 77, 14, 213
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\information\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 209, 0, 14, 13, 236
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\login.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 122, 0, 0, 9, 131
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 119, 0, 0, 7, 126
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech_confirm.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 79, 0, 0, 4, 83
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech_info.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 221, 0, 0, 14, 235
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\rating.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 174, 0, 0, 10, 184
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\signup.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 166, 0, 0, 7, 173
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\success.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 29, 0, 0, 5, 34
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_booking\index.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 189, 0, 9, 15, 213
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_earning.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 71, 0, 0, 3, 74
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_setting.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 9, 0, 0, 3, 12
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\technicians.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 91, 0, 0, 6, 97
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 26, 0, 0, 5, 31
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_buttons.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 26, 0, 0, 5, 31
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_texts.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 20, 0, 0, 6, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\templateComponents\Description.tsx", "TypeScript React", 0, 0, 0, 0, 0, 0, 11, 0, 0, 4, 15
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\theme\index.ts", "TypeScript", 0, 11, 0, 0, 0, 0, 0, 0, 0, 3, 14
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\addon.ts", "TypeScript", 0, 12, 0, 0, 0, 0, 0, 0, 0, 4, 16
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\calendarTypeBooking.ts", "TypeScript", 0, 26, 0, 0, 0, 0, 0, 0, 0, 4, 30
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\car.ts", "TypeScript", 0, 31, 0, 0, 0, 0, 0, 0, 0, 8, 39
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\category.ts", "TypeScript", 0, 71, 0, 0, 0, 0, 0, 0, 1, 8, 80
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\index.ts", "TypeScript", 0, 90, 0, 0, 0, 0, 0, 0, 0, 8, 98
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\interfaces.ts", "TypeScript", 0, 15, 0, 0, 0, 0, 0, 0, 0, 2, 17
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\user.ts", "TypeScript", 0, 48, 0, 0, 0, 0, 0, 0, 0, 13, 61
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\values\index.ts", "TypeScript", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\Home.module.css", "CSS", 0, 0, 0, 100, 0, 0, 0, 0, 0, 17, 117
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\globals.css", "CSS", 0, 0, 0, 68, 0, 0, 0, 0, 0, 14, 82
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tailwind.config.js", "JavaScript", 0, 0, 0, 0, 16, 0, 0, 0, 0, 2, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tsconfig.json", "JSON with Comments", 0, 0, 20, 0, 0, 0, 0, 0, 0, 1, 21
"Total", "-", 95, 1764, 20, 168, 32, 20, 7633, 4, 314, 893, 10943