{"file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/tsconfig.json": {"language": "JSON with Comments", "code": 20, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/styles/globals.css": {"language": "CSS", "code": 68, "comment": 0, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/styles/Home.module.css": {"language": "CSS", "code": 100, "comment": 0, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/package.json": {"language": "JSON", "code": 87, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/tailwind.config.js": {"language": "JavaScript", "code": 16, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/values/index.ts": {"language": "TypeScript", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/hook.ts": {"language": "TypeScript", "code": 4, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/interfaces.ts": {"language": "TypeScript", "code": 15, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/index.ts": {"language": "TypeScript", "code": 21, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/user.ts": {"language": "TypeScript", "code": 48, "comment": 0, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/index.ts": {"language": "TypeScript", "code": 90, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/category.ts": {"language": "TypeScript", "code": 71, "comment": 1, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/theme/index.ts": {"language": "TypeScript", "code": 11, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/postcss.config.js": {"language": "JavaScript", "code": 6, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/calendarTypeBooking.ts": {"language": "TypeScript", "code": 26, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/car.ts": {"language": "TypeScript", "code": 31, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/types/addon.ts": {"language": "TypeScript", "code": 12, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/templateComponents/Description.tsx": {"language": "TypeScript React", "code": 11, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/customer_booking.tsx": {"language": "TypeScript React", "code": 194, "comment": 37, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/new_tech_info.tsx": {"language": "TypeScript React", "code": 221, "comment": 0, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/tech_booking/AvailabilitySection.tsx": {"language": "TypeScript React", "code": 26, "comment": 12, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/new_tech_confirm.tsx": {"language": "TypeScript React", "code": 79, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/rating.tsx": {"language": "TypeScript React", "code": 174, "comment": 0, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/new_tech.tsx": {"language": "TypeScript React", "code": 119, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/hooks/useTimer.ts": {"language": "TypeScript", "code": 25, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/login.tsx": {"language": "TypeScript React", "code": 122, "comment": 0, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/tech_booking/AvailabilityModal.tsx": {"language": "TypeScript React", "code": 354, "comment": 0, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/hooks/useTechnicianSearch.ts": {"language": "TypeScript", "code": 45, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/tech_booking/CalendarSection.tsx": {"language": "TypeScript React", "code": 54, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/tech_booking/AvailabilityBlock.tsx": {"language": "TypeScript React", "code": 34, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/hooks/useCategories.ts": {"language": "TypeScript", "code": 19, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/success.tsx": {"language": "TypeScript React", "code": 29, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/tech_earning.tsx": {"language": "TypeScript React", "code": 71, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/tech_booking/index.tsx": {"language": "TypeScript React", "code": 189, "comment": 9, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/signup.tsx": {"language": "TypeScript React", "code": 166, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/information/index.tsx": {"language": "TypeScript React", "code": 209, "comment": 14, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/_document.tsx": {"language": "TypeScript React", "code": 30, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/README.md": {"language": "<PERSON><PERSON>", "code": 20, "comment": 0, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/_app.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/test_buttons.tsx": {"language": "TypeScript React", "code": 26, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/test.tsx": {"language": "TypeScript React", "code": 26, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/tech_setting.tsx": {"language": "TypeScript React", "code": 9, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/test_texts.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/technicians.tsx": {"language": "TypeScript React", "code": 91, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/index.tsx": {"language": "TypeScript React", "code": 122, "comment": 77, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/hooks/useCalendarController.ts": {"language": "TypeScript", "code": 40, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/confirm.tsx": {"language": "TypeScript React", "code": 71, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/hooks/useAvailability.ts": {"language": "TypeScript", "code": 118, "comment": 1, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/cancel.tsx": {"language": "TypeScript React", "code": 23, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/admin_technicians/index.tsx": {"language": "TypeScript React", "code": 212, "comment": 1, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/customer_profile/index.tsx": {"language": "TypeScript React", "code": 61, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/availability/index.tsx": {"language": "TypeScript React", "code": 149, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/MobileServiceItem.tsx": {"language": "TypeScript React", "code": 55, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/admin_service/index.tsx": {"language": "TypeScript React", "code": 193, "comment": 2, "blank": 16}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/CarMenu.tsx": {"language": "TypeScript React", "code": 38, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/CarItem.tsx": {"language": "TypeScript React", "code": 72, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/ServiceItem.tsx": {"language": "TypeScript React", "code": 77, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/AddCarButton.tsx": {"language": "TypeScript React", "code": 26, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/Services.tsx": {"language": "TypeScript React", "code": 109, "comment": 9, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/public/vercel.svg": {"language": "XML", "code": 4, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/account.tsx": {"language": "TypeScript React", "code": 82, "comment": 1, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/information/ServiceItem.tsx": {"language": "TypeScript React", "code": 46, "comment": 1, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/select_service/ServiceTotalViewer.tsx": {"language": "TypeScript React", "code": 89, "comment": 10, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pages/admin_addons/index.tsx": {"language": "TypeScript React", "code": 237, "comment": 0, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/information/TimeBlock.tsx": {"language": "TypeScript React", "code": 23, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/CategoryServiceItem.tsx": {"language": "TypeScript React", "code": 25, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/information/PaymentBlock.tsx": {"language": "TypeScript React", "code": 118, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/ReviewItem.tsx": {"language": "TypeScript React", "code": 82, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/CarItem.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/customer_profile/VehicleItem.tsx": {"language": "TypeScript React", "code": 30, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/customer_profile/VehicleBlock.tsx": {"language": "TypeScript React", "code": 43, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/customer_profile/InformationBlock.tsx": {"language": "TypeScript React", "code": 208, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/customer_profile/AddressBlock.tsx": {"language": "TypeScript React", "code": 210, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/SideMenu.tsx": {"language": "TypeScript React", "code": 132, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/SelectedTechnicianInfo.tsx": {"language": "TypeScript React", "code": 66, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/techBooking/index.ts": {"language": "TypeScript", "code": 57, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/TechnicianList.tsx": {"language": "TypeScript React", "code": 186, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/pageComponents/availability/TechnicianItem.tsx": {"language": "TypeScript React", "code": 122, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/auth/thunks.ts": {"language": "TypeScript", "code": 52, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/techBooking/thunk.ts": {"language": "TypeScript", "code": 8, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/auth/index.ts": {"language": "TypeScript", "code": 91, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/appointment/function.ts": {"language": "TypeScript", "code": 60, "comment": 2, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/appointment/index.ts": {"language": "TypeScript", "code": 262, "comment": 2, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/next-env.d.ts": {"language": "TypeScript", "code": 0, "comment": 4, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/appointment.ts": {"language": "TypeScript", "code": 70, "comment": 8, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/calendar.ts": {"language": "TypeScript", "code": 20, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/util.ts": {"language": "TypeScript", "code": 23, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/context/global.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/.prettierrc": {"language": "JSON", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/next.config.js": {"language": "JavaScript", "code": 10, "comment": 1, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/Layout/index.tsx": {"language": "TypeScript React", "code": 189, "comment": 25, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/local/index.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/cognito/util.ts": {"language": "TypeScript", "code": 25, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/cognito/pool.ts": {"language": "TypeScript", "code": 7, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/cognito/index.ts": {"language": "TypeScript", "code": 102, "comment": 0, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/TechnicianLayout/index.tsx": {"language": "TypeScript React", "code": 52, "comment": 45, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/tmp.tsx": {"language": "TypeScript React", "code": 0, "comment": 1089, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/TimeTable/DateRow.tsx": {"language": "TypeScript React", "code": 44, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/Header/mobile.tsx": {"language": "TypeScript React", "code": 52, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/TimeTable/index.tsx": {"language": "TypeScript React", "code": 336, "comment": 4, "blank": 21}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/Menu/index.tsx": {"language": "TypeScript React", "code": 12, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/Header/index.tsx": {"language": "TypeScript React", "code": 202, "comment": 0, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/CustomerLayout/index.tsx": {"language": "TypeScript React", "code": 71, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/Stepper/index.tsx": {"language": "TypeScript React", "code": 103, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/AdminSideModal/index.tsx": {"language": "TypeScript React", "code": 19, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/BookingInfoModal/index.tsx": {"language": "TypeScript React", "code": 83, "comment": 30, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/AdminLayout/index.tsx": {"language": "TypeScript React", "code": 63, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/organism/AddCarModal/index.tsx": {"language": "TypeScript React", "code": 253, "comment": 15, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/NoPaddingPaper.tsx": {"language": "TypeScript React", "code": 19, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/ImageFileInput.tsx": {"language": "TypeScript React", "code": 40, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/%40redux/modules/appointment/thunk.ts": {"language": "TypeScript", "code": 16, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/molecule/ModalConfirmCancelBar.tsx": {"language": "TypeScript React", "code": 32, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Paper.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/BorderSelect.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/car.ts": {"language": "TypeScript", "code": 51, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/calendar.ts": {"language": "TypeScript", "code": 18, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/booking.ts": {"language": "TypeScript", "code": 43, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/molecule/AdminSubHeader/index.tsx": {"language": "TypeScript React", "code": 23, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/TimeSelect.tsx": {"language": "TypeScript React", "code": 32, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/availability.ts": {"language": "TypeScript", "code": 92, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/TextInput.tsx": {"language": "TypeScript React", "code": 41, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/addon.ts": {"language": "TypeScript", "code": 58, "comment": 0, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/MenuButton.tsx": {"language": "TypeScript React", "code": 24, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/ProfilePlaceholder.tsx": {"language": "TypeScript React", "code": 28, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/user.ts": {"language": "TypeScript", "code": 40, "comment": 1, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/stripe.ts": {"language": "TypeScript", "code": 7, "comment": 1, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/molecule/MobileStepper/index.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/technician.ts": {"language": "TypeScript", "code": 17, "comment": 0, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/BackgroundMiddleButton.tsx": {"language": "TypeScript React", "code": 33, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/AccentTextButton.tsx": {"language": "TypeScript React", "code": 18, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/service.ts": {"language": "TypeScript", "code": 31, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/functions/api/request.ts": {"language": "TypeScript", "code": 37, "comment": 0, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/AuthInput/index.tsx": {"language": "TypeScript React", "code": 19, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/molecule/CalendarHeader/index.tsx": {"language": "TypeScript React", "code": 52, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/Modal/index.tsx": {"language": "TypeScript React", "code": 10, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/Modal/ConfirmCancelModal.tsx": {"language": "TypeScript React", "code": 60, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/MenuSectionTitle/index.tsx": {"language": "TypeScript React", "code": 16, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/AuthButton/index.tsx": {"language": "TypeScript React", "code": 38, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/ProfileFieldValue/index.tsx": {"language": "TypeScript React", "code": 14, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/ProfileDescription/index.tsx": {"language": "TypeScript React", "code": 16, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/BackgroundSmallButton/index.tsx": {"language": "TypeScript React", "code": 33, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/ProfileFieldTitle/index.tsx": {"language": "TypeScript React", "code": 17, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/GrayText14.tsx": {"language": "TypeScript React", "code": 18, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/BorderSmallButton/index.tsx": {"language": "TypeScript React", "code": 22, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/base/SmallButton.tsx": {"language": "TypeScript React", "code": 32, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/base/TextButton.tsx": {"language": "TypeScript React", "code": 20, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Paragraph14.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/.eslintrc.json": {"language": "JSON", "code": 3, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/InstructionText/index.tsx": {"language": "TypeScript React", "code": 22, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/GrayParagraph/index.tsx": {"language": "TypeScript React", "code": 18, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Buttons/base/MiddleButton.tsx": {"language": "TypeScript React", "code": 37, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/ProfileTitle/index.tsx": {"language": "TypeScript React", "code": 11, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/ModalTitle/index.tsx": {"language": "TypeScript React", "code": 16, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Text16.tsx": {"language": "TypeScript React", "code": 18, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Text12.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Title18.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Text14.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Title24.tsx": {"language": "TypeScript React", "code": 15, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/Desktop/desk/luxe/wheeleasy_web/src/components/atom/Texts/base/Title32.tsx": {"language": "TypeScript React", "code": 19, "comment": 0, "blank": 4}}