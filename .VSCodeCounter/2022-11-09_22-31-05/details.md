# Details

Date : 2022-11-09 22:31:05

Directory c:\\Users\\<USER>\\Desktop\\Desktop\\desk\\luxe\\wheeleasy_web

Total : 159 files,  9736 codes, 1403 comments, 897 blanks, all 12036 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.eslintrc.json](/.eslintrc.json) | JSON | 3 | 0 | 1 | 4 |
| [.prettierrc](/.prettierrc) | JSON | 5 | 0 | 1 | 6 |
| [README.md](/README.md) | Markdown | 20 | 0 | 15 | 35 |
| [next-env.d.ts](/next-env.d.ts) | TypeScript | 0 | 4 | 2 | 6 |
| [next.config.js](/next.config.js) | JavaScript | 10 | 1 | 2 | 13 |
| [package.json](/package.json) | JSON | 87 | 0 | 1 | 88 |
| [postcss.config.js](/postcss.config.js) | JavaScript | 6 | 0 | 1 | 7 |
| [public/vercel.svg](/public/vercel.svg) | XML | 4 | 0 | 0 | 4 |
| [src/@redux/hook.ts](/src/@redux/hook.ts) | TypeScript | 4 | 0 | 1 | 5 |
| [src/@redux/index.ts](/src/@redux/index.ts) | TypeScript | 21 | 0 | 3 | 24 |
| [src/@redux/modules/appointment/function.ts](/src/@redux/modules/appointment/function.ts) | TypeScript | 60 | 2 | 8 | 70 |
| [src/@redux/modules/appointment/index.ts](/src/@redux/modules/appointment/index.ts) | TypeScript | 262 | 2 | 17 | 281 |
| [src/@redux/modules/appointment/thunk.ts](/src/@redux/modules/appointment/thunk.ts) | TypeScript | 16 | 0 | 2 | 18 |
| [src/@redux/modules/auth/index.ts](/src/@redux/modules/auth/index.ts) | TypeScript | 91 | 0 | 6 | 97 |
| [src/@redux/modules/auth/thunks.ts](/src/@redux/modules/auth/thunks.ts) | TypeScript | 52 | 0 | 7 | 59 |
| [src/@redux/modules/techBooking/index.ts](/src/@redux/modules/techBooking/index.ts) | TypeScript | 57 | 0 | 6 | 63 |
| [src/@redux/modules/techBooking/thunk.ts](/src/@redux/modules/techBooking/thunk.ts) | TypeScript | 8 | 0 | 2 | 10 |
| [src/components/Layout/index.tsx](/src/components/Layout/index.tsx) | TypeScript React | 189 | 25 | 9 | 223 |
| [src/components/Modal/ConfirmCancelModal.tsx](/src/components/Modal/ConfirmCancelModal.tsx) | TypeScript React | 60 | 0 | 4 | 64 |
| [src/components/Modal/index.tsx](/src/components/Modal/index.tsx) | TypeScript React | 10 | 0 | 3 | 13 |
| [src/components/Stepper/index.tsx](/src/components/Stepper/index.tsx) | TypeScript React | 103 | 0 | 5 | 108 |
| [src/components/atom/AuthInput/index.tsx](/src/components/atom/AuthInput/index.tsx) | TypeScript React | 19 | 0 | 4 | 23 |
| [src/components/atom/BorderSelect.tsx](/src/components/atom/BorderSelect.tsx) | TypeScript React | 20 | 0 | 3 | 23 |
| [src/components/atom/Buttons/AccentTextButton.tsx](/src/components/atom/Buttons/AccentTextButton.tsx) | TypeScript React | 18 | 0 | 4 | 22 |
| [src/components/atom/Buttons/AuthButton/index.tsx](/src/components/atom/Buttons/AuthButton/index.tsx) | TypeScript React | 38 | 0 | 4 | 42 |
| [src/components/atom/Buttons/BackgroundMiddleButton.tsx](/src/components/atom/Buttons/BackgroundMiddleButton.tsx) | TypeScript React | 33 | 0 | 4 | 37 |
| [src/components/atom/Buttons/BackgroundSmallButton/index.tsx](/src/components/atom/Buttons/BackgroundSmallButton/index.tsx) | TypeScript React | 33 | 0 | 5 | 38 |
| [src/components/atom/Buttons/BorderSmallButton/index.tsx](/src/components/atom/Buttons/BorderSmallButton/index.tsx) | TypeScript React | 22 | 0 | 4 | 26 |
| [src/components/atom/Buttons/MenuButton.tsx](/src/components/atom/Buttons/MenuButton.tsx) | TypeScript React | 24 | 0 | 4 | 28 |
| [src/components/atom/Buttons/base/MiddleButton.tsx](/src/components/atom/Buttons/base/MiddleButton.tsx) | TypeScript React | 37 | 0 | 4 | 41 |
| [src/components/atom/Buttons/base/SmallButton.tsx](/src/components/atom/Buttons/base/SmallButton.tsx) | TypeScript React | 32 | 0 | 4 | 36 |
| [src/components/atom/Buttons/base/TextButton.tsx](/src/components/atom/Buttons/base/TextButton.tsx) | TypeScript React | 20 | 0 | 3 | 23 |
| [src/components/atom/ImageFileInput.tsx](/src/components/atom/ImageFileInput.tsx) | TypeScript React | 40 | 0 | 8 | 48 |
| [src/components/atom/NoPaddingPaper.tsx](/src/components/atom/NoPaddingPaper.tsx) | TypeScript React | 19 | 0 | 3 | 22 |
| [src/components/atom/Paper.tsx](/src/components/atom/Paper.tsx) | TypeScript React | 20 | 0 | 4 | 24 |
| [src/components/atom/ProfilePlaceholder.tsx](/src/components/atom/ProfilePlaceholder.tsx) | TypeScript React | 28 | 0 | 5 | 33 |
| [src/components/atom/TextInput.tsx](/src/components/atom/TextInput.tsx) | TypeScript React | 41 | 0 | 4 | 45 |
| [src/components/atom/Texts/GrayParagraph/index.tsx](/src/components/atom/Texts/GrayParagraph/index.tsx) | TypeScript React | 18 | 0 | 4 | 22 |
| [src/components/atom/Texts/GrayText14.tsx](/src/components/atom/Texts/GrayText14.tsx) | TypeScript React | 18 | 0 | 4 | 22 |
| [src/components/atom/Texts/InstructionText/index.tsx](/src/components/atom/Texts/InstructionText/index.tsx) | TypeScript React | 22 | 0 | 4 | 26 |
| [src/components/atom/Texts/MenuSectionTitle/index.tsx](/src/components/atom/Texts/MenuSectionTitle/index.tsx) | TypeScript React | 16 | 0 | 4 | 20 |
| [src/components/atom/Texts/ModalTitle/index.tsx](/src/components/atom/Texts/ModalTitle/index.tsx) | TypeScript React | 16 | 0 | 4 | 20 |
| [src/components/atom/Texts/ProfileDescription/index.tsx](/src/components/atom/Texts/ProfileDescription/index.tsx) | TypeScript React | 16 | 0 | 3 | 19 |
| [src/components/atom/Texts/ProfileFieldTitle/index.tsx](/src/components/atom/Texts/ProfileFieldTitle/index.tsx) | TypeScript React | 17 | 0 | 4 | 21 |
| [src/components/atom/Texts/ProfileFieldValue/index.tsx](/src/components/atom/Texts/ProfileFieldValue/index.tsx) | TypeScript React | 14 | 0 | 3 | 17 |
| [src/components/atom/Texts/ProfileTitle/index.tsx](/src/components/atom/Texts/ProfileTitle/index.tsx) | TypeScript React | 11 | 0 | 4 | 15 |
| [src/components/atom/Texts/base/Paragraph14.tsx](/src/components/atom/Texts/base/Paragraph14.tsx) | TypeScript React | 15 | 0 | 4 | 19 |
| [src/components/atom/Texts/base/Text12.tsx](/src/components/atom/Texts/base/Text12.tsx) | TypeScript React | 15 | 0 | 4 | 19 |
| [src/components/atom/Texts/base/Text14.tsx](/src/components/atom/Texts/base/Text14.tsx) | TypeScript React | 15 | 0 | 4 | 19 |
| [src/components/atom/Texts/base/Text16.tsx](/src/components/atom/Texts/base/Text16.tsx) | TypeScript React | 18 | 0 | 4 | 22 |
| [src/components/atom/Texts/base/Title18.tsx](/src/components/atom/Texts/base/Title18.tsx) | TypeScript React | 15 | 0 | 3 | 18 |
| [src/components/atom/Texts/base/Title24.tsx](/src/components/atom/Texts/base/Title24.tsx) | TypeScript React | 15 | 0 | 3 | 18 |
| [src/components/atom/Texts/base/Title32.tsx](/src/components/atom/Texts/base/Title32.tsx) | TypeScript React | 19 | 0 | 4 | 23 |
| [src/components/atom/TimeSelect.tsx](/src/components/atom/TimeSelect.tsx) | TypeScript React | 32 | 0 | 5 | 37 |
| [src/components/molecule/AdminSubHeader/index.tsx](/src/components/molecule/AdminSubHeader/index.tsx) | TypeScript React | 23 | 0 | 4 | 27 |
| [src/components/molecule/CalendarHeader/index.tsx](/src/components/molecule/CalendarHeader/index.tsx) | TypeScript React | 52 | 0 | 4 | 56 |
| [src/components/molecule/MobileStepper/index.tsx](/src/components/molecule/MobileStepper/index.tsx) | TypeScript React | 15 | 0 | 4 | 19 |
| [src/components/molecule/ModalConfirmCancelBar.tsx](/src/components/molecule/ModalConfirmCancelBar.tsx) | TypeScript React | 32 | 0 | 4 | 36 |
| [src/components/organism/AddCarModal/index.tsx](/src/components/organism/AddCarModal/index.tsx) | TypeScript React | 253 | 15 | 13 | 281 |
| [src/components/organism/AdminLayout/index.tsx](/src/components/organism/AdminLayout/index.tsx) | TypeScript React | 63 | 0 | 4 | 67 |
| [src/components/organism/AdminSideModal/index.tsx](/src/components/organism/AdminSideModal/index.tsx) | TypeScript React | 19 | 0 | 4 | 23 |
| [src/components/organism/BookingInfoModal/index.tsx](/src/components/organism/BookingInfoModal/index.tsx) | TypeScript React | 83 | 30 | 3 | 116 |
| [src/components/organism/CustomerLayout/index.tsx](/src/components/organism/CustomerLayout/index.tsx) | TypeScript React | 71 | 0 | 4 | 75 |
| [src/components/organism/Header/index.tsx](/src/components/organism/Header/index.tsx) | TypeScript React | 202 | 0 | 9 | 211 |
| [src/components/organism/Header/mobile.tsx](/src/components/organism/Header/mobile.tsx) | TypeScript React | 52 | 0 | 5 | 57 |
| [src/components/organism/Menu/index.tsx](/src/components/organism/Menu/index.tsx) | TypeScript React | 12 | 0 | 4 | 16 |
| [src/components/organism/TechnicianLayout/index.tsx](/src/components/organism/TechnicianLayout/index.tsx) | TypeScript React | 52 | 45 | 4 | 101 |
| [src/components/organism/TimeTable/DateRow.tsx](/src/components/organism/TimeTable/DateRow.tsx) | TypeScript React | 44 | 0 | 4 | 48 |
| [src/components/organism/TimeTable/index.tsx](/src/components/organism/TimeTable/index.tsx) | TypeScript React | 336 | 4 | 21 | 361 |
| [src/components/tmp.tsx](/src/components/tmp.tsx) | TypeScript React | 0 | 1,089 | 4 | 1,093 |
| [src/context/global.tsx](/src/context/global.tsx) | TypeScript React | 20 | 0 | 6 | 26 |
| [src/functions/api/addon.ts](/src/functions/api/addon.ts) | TypeScript | 58 | 0 | 8 | 66 |
| [src/functions/api/availability.ts](/src/functions/api/availability.ts) | TypeScript | 92 | 0 | 8 | 100 |
| [src/functions/api/booking.ts](/src/functions/api/booking.ts) | TypeScript | 43 | 0 | 4 | 47 |
| [src/functions/api/calendar.ts](/src/functions/api/calendar.ts) | TypeScript | 18 | 0 | 2 | 20 |
| [src/functions/api/car.ts](/src/functions/api/car.ts) | TypeScript | 51 | 0 | 8 | 59 |
| [src/functions/api/request.ts](/src/functions/api/request.ts) | TypeScript | 37 | 0 | 7 | 44 |
| [src/functions/api/service.ts](/src/functions/api/service.ts) | TypeScript | 31 | 0 | 3 | 34 |
| [src/functions/api/stripe.ts](/src/functions/api/stripe.ts) | TypeScript | 7 | 1 | 3 | 11 |
| [src/functions/api/technician.ts](/src/functions/api/technician.ts) | TypeScript | 17 | 0 | 2 | 19 |
| [src/functions/api/user.ts](/src/functions/api/user.ts) | TypeScript | 40 | 1 | 3 | 44 |
| [src/functions/appointment.ts](/src/functions/appointment.ts) | TypeScript | 70 | 8 | 12 | 90 |
| [src/functions/calendar.ts](/src/functions/calendar.ts) | TypeScript | 20 | 0 | 7 | 27 |
| [src/functions/cognito/index.ts](/src/functions/cognito/index.ts) | TypeScript | 102 | 0 | 13 | 115 |
| [src/functions/cognito/pool.ts](/src/functions/cognito/pool.ts) | TypeScript | 7 | 0 | 2 | 9 |
| [src/functions/cognito/util.ts](/src/functions/cognito/util.ts) | TypeScript | 25 | 0 | 4 | 29 |
| [src/functions/local/index.tsx](/src/functions/local/index.tsx) | TypeScript React | 15 | 0 | 3 | 18 |
| [src/functions/util.ts](/src/functions/util.ts) | TypeScript | 23 | 0 | 6 | 29 |
| [src/hooks/useAvailability.ts](/src/hooks/useAvailability.ts) | TypeScript | 118 | 1 | 12 | 131 |
| [src/hooks/useCalendarController.ts](/src/hooks/useCalendarController.ts) | TypeScript | 40 | 0 | 7 | 47 |
| [src/hooks/useCategories.ts](/src/hooks/useCategories.ts) | TypeScript | 19 | 0 | 4 | 23 |
| [src/hooks/useTechnicianSearch.ts](/src/hooks/useTechnicianSearch.ts) | TypeScript | 45 | 1 | 6 | 52 |
| [src/hooks/useTimer.ts](/src/hooks/useTimer.ts) | TypeScript | 25 | 0 | 5 | 30 |
| [src/pageComponents/availability/CarItem.tsx](/src/pageComponents/availability/CarItem.tsx) | TypeScript React | 15 | 0 | 4 | 19 |
| [src/pageComponents/availability/CategoryServiceItem.tsx](/src/pageComponents/availability/CategoryServiceItem.tsx) | TypeScript React | 25 | 0 | 4 | 29 |
| [src/pageComponents/availability/ReviewItem.tsx](/src/pageComponents/availability/ReviewItem.tsx) | TypeScript React | 82 | 0 | 4 | 86 |
| [src/pageComponents/availability/SelectedTechnicianInfo.tsx](/src/pageComponents/availability/SelectedTechnicianInfo.tsx) | TypeScript React | 66 | 0 | 4 | 70 |
| [src/pageComponents/availability/SideMenu.tsx](/src/pageComponents/availability/SideMenu.tsx) | TypeScript React | 132 | 0 | 7 | 139 |
| [src/pageComponents/availability/TechnicianItem.tsx](/src/pageComponents/availability/TechnicianItem.tsx) | TypeScript React | 122 | 0 | 4 | 126 |
| [src/pageComponents/availability/TechnicianList.tsx](/src/pageComponents/availability/TechnicianList.tsx) | TypeScript React | 186 | 0 | 7 | 193 |
| [src/pageComponents/customer_profile/AddressBlock.tsx](/src/pageComponents/customer_profile/AddressBlock.tsx) | TypeScript React | 210 | 0 | 5 | 215 |
| [src/pageComponents/customer_profile/InformationBlock.tsx](/src/pageComponents/customer_profile/InformationBlock.tsx) | TypeScript React | 208 | 0 | 8 | 216 |
| [src/pageComponents/customer_profile/VehicleBlock.tsx](/src/pageComponents/customer_profile/VehicleBlock.tsx) | TypeScript React | 43 | 0 | 5 | 48 |
| [src/pageComponents/customer_profile/VehicleItem.tsx](/src/pageComponents/customer_profile/VehicleItem.tsx) | TypeScript React | 30 | 0 | 4 | 34 |
| [src/pageComponents/information/PaymentBlock.tsx](/src/pageComponents/information/PaymentBlock.tsx) | TypeScript React | 118 | 0 | 8 | 126 |
| [src/pageComponents/information/ServiceItem.tsx](/src/pageComponents/information/ServiceItem.tsx) | TypeScript React | 46 | 1 | 5 | 52 |
| [src/pageComponents/information/TimeBlock.tsx](/src/pageComponents/information/TimeBlock.tsx) | TypeScript React | 23 | 0 | 3 | 26 |
| [src/pageComponents/select_service/AddCarButton.tsx](/src/pageComponents/select_service/AddCarButton.tsx) | TypeScript React | 26 | 0 | 4 | 30 |
| [src/pageComponents/select_service/CarItem.tsx](/src/pageComponents/select_service/CarItem.tsx) | TypeScript React | 72 | 0 | 3 | 75 |
| [src/pageComponents/select_service/CarMenu.tsx](/src/pageComponents/select_service/CarMenu.tsx) | TypeScript React | 38 | 0 | 4 | 42 |
| [src/pageComponents/select_service/MobileServiceItem.tsx](/src/pageComponents/select_service/MobileServiceItem.tsx) | TypeScript React | 55 | 0 | 4 | 59 |
| [src/pageComponents/select_service/ServiceItem.tsx](/src/pageComponents/select_service/ServiceItem.tsx) | TypeScript React | 77 | 0 | 3 | 80 |
| [src/pageComponents/select_service/ServiceTotalViewer.tsx](/src/pageComponents/select_service/ServiceTotalViewer.tsx) | TypeScript React | 89 | 10 | 5 | 104 |
| [src/pageComponents/select_service/Services.tsx](/src/pageComponents/select_service/Services.tsx) | TypeScript React | 109 | 9 | 8 | 126 |
| [src/pageComponents/tech_booking/AvailabilityBlock.tsx](/src/pageComponents/tech_booking/AvailabilityBlock.tsx) | TypeScript React | 34 | 0 | 4 | 38 |
| [src/pageComponents/tech_booking/AvailabilityModal.tsx](/src/pageComponents/tech_booking/AvailabilityModal.tsx) | TypeScript React | 354 | 0 | 19 | 373 |
| [src/pageComponents/tech_booking/AvailabilitySection.tsx](/src/pageComponents/tech_booking/AvailabilitySection.tsx) | TypeScript React | 26 | 12 | 5 | 43 |
| [src/pageComponents/tech_booking/CalendarSection.tsx](/src/pageComponents/tech_booking/CalendarSection.tsx) | TypeScript React | 54 | 0 | 6 | 60 |
| [src/pages/_app.tsx](/src/pages/_app.tsx) | TypeScript React | 15 | 0 | 3 | 18 |
| [src/pages/_document.tsx](/src/pages/_document.tsx) | TypeScript React | 30 | 0 | 4 | 34 |
| [src/pages/account.tsx](/src/pages/account.tsx) | TypeScript React | 82 | 1 | 8 | 91 |
| [src/pages/admin_addons/index.tsx](/src/pages/admin_addons/index.tsx) | TypeScript React | 237 | 0 | 14 | 251 |
| [src/pages/admin_service/index.tsx](/src/pages/admin_service/index.tsx) | TypeScript React | 193 | 2 | 16 | 211 |
| [src/pages/admin_technicians/index.tsx](/src/pages/admin_technicians/index.tsx) | TypeScript React | 212 | 1 | 5 | 218 |
| [src/pages/availability/index.tsx](/src/pages/availability/index.tsx) | TypeScript React | 149 | 0 | 11 | 160 |
| [src/pages/cancel.tsx](/src/pages/cancel.tsx) | TypeScript React | 23 | 0 | 4 | 27 |
| [src/pages/confirm.tsx](/src/pages/confirm.tsx) | TypeScript React | 71 | 0 | 7 | 78 |
| [src/pages/customer_booking.tsx](/src/pages/customer_booking.tsx) | TypeScript React | 194 | 37 | 12 | 243 |
| [src/pages/customer_profile/index.tsx](/src/pages/customer_profile/index.tsx) | TypeScript React | 61 | 0 | 4 | 65 |
| [src/pages/index.tsx](/src/pages/index.tsx) | TypeScript React | 122 | 77 | 14 | 213 |
| [src/pages/information/index.tsx](/src/pages/information/index.tsx) | TypeScript React | 209 | 14 | 13 | 236 |
| [src/pages/login.tsx](/src/pages/login.tsx) | TypeScript React | 122 | 0 | 9 | 131 |
| [src/pages/new_tech.tsx](/src/pages/new_tech.tsx) | TypeScript React | 119 | 0 | 7 | 126 |
| [src/pages/new_tech_confirm.tsx](/src/pages/new_tech_confirm.tsx) | TypeScript React | 79 | 0 | 4 | 83 |
| [src/pages/new_tech_info.tsx](/src/pages/new_tech_info.tsx) | TypeScript React | 221 | 0 | 14 | 235 |
| [src/pages/rating.tsx](/src/pages/rating.tsx) | TypeScript React | 174 | 0 | 10 | 184 |
| [src/pages/signup.tsx](/src/pages/signup.tsx) | TypeScript React | 166 | 0 | 7 | 173 |
| [src/pages/success.tsx](/src/pages/success.tsx) | TypeScript React | 29 | 0 | 5 | 34 |
| [src/pages/tech_booking/index.tsx](/src/pages/tech_booking/index.tsx) | TypeScript React | 189 | 9 | 15 | 213 |
| [src/pages/tech_earning.tsx](/src/pages/tech_earning.tsx) | TypeScript React | 71 | 0 | 3 | 74 |
| [src/pages/tech_setting.tsx](/src/pages/tech_setting.tsx) | TypeScript React | 9 | 0 | 3 | 12 |
| [src/pages/technicians.tsx](/src/pages/technicians.tsx) | TypeScript React | 91 | 0 | 6 | 97 |
| [src/pages/test.tsx](/src/pages/test.tsx) | TypeScript React | 26 | 0 | 5 | 31 |
| [src/pages/test_buttons.tsx](/src/pages/test_buttons.tsx) | TypeScript React | 26 | 0 | 5 | 31 |
| [src/pages/test_texts.tsx](/src/pages/test_texts.tsx) | TypeScript React | 20 | 0 | 6 | 26 |
| [src/templateComponents/Description.tsx](/src/templateComponents/Description.tsx) | TypeScript React | 11 | 0 | 4 | 15 |
| [src/theme/index.ts](/src/theme/index.ts) | TypeScript | 11 | 0 | 3 | 14 |
| [src/types/addon.ts](/src/types/addon.ts) | TypeScript | 12 | 0 | 4 | 16 |
| [src/types/calendarTypeBooking.ts](/src/types/calendarTypeBooking.ts) | TypeScript | 26 | 0 | 4 | 30 |
| [src/types/car.ts](/src/types/car.ts) | TypeScript | 31 | 0 | 8 | 39 |
| [src/types/category.ts](/src/types/category.ts) | TypeScript | 71 | 1 | 8 | 80 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 90 | 0 | 8 | 98 |
| [src/types/interfaces.ts](/src/types/interfaces.ts) | TypeScript | 15 | 0 | 2 | 17 |
| [src/types/user.ts](/src/types/user.ts) | TypeScript | 48 | 0 | 13 | 61 |
| [src/values/index.ts](/src/values/index.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [styles/Home.module.css](/styles/Home.module.css) | CSS | 100 | 0 | 17 | 117 |
| [styles/globals.css](/styles/globals.css) | CSS | 68 | 0 | 14 | 82 |
| [tailwind.config.js](/tailwind.config.js) | JavaScript | 16 | 0 | 2 | 18 |
| [tsconfig.json](/tsconfig.json) | JSON with Comments | 20 | 0 | 1 | 21 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)