Date : 2022-11-09 22:31:05
Directory : c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web
Total : 159 files,  9736 codes, 1403 comments, 897 blanks, all 12036 lines

Languages
+--------------------+------------+------------+------------+------------+------------+
| language           | files      | code       | comment    | blank      | total      |
+--------------------+------------+------------+------------+------------+------------+
| TypeScript React   |        108 |      7,633 |      1,381 |        612 |      9,626 |
| TypeScript         |         40 |      1,764 |         21 |        230 |      2,015 |
| CSS                |          2 |        168 |          0 |         31 |        199 |
| JSON               |          3 |         95 |          0 |          3 |         98 |
| JavaScript         |          3 |         32 |          1 |          5 |         38 |
| JSON with Comments |          1 |         20 |          0 |          1 |         21 |
| Markdown           |          1 |         20 |          0 |         15 |         35 |
| XML                |          1 |          4 |          0 |          0 |          4 |
+--------------------+------------+------------+------------+------------+------------+

Directories
+--------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                               | files      | code       | comment    | blank      | total      |
+--------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                                  |        159 |      9,736 |      1,403 |        897 |     12,036 |
| public                                                                                                             |          1 |          4 |          0 |          0 |          4 |
| src                                                                                                                |        147 |      9,397 |      1,398 |        840 |     11,635 |
| src\@redux                                                                                                         |          9 |        571 |          4 |         52 |        627 |
| src\@redux\modules                                                                                                 |          7 |        546 |          4 |         48 |        598 |
| src\@redux\modules\appointment                                                                                     |          3 |        338 |          4 |         27 |        369 |
| src\@redux\modules\auth                                                                                            |          2 |        143 |          0 |         13 |        156 |
| src\@redux\modules\techBooking                                                                                     |          2 |         65 |          0 |          8 |         73 |
| src\components                                                                                                     |         53 |      2,407 |      1,208 |        248 |      3,863 |
| src\components\Layout                                                                                              |          1 |        189 |         25 |          9 |        223 |
| src\components\Modal                                                                                               |          2 |         70 |          0 |          7 |         77 |
| src\components\Stepper                                                                                             |          1 |        103 |          0 |          5 |        108 |
| src\components\atom                                                                                                |         33 |        736 |          0 |        132 |        868 |
| src\components\atom\AuthInput                                                                                      |          1 |         19 |          0 |          4 |         23 |
| src\components\atom\Buttons                                                                                        |          9 |        257 |          0 |         36 |        293 |
| src\components\atom\Buttons\AuthButton                                                                             |          1 |         38 |          0 |          4 |         42 |
| src\components\atom\Buttons\BackgroundSmallButton                                                                  |          1 |         33 |          0 |          5 |         38 |
| src\components\atom\Buttons\BorderSmallButton                                                                      |          1 |         22 |          0 |          4 |         26 |
| src\components\atom\Buttons\base                                                                                   |          3 |         89 |          0 |         11 |        100 |
| src\components\atom\Texts                                                                                          |         16 |        260 |          0 |         60 |        320 |
| src\components\atom\Texts\GrayParagraph                                                                            |          1 |         18 |          0 |          4 |         22 |
| src\components\atom\Texts\InstructionText                                                                          |          1 |         22 |          0 |          4 |         26 |
| src\components\atom\Texts\MenuSectionTitle                                                                         |          1 |         16 |          0 |          4 |         20 |
| src\components\atom\Texts\ModalTitle                                                                               |          1 |         16 |          0 |          4 |         20 |
| src\components\atom\Texts\ProfileDescription                                                                       |          1 |         16 |          0 |          3 |         19 |
| src\components\atom\Texts\ProfileFieldTitle                                                                        |          1 |         17 |          0 |          4 |         21 |
| src\components\atom\Texts\ProfileFieldValue                                                                        |          1 |         14 |          0 |          3 |         17 |
| src\components\atom\Texts\ProfileTitle                                                                             |          1 |         11 |          0 |          4 |         15 |
| src\components\atom\Texts\base                                                                                     |          7 |        112 |          0 |         26 |        138 |
| src\components\molecule                                                                                            |          4 |        122 |          0 |         16 |        138 |
| src\components\molecule\AdminSubHeader                                                                             |          1 |         23 |          0 |          4 |         27 |
| src\components\molecule\CalendarHeader                                                                             |          1 |         52 |          0 |          4 |         56 |
| src\components\molecule\MobileStepper                                                                              |          1 |         15 |          0 |          4 |         19 |
| src\components\organism                                                                                            |         11 |      1,187 |         94 |         75 |      1,356 |
| src\components\organism\AddCarModal                                                                                |          1 |        253 |         15 |         13 |        281 |
| src\components\organism\AdminLayout                                                                                |          1 |         63 |          0 |          4 |         67 |
| src\components\organism\AdminSideModal                                                                             |          1 |         19 |          0 |          4 |         23 |
| src\components\organism\BookingInfoModal                                                                           |          1 |         83 |         30 |          3 |        116 |
| src\components\organism\CustomerLayout                                                                             |          1 |         71 |          0 |          4 |         75 |
| src\components\organism\Header                                                                                     |          2 |        254 |          0 |         14 |        268 |
| src\components\organism\Menu                                                                                       |          1 |         12 |          0 |          4 |         16 |
| src\components\organism\TechnicianLayout                                                                           |          1 |         52 |         45 |          4 |        101 |
| src\components\organism\TimeTable                                                                                  |          2 |        380 |          4 |         25 |        409 |
| src\context                                                                                                        |          1 |         20 |          0 |          6 |         26 |
| src\functions                                                                                                      |         17 |        656 |         10 |         95 |        761 |
| src\functions\api                                                                                                  |         10 |        394 |          2 |         48 |        444 |
| src\functions\cognito                                                                                              |          3 |        134 |          0 |         19 |        153 |
| src\functions\local                                                                                                |          1 |         15 |          0 |          3 |         18 |
| src\hooks                                                                                                          |          5 |        247 |          2 |         34 |        283 |
| src\pageComponents                                                                                                 |         25 |      2,240 |         32 |        137 |      2,409 |
| src\pageComponents\availability                                                                                    |          7 |        628 |          0 |         34 |        662 |
| src\pageComponents\customer_profile                                                                                |          4 |        491 |          0 |         22 |        513 |
| src\pageComponents\information                                                                                     |          3 |        187 |          1 |         16 |        204 |
| src\pageComponents\select_service                                                                                  |          7 |        466 |         19 |         31 |        516 |
| src\pageComponents\tech_booking                                                                                    |          4 |        468 |         12 |         34 |        514 |
| src\pages                                                                                                          |         27 |      2,940 |        141 |        214 |      3,295 |
| src\pages\admin_addons                                                                                             |          1 |        237 |          0 |         14 |        251 |
| src\pages\admin_service                                                                                            |          1 |        193 |          2 |         16 |        211 |
| src\pages\admin_technicians                                                                                        |          1 |        212 |          1 |          5 |        218 |
| src\pages\availability                                                                                             |          1 |        149 |          0 |         11 |        160 |
| src\pages\customer_profile                                                                                         |          1 |         61 |          0 |          4 |         65 |
| src\pages\information                                                                                              |          1 |        209 |         14 |         13 |        236 |
| src\pages\tech_booking                                                                                             |          1 |        189 |          9 |         15 |        213 |
| src\templateComponents                                                                                             |          1 |         11 |          0 |          4 |         15 |
| src\theme                                                                                                          |          1 |         11 |          0 |          3 |         14 |
| src\types                                                                                                          |          7 |        293 |          1 |         47 |        341 |
| src\values                                                                                                         |          1 |          1 |          0 |          0 |          1 |
| styles                                                                                                             |          2 |        168 |          0 |         31 |        199 |
+--------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+--------------------------------------------------------------------------------------------------------------------+--------------------+------------+------------+------------+------------+
| filename                                                                                                           | language           | code       | comment    | blank      | total      |
+--------------------------------------------------------------------------------------------------------------------+--------------------+------------+------------+------------+------------+
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.eslintrc.json                                              | JSON               |          3 |          0 |          1 |          4 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.prettierrc                                                 | JSON               |          5 |          0 |          1 |          6 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\README.md                                                   | Markdown           |         20 |          0 |         15 |         35 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\next-env.d.ts                                               | TypeScript         |          0 |          4 |          2 |          6 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\next.config.js                                              | JavaScript         |         10 |          1 |          2 |         13 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\package.json                                                | JSON               |         87 |          0 |          1 |         88 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\postcss.config.js                                           | JavaScript         |          6 |          0 |          1 |          7 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\public\vercel.svg                                           | XML                |          4 |          0 |          0 |          4 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\hook.ts                                          | TypeScript         |          4 |          0 |          1 |          5 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\index.ts                                         | TypeScript         |         21 |          0 |          3 |         24 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\function.ts                  | TypeScript         |         60 |          2 |          8 |         70 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\index.ts                     | TypeScript         |        262 |          2 |         17 |        281 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\thunk.ts                     | TypeScript         |         16 |          0 |          2 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\index.ts                            | TypeScript         |         91 |          0 |          6 |         97 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\thunks.ts                           | TypeScript         |         52 |          0 |          7 |         59 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\techBooking\index.ts                     | TypeScript         |         57 |          0 |          6 |         63 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\techBooking\thunk.ts                     | TypeScript         |          8 |          0 |          2 |         10 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Layout\index.tsx                             | TypeScript React   |        189 |         25 |          9 |        223 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\ConfirmCancelModal.tsx                 | TypeScript React   |         60 |          0 |          4 |         64 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\index.tsx                              | TypeScript React   |         10 |          0 |          3 |         13 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Stepper\index.tsx                            | TypeScript React   |        103 |          0 |          5 |        108 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\AuthInput\index.tsx                     | TypeScript React   |         19 |          0 |          4 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\BorderSelect.tsx                        | TypeScript React   |         20 |          0 |          3 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AccentTextButton.tsx            | TypeScript React   |         18 |          0 |          4 |         22 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AuthButton\index.tsx            | TypeScript React   |         38 |          0 |          4 |         42 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundMiddleButton.tsx      | TypeScript React   |         33 |          0 |          4 |         37 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundSmallButton\index.tsx | TypeScript React   |         33 |          0 |          5 |         38 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BorderSmallButton\index.tsx     | TypeScript React   |         22 |          0 |          4 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\MenuButton.tsx                  | TypeScript React   |         24 |          0 |          4 |         28 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\MiddleButton.tsx           | TypeScript React   |         37 |          0 |          4 |         41 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\SmallButton.tsx            | TypeScript React   |         32 |          0 |          4 |         36 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\TextButton.tsx             | TypeScript React   |         20 |          0 |          3 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ImageFileInput.tsx                      | TypeScript React   |         40 |          0 |          8 |         48 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\NoPaddingPaper.tsx                      | TypeScript React   |         19 |          0 |          3 |         22 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Paper.tsx                               | TypeScript React   |         20 |          0 |          4 |         24 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ProfilePlaceholder.tsx                  | TypeScript React   |         28 |          0 |          5 |         33 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TextInput.tsx                           | TypeScript React   |         41 |          0 |          4 |         45 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayParagraph\index.tsx           | TypeScript React   |         18 |          0 |          4 |         22 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayText14.tsx                    | TypeScript React   |         18 |          0 |          4 |         22 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\InstructionText\index.tsx         | TypeScript React   |         22 |          0 |          4 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\MenuSectionTitle\index.tsx        | TypeScript React   |         16 |          0 |          4 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ModalTitle\index.tsx              | TypeScript React   |         16 |          0 |          4 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileDescription\index.tsx      | TypeScript React   |         16 |          0 |          3 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileFieldTitle\index.tsx       | TypeScript React   |         17 |          0 |          4 |         21 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileFieldValue\index.tsx       | TypeScript React   |         14 |          0 |          3 |         17 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileTitle\index.tsx            | TypeScript React   |         11 |          0 |          4 |         15 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Paragraph14.tsx              | TypeScript React   |         15 |          0 |          4 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text12.tsx                   | TypeScript React   |         15 |          0 |          4 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text14.tsx                   | TypeScript React   |         15 |          0 |          4 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text16.tsx                   | TypeScript React   |         18 |          0 |          4 |         22 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title18.tsx                  | TypeScript React   |         15 |          0 |          3 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title24.tsx                  | TypeScript React   |         15 |          0 |          3 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title32.tsx                  | TypeScript React   |         19 |          0 |          4 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TimeSelect.tsx                          | TypeScript React   |         32 |          0 |          5 |         37 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\AdminSubHeader\index.tsx            | TypeScript React   |         23 |          0 |          4 |         27 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\CalendarHeader\index.tsx            | TypeScript React   |         52 |          0 |          4 |         56 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\MobileStepper\index.tsx             | TypeScript React   |         15 |          0 |          4 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ModalConfirmCancelBar.tsx           | TypeScript React   |         32 |          0 |          4 |         36 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddCarModal\index.tsx               | TypeScript React   |        253 |         15 |         13 |        281 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AdminLayout\index.tsx               | TypeScript React   |         63 |          0 |          4 |         67 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AdminSideModal\index.tsx            | TypeScript React   |         19 |          0 |          4 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\BookingInfoModal\index.tsx          | TypeScript React   |         83 |         30 |          3 |        116 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\CustomerLayout\index.tsx            | TypeScript React   |         71 |          0 |          4 |         75 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\index.tsx                    | TypeScript React   |        202 |          0 |          9 |        211 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\mobile.tsx                   | TypeScript React   |         52 |          0 |          5 |         57 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Menu\index.tsx                      | TypeScript React   |         12 |          0 |          4 |         16 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TechnicianLayout\index.tsx          | TypeScript React   |         52 |         45 |          4 |        101 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TimeTable\DateRow.tsx               | TypeScript React   |         44 |          0 |          4 |         48 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TimeTable\index.tsx                 | TypeScript React   |        336 |          4 |         21 |        361 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\tmp.tsx                                      | TypeScript React   |          0 |      1,089 |          4 |      1,093 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\context\global.tsx                                      | TypeScript React   |         20 |          0 |          6 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\addon.ts                                  | TypeScript         |         58 |          0 |          8 |         66 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\availability.ts                           | TypeScript         |         92 |          0 |          8 |        100 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\booking.ts                                | TypeScript         |         43 |          0 |          4 |         47 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\calendar.ts                               | TypeScript         |         18 |          0 |          2 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\car.ts                                    | TypeScript         |         51 |          0 |          8 |         59 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\request.ts                                | TypeScript         |         37 |          0 |          7 |         44 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\service.ts                                | TypeScript         |         31 |          0 |          3 |         34 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\stripe.ts                                 | TypeScript         |          7 |          1 |          3 |         11 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\technician.ts                             | TypeScript         |         17 |          0 |          2 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\user.ts                                   | TypeScript         |         40 |          1 |          3 |         44 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\appointment.ts                                | TypeScript         |         70 |          8 |         12 |         90 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\calendar.ts                                   | TypeScript         |         20 |          0 |          7 |         27 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\index.ts                              | TypeScript         |        102 |          0 |         13 |        115 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\pool.ts                               | TypeScript         |          7 |          0 |          2 |          9 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\util.ts                               | TypeScript         |         25 |          0 |          4 |         29 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\local\index.tsx                               | TypeScript React   |         15 |          0 |          3 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\util.ts                                       | TypeScript         |         23 |          0 |          6 |         29 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAvailability.ts                                | TypeScript         |        118 |          1 |         12 |        131 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCalendarController.ts                          | TypeScript         |         40 |          0 |          7 |         47 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCategories.ts                                  | TypeScript         |         19 |          0 |          4 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianSearch.ts                            | TypeScript         |         45 |          1 |          6 |         52 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTimer.ts                                       | TypeScript         |         25 |          0 |          5 |         30 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CarItem.tsx                 | TypeScript React   |         15 |          0 |          4 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CategoryServiceItem.tsx     | TypeScript React   |         25 |          0 |          4 |         29 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\ReviewItem.tsx              | TypeScript React   |         82 |          0 |          4 |         86 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SelectedTechnicianInfo.tsx  | TypeScript React   |         66 |          0 |          4 |         70 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SideMenu.tsx                | TypeScript React   |        132 |          0 |          7 |        139 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianItem.tsx          | TypeScript React   |        122 |          0 |          4 |        126 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianList.tsx          | TypeScript React   |        186 |          0 |          7 |        193 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\AddressBlock.tsx        | TypeScript React   |        210 |          0 |          5 |        215 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\InformationBlock.tsx    | TypeScript React   |        208 |          0 |          8 |        216 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleBlock.tsx        | TypeScript React   |         43 |          0 |          5 |         48 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleItem.tsx         | TypeScript React   |         30 |          0 |          4 |         34 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\PaymentBlock.tsx             | TypeScript React   |        118 |          0 |          8 |        126 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\ServiceItem.tsx              | TypeScript React   |         46 |          1 |          5 |         52 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\TimeBlock.tsx                | TypeScript React   |         23 |          0 |          3 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\AddCarButton.tsx          | TypeScript React   |         26 |          0 |          4 |         30 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarItem.tsx               | TypeScript React   |         72 |          0 |          3 |         75 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarMenu.tsx               | TypeScript React   |         38 |          0 |          4 |         42 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\MobileServiceItem.tsx     | TypeScript React   |         55 |          0 |          4 |         59 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceItem.tsx           | TypeScript React   |         77 |          0 |          3 |         80 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceTotalViewer.tsx    | TypeScript React   |         89 |         10 |          5 |        104 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\Services.tsx              | TypeScript React   |        109 |          9 |          8 |        126 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityBlock.tsx       | TypeScript React   |         34 |          0 |          4 |         38 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityModal.tsx       | TypeScript React   |        354 |          0 |         19 |        373 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilitySection.tsx     | TypeScript React   |         26 |         12 |          5 |         43 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\CalendarSection.tsx         | TypeScript React   |         54 |          0 |          6 |         60 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_app.tsx                                          | TypeScript React   |         15 |          0 |          3 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_document.tsx                                     | TypeScript React   |         30 |          0 |          4 |         34 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\account.tsx                                       | TypeScript React   |         82 |          1 |          8 |         91 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_addons\index.tsx                            | TypeScript React   |        237 |          0 |         14 |        251 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_service\index.tsx                           | TypeScript React   |        193 |          2 |         16 |        211 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_technicians\index.tsx                       | TypeScript React   |        212 |          1 |          5 |        218 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\availability\index.tsx                            | TypeScript React   |        149 |          0 |         11 |        160 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\cancel.tsx                                        | TypeScript React   |         23 |          0 |          4 |         27 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\confirm.tsx                                       | TypeScript React   |         71 |          0 |          7 |         78 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_booking.tsx                              | TypeScript React   |        194 |         37 |         12 |        243 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_profile\index.tsx                        | TypeScript React   |         61 |          0 |          4 |         65 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\index.tsx                                         | TypeScript React   |        122 |         77 |         14 |        213 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\information\index.tsx                             | TypeScript React   |        209 |         14 |         13 |        236 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\login.tsx                                         | TypeScript React   |        122 |          0 |          9 |        131 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech.tsx                                      | TypeScript React   |        119 |          0 |          7 |        126 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech_confirm.tsx                              | TypeScript React   |         79 |          0 |          4 |         83 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech_info.tsx                                 | TypeScript React   |        221 |          0 |         14 |        235 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\rating.tsx                                        | TypeScript React   |        174 |          0 |         10 |        184 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\signup.tsx                                        | TypeScript React   |        166 |          0 |          7 |        173 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\success.tsx                                       | TypeScript React   |         29 |          0 |          5 |         34 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_booking\index.tsx                            | TypeScript React   |        189 |          9 |         15 |        213 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_earning.tsx                                  | TypeScript React   |         71 |          0 |          3 |         74 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_setting.tsx                                  | TypeScript React   |          9 |          0 |          3 |         12 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\technicians.tsx                                   | TypeScript React   |         91 |          0 |          6 |         97 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test.tsx                                          | TypeScript React   |         26 |          0 |          5 |         31 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_buttons.tsx                                  | TypeScript React   |         26 |          0 |          5 |         31 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_texts.tsx                                    | TypeScript React   |         20 |          0 |          6 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\templateComponents\Description.tsx                      | TypeScript React   |         11 |          0 |          4 |         15 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\theme\index.ts                                          | TypeScript         |         11 |          0 |          3 |         14 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\addon.ts                                          | TypeScript         |         12 |          0 |          4 |         16 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\calendarTypeBooking.ts                            | TypeScript         |         26 |          0 |          4 |         30 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\car.ts                                            | TypeScript         |         31 |          0 |          8 |         39 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\category.ts                                       | TypeScript         |         71 |          1 |          8 |         80 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\index.ts                                          | TypeScript         |         90 |          0 |          8 |         98 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\interfaces.ts                                     | TypeScript         |         15 |          0 |          2 |         17 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\user.ts                                           | TypeScript         |         48 |          0 |         13 |         61 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\values\index.ts                                         | TypeScript         |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\Home.module.css                                      | CSS                |        100 |          0 |         17 |        117 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\globals.css                                          | CSS                |         68 |          0 |         14 |         82 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tailwind.config.js                                          | JavaScript         |         16 |          0 |          2 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tsconfig.json                                               | JSON with Comments |         20 |          0 |          1 |         21 |
| Total                                                                                                              |                    |      9,736 |      1,403 |        897 |     12,036 |
+--------------------------------------------------------------------------------------------------------------------+--------------------+------------+------------+------------+------------+