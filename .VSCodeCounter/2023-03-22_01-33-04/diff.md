# Diff Summary

Date : 2023-03-22 01:33:04

Directory c:\\Users\\<USER>\\Desktop\\Desktop\\desk\\luxe\\wheeleasy_web

Total : 173 files,  41324 codes, 75 comments, 447 blanks, all 41846 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| JSON | 9 | 36,438 | 0 | 1 | 36,439 |
| TypeScript JSX | 92 | 5,142 | 9 | 388 | 5,539 |
| TypeScript | 59 | 1,091 | 108 | 151 | 1,350 |
| CSS | 1 | 91 | 0 | 8 | 99 |
| JavaScript | 1 | 10 | 0 | 0 | 10 |
| XML | 1 | 1 | 0 | 0 | 1 |
| TypeScript React | 10 | -1,449 | -42 | -101 | -1,592 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 173 | 41,324 | 75 | 447 | 41,846 |
| . (Files) | 4 | 36,161 | 0 | 1 | 36,162 |
| amplify | 6 | 168 | 0 | 0 | 168 |
| amplify (Files) | 2 | 87 | 0 | 0 | 87 |
| amplify\\backend | 4 | 81 | 0 | 0 | 81 |
| amplify\\backend (Files) | 2 | 55 | 0 | 0 | 55 |
| amplify\\backend\\auth | 1 | 25 | 0 | 0 | 25 |
| amplify\\backend\\auth\\wheeleasyaa553eff | 1 | 25 | 0 | 0 | 25 |
| amplify\\backend\\types | 1 | 1 | 0 | 0 | 1 |
| public | 1 | 1 | 0 | 0 | 1 |
| src | 161 | 4,903 | 75 | 438 | 5,416 |
| src (Files) | 1 | 1 | 0 | 0 | 1 |
| src\\@redux | 9 | -235 | -3 | -11 | -249 |
| src\\@redux (Files) | 1 | 2 | 0 | 0 | 2 |
| src\\@redux\\modules | 8 | -237 | -3 | -11 | -251 |
| src\\@redux\\modules\\appointment | 3 | -338 | -4 | -27 | -369 |
| src\\@redux\\modules\\auth | 2 | -33 | 0 | -2 | -35 |
| src\\@redux\\modules\\categoryBooking | 2 | 64 | 1 | 8 | 73 |
| src\\@redux\\modules\\summary | 1 | 70 | 0 | 10 | 80 |
| src\\components | 50 | 2,175 | -13 | 155 | 2,317 |
| src\\components\\Layout | 1 | 68 | -23 | 3 | 48 |
| src\\components\\Modal | 1 | 12 | 0 | 0 | 12 |
| src\\components\\Stepper | 1 | -2 | 0 | 0 | -2 |
| src\\components\\VerticalStepper | 1 | 133 | 0 | 8 | 141 |
| src\\components\\atom | 25 | 1,096 | 0 | 83 | 1,179 |
| src\\components\\atom (Files) | 10 | 733 | 0 | 56 | 789 |
| src\\components\\atom\\AuthInput | 1 | 12 | 0 | 1 | 13 |
| src\\components\\atom\\Buttons | 7 | 95 | 0 | 10 | 105 |
| src\\components\\atom\\Buttons (Files) | 1 | 18 | 0 | 2 | 20 |
| src\\components\\atom\\Buttons\\AuthButton | 1 | 0 | 0 | 1 | 1 |
| src\\components\\atom\\Buttons\\BackgroundSmallButton | 1 | 4 | 0 | 0 | 4 |
| src\\components\\atom\\Buttons\\BorderButton | 1 | 22 | 0 | 3 | 25 |
| src\\components\\atom\\Buttons\\BorderSmallButton | 1 | 2 | 0 | 0 | 2 |
| src\\components\\atom\\Buttons\\GoogleAuthButton | 1 | 52 | 0 | 4 | 56 |
| src\\components\\atom\\Buttons\\base | 1 | -3 | 0 | 0 | -3 |
| src\\components\\atom\\SizeableImage | 1 | 18 | 0 | 4 | 22 |
| src\\components\\atom\\Testimonials | 3 | 244 | 0 | 12 | 256 |
| src\\components\\atom\\Texts | 3 | -6 | 0 | 0 | -6 |
| src\\components\\atom\\Texts\\GrayParagraph | 1 | -2 | 0 | 0 | -2 |
| src\\components\\atom\\Texts\\MenuSectionTitle | 1 | -8 | 0 | 0 | -8 |
| src\\components\\atom\\Texts\\base | 1 | 4 | 0 | 0 | 4 |
| src\\components\\molecule | 5 | 164 | 0 | 16 | 180 |
| src\\components\\molecule (Files) | 4 | 136 | 0 | 12 | 148 |
| src\\components\\molecule\\ProfileBlockHeader | 1 | 28 | 0 | 4 | 32 |
| src\\components\\organism | 16 | 704 | 10 | 45 | 759 |
| src\\components\\organism (Files) | 5 | 860 | 26 | 46 | 932 |
| src\\components\\organism\\AddCarModal | 1 | -253 | -15 | -13 | -281 |
| src\\components\\organism\\CustomerLayout | 1 | 50 | 0 | 0 | 50 |
| src\\components\\organism\\Header | 2 | -14 | 15 | 0 | 1 |
| src\\components\\organism\\Menu | 1 | 3 | 0 | 0 | 3 |
| src\\components\\organism\\SelectCarModal | 1 | 225 | 2 | 16 | 243 |
| src\\components\\organism\\TechnicianLayout | 1 | 24 | -14 | 1 | 11 |
| src\\components\\organism\\TimeTable | 2 | -380 | -4 | -25 | -409 |
| src\\components\\organism\\VehicleAddonEditModal | 1 | 146 | 0 | 15 | 161 |
| src\\components\\organism\\VehicleDeleteModal | 1 | 43 | 0 | 5 | 48 |
| src\\context | 1 | -20 | 0 | -6 | -26 |
| src\\functions | 7 | -128 | -8 | -16 | -152 |
| src\\functions (Files) | 2 | -85 | -8 | -16 | -109 |
| src\\functions\\api | 5 | -43 | 0 | 0 | -43 |
| src\\hooks | 15 | 622 | 111 | 117 | 850 |
| src\\hooks (Files) | 11 | 302 | 103 | 67 | 472 |
| src\\hooks\\useCarServices | 2 | 147 | 0 | 23 | 170 |
| src\\hooks\\useTechnicianBooking | 2 | 173 | 8 | 27 | 208 |
| src\\pageComponents | 32 | 1,200 | -15 | 122 | 1,307 |
| src\\pageComponents\\availability | 6 | 66 | 1 | 16 | 83 |
| src\\pageComponents\\customer_profile | 5 | 58 | 0 | 16 | 74 |
| src\\pageComponents\\information | 4 | 424 | 0 | 39 | 463 |
| src\\pageComponents\\select_service | 8 | 358 | -19 | 20 | 359 |
| src\\pageComponents\\tech_booking | 5 | -23 | 0 | 1 | -22 |
| src\\pageComponents\\technician_profile | 4 | 317 | 3 | 30 | 350 |
| src\\pages | 20 | 458 | -5 | 16 | 469 |
| src\\pages (Files) | 15 | 823 | 17 | 48 | 888 |
| src\\pages\\availability | 1 | -149 | 0 | -11 | -160 |
| src\\pages\\change_password | 1 | 151 | 1 | 6 | 158 |
| src\\pages\\customer_profile | 1 | 31 | 0 | 1 | 32 |
| src\\pages\\information | 1 | -209 | -14 | -13 | -236 |
| src\\pages\\tech_booking | 1 | -189 | -9 | -15 | -213 |
| src\\services | 6 | 358 | 0 | 38 | 396 |
| src\\theme | 1 | -9 | 0 | -3 | -12 |
| src\\types | 18 | 430 | 8 | 23 | 461 |
| src\\values | 1 | 51 | 0 | 3 | 54 |
| styles | 1 | 91 | 0 | 8 | 99 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)