# Summary

Date : 2023-03-22 01:33:04

Directory c:\\Users\\<USER>\\Desktop\\Desktop\\desk\\luxe\\wheeleasy_web

Total : 226 files,  51060 codes, 389 comments, 1340 blanks, all 52789 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| JSON | 10 | 36,533 | 0 | 4 | 36,537 |
| TypeScript JSX | 142 | 11,326 | 259 | 895 | 12,480 |
| TypeScript | 65 | 2,855 | 129 | 381 | 3,365 |
| CSS | 2 | 259 | 0 | 39 | 298 |
| JavaScript | 3 | 42 | 1 | 5 | 48 |
| JSON with Comments | 1 | 20 | 0 | 1 | 21 |
| Markdown | 1 | 20 | 0 | 15 | 35 |
| XML | 2 | 5 | 0 | 0 | 5 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 226 | 51,060 | 389 | 1,340 | 52,789 |
| . (Files) | 10 | 36,328 | 5 | 27 | 36,360 |
| amplify | 6 | 168 | 0 | 0 | 168 |
| amplify (Files) | 2 | 87 | 0 | 0 | 87 |
| amplify\\backend | 4 | 81 | 0 | 0 | 81 |
| amplify\\backend (Files) | 2 | 55 | 0 | 0 | 55 |
| amplify\\backend\\auth | 1 | 25 | 0 | 0 | 25 |
| amplify\\backend\\auth\\wheeleasyaa553eff | 1 | 25 | 0 | 0 | 25 |
| amplify\\backend\\types | 1 | 1 | 0 | 0 | 1 |
| public | 2 | 5 | 0 | 0 | 5 |
| src | 206 | 14,300 | 384 | 1,274 | 15,958 |
| src (Files) | 1 | 1 | 0 | 0 | 1 |
| src\\@redux | 9 | 336 | 1 | 41 | 378 |
| src\\@redux (Files) | 2 | 27 | 0 | 4 | 31 |
| src\\@redux\\modules | 7 | 309 | 1 | 37 | 347 |
| src\\@redux\\modules\\auth | 2 | 110 | 0 | 11 | 121 |
| src\\@redux\\modules\\categoryBooking | 2 | 64 | 1 | 8 | 73 |
| src\\@redux\\modules\\summary | 1 | 70 | 0 | 10 | 80 |
| src\\@redux\\modules\\techBooking | 2 | 65 | 0 | 8 | 73 |
| src\\components | 76 | 4,582 | 106 | 399 | 5,087 |
| src\\components\\Layout | 1 | 257 | 2 | 12 | 271 |
| src\\components\\Modal | 2 | 82 | 0 | 7 | 89 |
| src\\components\\Stepper | 1 | 101 | 0 | 5 | 106 |
| src\\components\\VerticalStepper | 1 | 133 | 0 | 8 | 141 |
| src\\components\\atom | 47 | 1,832 | 0 | 215 | 2,047 |
| src\\components\\atom (Files) | 15 | 933 | 0 | 88 | 1,021 |
| src\\components\\atom\\AuthInput | 1 | 31 | 0 | 5 | 36 |
| src\\components\\atom\\Buttons | 11 | 352 | 0 | 46 | 398 |
| src\\components\\atom\\Buttons (Files) | 3 | 93 | 0 | 14 | 107 |
| src\\components\\atom\\Buttons\\AuthButton | 1 | 38 | 0 | 5 | 43 |
| src\\components\\atom\\Buttons\\BackgroundSmallButton | 1 | 37 | 0 | 5 | 42 |
| src\\components\\atom\\Buttons\\BorderButton | 1 | 22 | 0 | 3 | 25 |
| src\\components\\atom\\Buttons\\BorderSmallButton | 1 | 24 | 0 | 4 | 28 |
| src\\components\\atom\\Buttons\\GoogleAuthButton | 1 | 52 | 0 | 4 | 56 |
| src\\components\\atom\\Buttons\\base | 3 | 86 | 0 | 11 | 97 |
| src\\components\\atom\\SizeableImage | 1 | 18 | 0 | 4 | 22 |
| src\\components\\atom\\Testimonials | 3 | 244 | 0 | 12 | 256 |
| src\\components\\atom\\Texts | 16 | 254 | 0 | 60 | 314 |
| src\\components\\atom\\Texts (Files) | 1 | 18 | 0 | 4 | 22 |
| src\\components\\atom\\Texts\\GrayParagraph | 1 | 16 | 0 | 4 | 20 |
| src\\components\\atom\\Texts\\InstructionText | 1 | 22 | 0 | 4 | 26 |
| src\\components\\atom\\Texts\\MenuSectionTitle | 1 | 8 | 0 | 4 | 12 |
| src\\components\\atom\\Texts\\ModalTitle | 1 | 16 | 0 | 4 | 20 |
| src\\components\\atom\\Texts\\ProfileDescription | 1 | 16 | 0 | 3 | 19 |
| src\\components\\atom\\Texts\\ProfileFieldTitle | 1 | 17 | 0 | 4 | 21 |
| src\\components\\atom\\Texts\\ProfileFieldValue | 1 | 14 | 0 | 3 | 17 |
| src\\components\\atom\\Texts\\ProfileTitle | 1 | 11 | 0 | 4 | 15 |
| src\\components\\atom\\Texts\\base | 7 | 116 | 0 | 26 | 142 |
| src\\components\\molecule | 8 | 286 | 0 | 32 | 318 |
| src\\components\\molecule (Files) | 4 | 168 | 0 | 16 | 184 |
| src\\components\\molecule\\AdminSubHeader | 1 | 23 | 0 | 4 | 27 |
| src\\components\\molecule\\CalendarHeader | 1 | 52 | 0 | 4 | 56 |
| src\\components\\molecule\\MobileStepper | 1 | 15 | 0 | 4 | 19 |
| src\\components\\molecule\\ProfileBlockHeader | 1 | 28 | 0 | 4 | 32 |
| src\\components\\organism | 16 | 1,891 | 104 | 120 | 2,115 |
| src\\components\\organism (Files) | 5 | 860 | 26 | 46 | 932 |
| src\\components\\organism\\AdminLayout | 1 | 63 | 0 | 4 | 67 |
| src\\components\\organism\\AdminSideModal | 1 | 19 | 0 | 4 | 23 |
| src\\components\\organism\\BookingInfoModal | 1 | 83 | 30 | 3 | 116 |
| src\\components\\organism\\CustomerLayout | 1 | 121 | 0 | 4 | 125 |
| src\\components\\organism\\Header | 2 | 240 | 15 | 14 | 269 |
| src\\components\\organism\\Menu | 1 | 15 | 0 | 4 | 19 |
| src\\components\\organism\\SelectCarModal | 1 | 225 | 2 | 16 | 243 |
| src\\components\\organism\\TechnicianLayout | 1 | 76 | 31 | 5 | 112 |
| src\\components\\organism\\VehicleAddonEditModal | 1 | 146 | 0 | 15 | 161 |
| src\\components\\organism\\VehicleDeleteModal | 1 | 43 | 0 | 5 | 48 |
| src\\functions | 16 | 528 | 2 | 79 | 609 |
| src\\functions (Files) | 2 | 28 | 0 | 9 | 37 |
| src\\functions\\api | 10 | 351 | 2 | 48 | 401 |
| src\\functions\\cognito | 3 | 134 | 0 | 19 | 153 |
| src\\functions\\local | 1 | 15 | 0 | 3 | 18 |
| src\\hooks | 12 | 869 | 113 | 151 | 1,133 |
| src\\hooks (Files) | 8 | 549 | 105 | 101 | 755 |
| src\\hooks\\useCarServices | 2 | 147 | 0 | 23 | 170 |
| src\\hooks\\useTechnicianBooking | 2 | 173 | 8 | 27 | 208 |
| src\\pageComponents | 37 | 3,440 | 17 | 259 | 3,716 |
| src\\pageComponents\\availability | 8 | 694 | 1 | 50 | 745 |
| src\\pageComponents\\customer_profile | 5 | 549 | 0 | 38 | 587 |
| src\\pageComponents\\information | 6 | 611 | 1 | 55 | 667 |
| src\\pageComponents\\select_service | 9 | 824 | 0 | 51 | 875 |
| src\\pageComponents\\tech_booking | 5 | 445 | 12 | 35 | 492 |
| src\\pageComponents\\technician_profile | 4 | 317 | 3 | 30 | 350 |
| src\\pages | 28 | 3,398 | 136 | 230 | 3,764 |
| src\\pages (Files) | 23 | 2,513 | 132 | 184 | 2,829 |
| src\\pages\\admin_addons | 1 | 237 | 0 | 14 | 251 |
| src\\pages\\admin_service | 1 | 193 | 2 | 16 | 211 |
| src\\pages\\admin_technicians | 1 | 212 | 1 | 5 | 218 |
| src\\pages\\change_password | 1 | 151 | 1 | 6 | 158 |
| src\\pages\\customer_profile | 1 | 92 | 0 | 5 | 97 |
| src\\services | 6 | 358 | 0 | 38 | 396 |
| src\\templateComponents | 1 | 11 | 0 | 4 | 15 |
| src\\theme | 1 | 2 | 0 | 0 | 2 |
| src\\types | 18 | 723 | 9 | 70 | 802 |
| src\\values | 1 | 52 | 0 | 3 | 55 |
| styles | 2 | 259 | 0 | 39 | 298 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)