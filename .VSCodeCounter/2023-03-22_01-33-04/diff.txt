Date : 2023-03-22 01:33:04
Directory : c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web
Total : 173 files,  41324 codes, 75 comments, 447 blanks, all 41846 lines

Languages
+------------------+------------+------------+------------+------------+------------+
| language         | files      | code       | comment    | blank      | total      |
+------------------+------------+------------+------------+------------+------------+
| JSON             |          9 |     36,438 |          0 |          1 |     36,439 |
| TypeScript JSX   |         92 |      5,142 |          9 |        388 |      5,539 |
| TypeScript       |         59 |      1,091 |        108 |        151 |      1,350 |
| CSS              |          1 |         91 |          0 |          8 |         99 |
| JavaScript       |          1 |         10 |          0 |          0 |         10 |
| XML              |          1 |          1 |          0 |          0 |          1 |
| TypeScript React |         10 |     -1,449 |        -42 |       -101 |     -1,592 |
+------------------+------------+------------+------------+------------+------------+

Directories
+----------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                                 | files      | code       | comment    | blank      | total      |
+----------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                                    |        173 |     41,324 |         75 |        447 |     41,846 |
| . (Files)                                                                                                            |          4 |     36,161 |          0 |          1 |     36,162 |
| amplify                                                                                                              |          6 |        168 |          0 |          0 |        168 |
| amplify (Files)                                                                                                      |          2 |         87 |          0 |          0 |         87 |
| amplify\backend                                                                                                      |          4 |         81 |          0 |          0 |         81 |
| amplify\backend (Files)                                                                                              |          2 |         55 |          0 |          0 |         55 |
| amplify\backend\auth                                                                                                 |          1 |         25 |          0 |          0 |         25 |
| amplify\backend\auth\wheeleasyaa553eff                                                                               |          1 |         25 |          0 |          0 |         25 |
| amplify\backend\types                                                                                                |          1 |          1 |          0 |          0 |          1 |
| public                                                                                                               |          1 |          1 |          0 |          0 |          1 |
| src                                                                                                                  |        161 |      4,903 |         75 |        438 |      5,416 |
| src (Files)                                                                                                          |          1 |          1 |          0 |          0 |          1 |
| src\@redux                                                                                                           |          9 |       -235 |         -3 |        -11 |       -249 |
| src\@redux (Files)                                                                                                   |          1 |          2 |          0 |          0 |          2 |
| src\@redux\modules                                                                                                   |          8 |       -237 |         -3 |        -11 |       -251 |
| src\@redux\modules\appointment                                                                                       |          3 |       -338 |         -4 |        -27 |       -369 |
| src\@redux\modules\auth                                                                                              |          2 |        -33 |          0 |         -2 |        -35 |
| src\@redux\modules\categoryBooking                                                                                   |          2 |         64 |          1 |          8 |         73 |
| src\@redux\modules\summary                                                                                           |          1 |         70 |          0 |         10 |         80 |
| src\components                                                                                                       |         50 |      2,175 |        -13 |        155 |      2,317 |
| src\components\Layout                                                                                                |          1 |         68 |        -23 |          3 |         48 |
| src\components\Modal                                                                                                 |          1 |         12 |          0 |          0 |         12 |
| src\components\Stepper                                                                                               |          1 |         -2 |          0 |          0 |         -2 |
| src\components\VerticalStepper                                                                                       |          1 |        133 |          0 |          8 |        141 |
| src\components\atom                                                                                                  |         25 |      1,096 |          0 |         83 |      1,179 |
| src\components\atom (Files)                                                                                          |         10 |        733 |          0 |         56 |        789 |
| src\components\atom\AuthInput                                                                                        |          1 |         12 |          0 |          1 |         13 |
| src\components\atom\Buttons                                                                                          |          7 |         95 |          0 |         10 |        105 |
| src\components\atom\Buttons (Files)                                                                                  |          1 |         18 |          0 |          2 |         20 |
| src\components\atom\Buttons\AuthButton                                                                               |          1 |          0 |          0 |          1 |          1 |
| src\components\atom\Buttons\BackgroundSmallButton                                                                    |          1 |          4 |          0 |          0 |          4 |
| src\components\atom\Buttons\BorderButton                                                                             |          1 |         22 |          0 |          3 |         25 |
| src\components\atom\Buttons\BorderSmallButton                                                                        |          1 |          2 |          0 |          0 |          2 |
| src\components\atom\Buttons\GoogleAuthButton                                                                         |          1 |         52 |          0 |          4 |         56 |
| src\components\atom\Buttons\base                                                                                     |          1 |         -3 |          0 |          0 |         -3 |
| src\components\atom\SizeableImage                                                                                    |          1 |         18 |          0 |          4 |         22 |
| src\components\atom\Testimonials                                                                                     |          3 |        244 |          0 |         12 |        256 |
| src\components\atom\Texts                                                                                            |          3 |         -6 |          0 |          0 |         -6 |
| src\components\atom\Texts\GrayParagraph                                                                              |          1 |         -2 |          0 |          0 |         -2 |
| src\components\atom\Texts\MenuSectionTitle                                                                           |          1 |         -8 |          0 |          0 |         -8 |
| src\components\atom\Texts\base                                                                                       |          1 |          4 |          0 |          0 |          4 |
| src\components\molecule                                                                                              |          5 |        164 |          0 |         16 |        180 |
| src\components\molecule (Files)                                                                                      |          4 |        136 |          0 |         12 |        148 |
| src\components\molecule\ProfileBlockHeader                                                                           |          1 |         28 |          0 |          4 |         32 |
| src\components\organism                                                                                              |         16 |        704 |         10 |         45 |        759 |
| src\components\organism (Files)                                                                                      |          5 |        860 |         26 |         46 |        932 |
| src\components\organism\AddCarModal                                                                                  |          1 |       -253 |        -15 |        -13 |       -281 |
| src\components\organism\CustomerLayout                                                                               |          1 |         50 |          0 |          0 |         50 |
| src\components\organism\Header                                                                                       |          2 |        -14 |         15 |          0 |          1 |
| src\components\organism\Menu                                                                                         |          1 |          3 |          0 |          0 |          3 |
| src\components\organism\SelectCarModal                                                                               |          1 |        225 |          2 |         16 |        243 |
| src\components\organism\TechnicianLayout                                                                             |          1 |         24 |        -14 |          1 |         11 |
| src\components\organism\TimeTable                                                                                    |          2 |       -380 |         -4 |        -25 |       -409 |
| src\components\organism\VehicleAddonEditModal                                                                        |          1 |        146 |          0 |         15 |        161 |
| src\components\organism\VehicleDeleteModal                                                                           |          1 |         43 |          0 |          5 |         48 |
| src\context                                                                                                          |          1 |        -20 |          0 |         -6 |        -26 |
| src\functions                                                                                                        |          7 |       -128 |         -8 |        -16 |       -152 |
| src\functions (Files)                                                                                                |          2 |        -85 |         -8 |        -16 |       -109 |
| src\functions\api                                                                                                    |          5 |        -43 |          0 |          0 |        -43 |
| src\hooks                                                                                                            |         15 |        622 |        111 |        117 |        850 |
| src\hooks (Files)                                                                                                    |         11 |        302 |        103 |         67 |        472 |
| src\hooks\useCarServices                                                                                             |          2 |        147 |          0 |         23 |        170 |
| src\hooks\useTechnicianBooking                                                                                       |          2 |        173 |          8 |         27 |        208 |
| src\pageComponents                                                                                                   |         32 |      1,200 |        -15 |        122 |      1,307 |
| src\pageComponents\availability                                                                                      |          6 |         66 |          1 |         16 |         83 |
| src\pageComponents\customer_profile                                                                                  |          5 |         58 |          0 |         16 |         74 |
| src\pageComponents\information                                                                                       |          4 |        424 |          0 |         39 |        463 |
| src\pageComponents\select_service                                                                                    |          8 |        358 |        -19 |         20 |        359 |
| src\pageComponents\tech_booking                                                                                      |          5 |        -23 |          0 |          1 |        -22 |
| src\pageComponents\technician_profile                                                                                |          4 |        317 |          3 |         30 |        350 |
| src\pages                                                                                                            |         20 |        458 |         -5 |         16 |        469 |
| src\pages (Files)                                                                                                    |         15 |        823 |         17 |         48 |        888 |
| src\pages\availability                                                                                               |          1 |       -149 |          0 |        -11 |       -160 |
| src\pages\change_password                                                                                            |          1 |        151 |          1 |          6 |        158 |
| src\pages\customer_profile                                                                                           |          1 |         31 |          0 |          1 |         32 |
| src\pages\information                                                                                                |          1 |       -209 |        -14 |        -13 |       -236 |
| src\pages\tech_booking                                                                                               |          1 |       -189 |         -9 |        -15 |       -213 |
| src\services                                                                                                         |          6 |        358 |          0 |         38 |        396 |
| src\theme                                                                                                            |          1 |         -9 |          0 |         -3 |        -12 |
| src\types                                                                                                            |         18 |        430 |          8 |         23 |        461 |
| src\values                                                                                                           |          1 |         51 |          0 |          3 |         54 |
| styles                                                                                                               |          1 |         91 |          0 |          8 |         99 |
+----------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+----------------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| filename                                                                                                             | language         | code       | comment    | blank      | total      |
+----------------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.prettierrc                                                   | JSON             |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\auth\wheeleasyaa553eff\parameters.json        | JSON             |         25 |          0 |          0 |         25 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\backend-config.json                           | JSON             |         45 |          0 |          0 |         45 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\tags.json                                     | JSON             |         10 |          0 |          0 |         10 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\types\amplify-dependent-resources-ref.d.ts    | TypeScript       |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\cli.json                                              | JSON             |         62 |          0 |          0 |         62 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\team-provider-info.json                               | JSON             |         25 |          0 |          0 |         25 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\package-lock.json                                             | JSON             |     36,148 |          0 |          1 |     36,149 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\package.json                                                  | JSON             |          2 |          0 |          0 |          2 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\public\google_icon.svg                                        | XML              |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\index.ts                                           | TypeScript       |          2 |          0 |          0 |          2 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\function.ts                    | TypeScript       |        -60 |         -2 |         -8 |        -70 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\index.ts                       | TypeScript       |       -262 |         -2 |        -17 |       -281 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\appointment\thunk.ts                       | TypeScript       |        -16 |          0 |         -2 |        -18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\index.ts                              | TypeScript       |        -18 |          0 |         -1 |        -19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\thunks.ts                             | TypeScript       |        -15 |          0 |         -1 |        -16 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\categoryBooking\index.ts                   | TypeScript       |         45 |          0 |          5 |         50 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\categoryBooking\selector.ts                | TypeScript       |         19 |          1 |          3 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\summary\index.ts                           | TypeScript       |         70 |          0 |         10 |         80 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Layout\index.tsx                               | TypeScript JSX   |         68 |        -23 |          3 |         48 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\ConfirmCancelModal.tsx                   | TypeScript JSX   |         12 |          0 |          0 |         12 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Stepper\index.tsx                              | TypeScript JSX   |         -2 |          0 |          0 |         -2 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\VerticalStepper\index.tsx                      | TypeScript JSX   |        133 |          0 |          8 |        141 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\AuthInput\index.tsx                       | TypeScript JSX   |         12 |          0 |          1 |         13 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\BookingDateDisplay.tsx                    | TypeScript JSX   |         33 |          0 |          6 |         39 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AuthButton\index.tsx              | TypeScript JSX   |          0 |          0 |          1 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundSmallButton\index.tsx   | TypeScript JSX   |          4 |          0 |          0 |          4 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BorderButton\index.tsx            | TypeScript JSX   |         22 |          0 |          3 |         25 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BorderSmallButton\index.tsx       | TypeScript JSX   |          2 |          0 |          0 |          2 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\GoogleAuthButton\index.tsx        | TypeScript JSX   |         52 |          0 |          4 |         56 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\MenuButton.tsx                    | TypeScript JSX   |         18 |          0 |          2 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\SmallButton.tsx              | TypeScript JSX   |         -3 |          0 |          0 |         -3 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Icons.tsx                                 | TypeScript JSX   |        393 |          0 |         17 |        410 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ImageFileInput.tsx                        | TypeScript JSX   |         15 |          0 |          2 |         17 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Rating.tsx                                | TypeScript JSX   |         31 |          0 |          7 |         38 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\SelectDateTechButton.tsx                  | TypeScript JSX   |         19 |          0 |          4 |         23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\SizeableImage\index.tsx                   | TypeScript JSX   |         18 |          0 |          4 |         22 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Spinner.tsx                               | TypeScript JSX   |         12 |          0 |          4 |         16 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\StickyContainer.tsx                       | TypeScript JSX   |         11 |          0 |          3 |         14 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\SummaryPageCarItem.tsx                    | TypeScript JSX   |        202 |          0 |          9 |        211 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Testimonials\AuthTestimonialCards.tsx     | TypeScript JSX   |         85 |          0 |          7 |         92 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Testimonials\TestimonialCard.tsx          | TypeScript JSX   |         39 |          0 |          5 |         44 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Testimonials\testimonials.json            | JSON             |        120 |          0 |          0 |        120 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TextInput.tsx                             | TypeScript JSX   |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayParagraph\index.tsx             | TypeScript JSX   |         -2 |          0 |          0 |         -2 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\MenuSectionTitle\index.tsx          | TypeScript JSX   |         -8 |          0 |          0 |         -8 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text14.tsx                     | TypeScript JSX   |          4 |          0 |          0 |          4 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TitleWithIcon.tsx                         | TypeScript JSX   |         16 |          0 |          4 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ModalConfirmCancelBar.tsx             | TypeScript JSX   |         10 |          0 |          0 |         10 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ProfileBlockHeader\index.tsx          | TypeScript JSX   |         28 |          0 |          4 |         32 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\SelectedTechInfoTab.tsx               | TypeScript JSX   |         56 |          0 |          4 |         60 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ServiceCarItem.tsx                    | TypeScript JSX   |         48 |          0 |          4 |         52 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\StepHeader.tsx                        | TypeScript JSX   |         22 |          0 |          4 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddCarModal\index.tsx                 | TypeScript React |       -253 |        -15 |        -13 |       -281 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddressModal.tsx                      | TypeScript JSX   |        107 |          0 |          8 |        115 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddressSelector.tsx                   | TypeScript JSX   |         56 |          0 |          5 |         61 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Calendar.tsx                          | TypeScript JSX   |        581 |         18 |         20 |        619 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\CustomerLayout\index.tsx              | TypeScript JSX   |         50 |          0 |          0 |         50 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\CustomerReviewModal.tsx               | TypeScript JSX   |         76 |          8 |          6 |         90 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\index.tsx                      | TypeScript JSX   |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\mobile.tsx                     | TypeScript JSX   |        -15 |         15 |          0 |          0 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Menu\index.tsx                        | TypeScript JSX   |          3 |          0 |          0 |          3 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\SelectCarModal\index.tsx              | TypeScript JSX   |        225 |          2 |         16 |        243 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TechnicianLayout\index.tsx            | TypeScript JSX   |         24 |        -14 |          1 |         11 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TechnicianReviewModal.tsx             | TypeScript JSX   |         40 |          0 |          7 |         47 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TimeTable\DateRow.tsx                 | TypeScript React |        -44 |          0 |         -4 |        -48 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TimeTable\index.tsx                   | TypeScript React |       -336 |         -4 |        -21 |       -361 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\VehicleAddonEditModal\index.tsx       | TypeScript JSX   |        146 |          0 |         15 |        161 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\VehicleDeleteModal\index.tsx          | TypeScript JSX   |         43 |          0 |          5 |         48 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\context\global.tsx                                        | TypeScript React |        -20 |          0 |         -6 |        -26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\booking.ts                                  | TypeScript       |        -30 |          0 |          1 |        -29 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\calendar.ts                                 | TypeScript       |          7 |          0 |          2 |          9 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\car.ts                                      | TypeScript       |        -36 |          0 |         -5 |        -41 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\technician.ts                               | TypeScript       |          7 |          0 |          1 |          8 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\user.ts                                     | TypeScript       |          9 |          0 |          1 |         10 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\appointment.ts                                  | TypeScript       |        -70 |         -8 |        -12 |        -90 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\calendar.ts                                     | TypeScript       |        -15 |          0 |         -4 |        -19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAddresses.ts                                     | TypeScript       |         16 |          0 |          4 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAvailabilities.ts                                | TypeScript       |        172 |         30 |         17 |        219 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAvailability.ts                                  | TypeScript       |       -118 |         -1 |        -12 |       -131 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCalendarController.ts                            | TypeScript       |        -40 |          0 |         -7 |        -47 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCarServices\index.ts                             | TypeScript       |         84 |          0 |         15 |         99 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCarServices\useContinueButton.ts                 | TypeScript       |         63 |          0 |          8 |         71 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCategories.ts                                    | TypeScript       |        -19 |          0 |         -4 |        -23 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useReducedMotion.ts                                 | TypeScript       |         17 |          0 |          9 |         26 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useRemoteAddon.ts                                   | TypeScript       |         23 |          0 |          5 |         28 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useRemoteService.ts                                 | TypeScript       |         16 |          0 |          4 |         20 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianBooking\index.ts                       | TypeScript       |        117 |          8 |         16 |        141 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianBooking\useCalendarOffsetController.ts | TypeScript       |         56 |          0 |         11 |         67 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianSearch.ts                              | TypeScript       |        187 |         74 |         42 |        303 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTimer.ts                                         | TypeScript       |         26 |          0 |          3 |         29 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useWindowDimensions.ts                              | TypeScript       |         22 |          0 |          6 |         28 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\jest.setup.ts                                             | TypeScript       |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CalendarDatePicker.tsx        | TypeScript JSX   |        269 |          1 |         17 |        287 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\ReviewItem.tsx                | TypeScript JSX   |        -33 |          0 |          1 |        -32 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SelectDateTechnicianModal.tsx | TypeScript JSX   |         97 |          0 |          8 |        105 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SideMenu.tsx                  | TypeScript React |       -132 |          0 |         -7 |       -139 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianItem.tsx            | TypeScript JSX   |        -13 |          0 |          0 |        -13 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianList.tsx            | TypeScript JSX   |       -122 |          0 |         -3 |       -125 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\AddressBlock.tsx          | TypeScript JSX   |        -44 |          0 |          2 |        -42 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\EditProfileModal.tsx      | TypeScript JSX   |        110 |          0 |         11 |        121 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\InformationBlock.tsx      | TypeScript JSX   |       -104 |          0 |         -2 |       -106 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleBlock.tsx          | TypeScript JSX   |         35 |          0 |          5 |         40 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleItem.tsx           | TypeScript JSX   |         61 |          0 |          0 |         61 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\AddonVehicleItem.tsx           | TypeScript JSX   |         75 |          0 |          6 |         81 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\CustomerInfo.tsx               | TypeScript JSX   |        107 |          0 |          8 |        115 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\PaymentBlock.tsx               | TypeScript JSX   |         80 |          0 |         11 |         91 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\Review.tsx                     | TypeScript JSX   |        162 |          0 |         14 |        176 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\AddCarButton.tsx            | TypeScript JSX   |          8 |          0 |          0 |          8 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\AddonSelectionModal.tsx     | TypeScript JSX   |        105 |          0 |          7 |        112 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarMenu.tsx                 | TypeScript JSX   |         -1 |          0 |          0 |         -1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\MobileServiceItem.tsx       | TypeScript JSX   |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceItem.tsx             | TypeScript JSX   |         38 |          0 |          4 |         42 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceTotalViewer.tsx      | TypeScript JSX   |          0 |        -10 |          0 |        -10 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceVehicleItem.tsx      | TypeScript JSX   |        122 |          0 |          8 |        130 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\Services.tsx                | TypeScript JSX   |         85 |         -9 |          1 |         77 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityBlock.tsx         | TypeScript JSX   |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityModal.tsx         | TypeScript JSX   |       -122 |          0 |         -8 |       -130 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilitySection.tsx       | TypeScript JSX   |         12 |          0 |         -1 |         11 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\CalendarSection.tsx           | TypeScript JSX   |        -13 |          0 |          0 |        -13 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\CalendarSyncOptionsModal.tsx  | TypeScript JSX   |         99 |          0 |         10 |        109 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\BiographyBlock.tsx      | TypeScript JSX   |         68 |          1 |          8 |         77 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\CategoryBlock.tsx       | TypeScript JSX   |        126 |          2 |         10 |        138 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\CertificationBlock.tsx  | TypeScript JSX   |         47 |          0 |          5 |         52 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\ServiceAreaBlock.tsx    | TypeScript JSX   |         76 |          0 |          7 |         83 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_app.tsx                                            | TypeScript JSX   |          0 |          0 |          2 |          2 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\account.tsx                                         | TypeScript JSX   |         15 |         -1 |         -3 |         11 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\account_confirm.tsx                                 | TypeScript JSX   |         70 |          0 |          6 |         76 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\availability\index.tsx                              | TypeScript React |       -149 |          0 |        -11 |       -160 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\booking_calendar.tsx                                | TypeScript JSX   |        185 |          1 |         11 |        197 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\change_password\index.tsx                           | TypeScript JSX   |        151 |          1 |          6 |        158 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\confirm.tsx                                         | TypeScript JSX   |        172 |          0 |         13 |        185 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_booking.tsx                                | TypeScript JSX   |         58 |          0 |          2 |         60 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_profile\index.tsx                          | TypeScript JSX   |         31 |          0 |          1 |         32 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\index.tsx                                           | TypeScript JSX   |        -90 |          0 |         -9 |        -99 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\information.tsx                                     | TypeScript JSX   |         85 |          0 |         13 |         98 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\information\index.tsx                               | TypeScript React |       -209 |        -14 |        -13 |       -236 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\login.tsx                                           | TypeScript JSX   |         82 |          0 |          5 |         87 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\signup.tsx                                          | TypeScript JSX   |         98 |          0 |          0 |         98 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\success.tsx                                         | TypeScript JSX   |         -4 |          0 |         -1 |         -5 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_booking.tsx                                    | TypeScript JSX   |        231 |         17 |         16 |        264 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_booking\index.tsx                              | TypeScript React |       -189 |         -9 |        -15 |       -213 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\technicians.tsx                                     | TypeScript React |        -91 |          0 |         -6 |        -97 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test.tsx                                            | TypeScript React |        -26 |          0 |         -5 |        -31 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_page.tsx                                       | TypeScript JSX   |         38 |          0 |          4 |         42 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\AddressService.ts                                | TypeScript       |         76 |          0 |          9 |         85 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\CalendarService.ts                               | TypeScript       |        133 |          0 |         10 |        143 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\OccupationService.ts                             | TypeScript       |         22 |          0 |          2 |         24 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\ReviewService.ts                                 | TypeScript       |         33 |          0 |          5 |         38 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\SessionService.ts                                | TypeScript       |         61 |          0 |          7 |         68 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\VehicleService.ts                                | TypeScript       |         33 |          0 |          5 |         38 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\theme\index.ts                                            | TypeScript       |         -9 |          0 |         -3 |        -12 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Address.ts                                          | TypeScript       |         10 |          0 |          0 |         10 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Booking.ts                                          | TypeScript       |         36 |          0 |          2 |         38 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\BusinessRule.test.ts                                | TypeScript       |        220 |          4 |         17 |        241 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\BusinessRule.ts                                     | TypeScript       |        119 |          1 |         19 |        139 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\CalendarBlock.ts                                    | TypeScript       |         15 |          0 |          1 |         16 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Color.ts                                            | TypeScript       |         15 |          0 |          2 |         17 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Customer.ts                                         | TypeScript       |         16 |          0 |          2 |         18 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\GoogleCalendarBlock.ts                              | TypeScript       |         18 |          0 |          1 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Review.ts                                           | TypeScript       |         19 |          0 |          0 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Technician.ts                                       | TypeScript       |         17 |          0 |          2 |         19 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\User.ts                                             | TypeScript       |         15 |          0 |          1 |         16 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\addon.ts                                            | TypeScript       |         -3 |          0 |         -4 |         -7 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\calendarTypeBooking.ts                              | TypeScript       |         -8 |          1 |         -2 |         -9 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\car.ts                                              | TypeScript       |        -20 |          0 |         -7 |        -27 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\carServiceExtension.ts                              | TypeScript       |         33 |          0 |          4 |         37 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\category.ts                                         | TypeScript       |        -25 |          2 |         -2 |        -25 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\index.ts                                            | TypeScript       |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\user.ts                                             | TypeScript       |        -48 |          0 |        -13 |        -61 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\values\index.ts                                           | TypeScript       |         51 |          0 |          3 |         54 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\globals.css                                            | CSS              |         91 |          0 |          8 |         99 |
| c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tailwind.config.js                                            | JavaScript       |         10 |          0 |          0 |         10 |
| Total                                                                                                                |                  |     41,324 |         75 |        447 |     41,846 |
+----------------------------------------------------------------------------------------------------------------------+------------------+------------+------------+------------+------------+