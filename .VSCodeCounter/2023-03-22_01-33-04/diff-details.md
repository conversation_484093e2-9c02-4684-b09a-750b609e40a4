# Diff Details

Date : 2023-03-22 01:33:04

Directory c:\\Users\\<USER>\\Desktop\\Desktop\\desk\\luxe\\wheeleasy_web

Total : 173 files,  41324 codes, 75 comments, 447 blanks, all 41846 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.prettierrc](/.prettierrc) | JSON | 1 | 0 | 0 | 1 |
| [amplify/backend/auth/wheeleasyaa553eff/parameters.json](/amplify/backend/auth/wheeleasyaa553eff/parameters.json) | JSON | 25 | 0 | 0 | 25 |
| [amplify/backend/backend-config.json](/amplify/backend/backend-config.json) | JSON | 45 | 0 | 0 | 45 |
| [amplify/backend/tags.json](/amplify/backend/tags.json) | JSON | 10 | 0 | 0 | 10 |
| [amplify/backend/types/amplify-dependent-resources-ref.d.ts](/amplify/backend/types/amplify-dependent-resources-ref.d.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [amplify/cli.json](/amplify/cli.json) | JSON | 62 | 0 | 0 | 62 |
| [amplify/team-provider-info.json](/amplify/team-provider-info.json) | JSON | 25 | 0 | 0 | 25 |
| [package-lock.json](/package-lock.json) | JSON | 36,148 | 0 | 1 | 36,149 |
| [package.json](/package.json) | JSON | 2 | 0 | 0 | 2 |
| [public/google_icon.svg](/public/google_icon.svg) | XML | 1 | 0 | 0 | 1 |
| [src/@redux/index.ts](/src/@redux/index.ts) | TypeScript | 2 | 0 | 0 | 2 |
| [src/@redux/modules/appointment/function.ts](/src/@redux/modules/appointment/function.ts) | TypeScript | -60 | -2 | -8 | -70 |
| [src/@redux/modules/appointment/index.ts](/src/@redux/modules/appointment/index.ts) | TypeScript | -262 | -2 | -17 | -281 |
| [src/@redux/modules/appointment/thunk.ts](/src/@redux/modules/appointment/thunk.ts) | TypeScript | -16 | 0 | -2 | -18 |
| [src/@redux/modules/auth/index.ts](/src/@redux/modules/auth/index.ts) | TypeScript | -18 | 0 | -1 | -19 |
| [src/@redux/modules/auth/thunks.ts](/src/@redux/modules/auth/thunks.ts) | TypeScript | -15 | 0 | -1 | -16 |
| [src/@redux/modules/categoryBooking/index.ts](/src/@redux/modules/categoryBooking/index.ts) | TypeScript | 45 | 0 | 5 | 50 |
| [src/@redux/modules/categoryBooking/selector.ts](/src/@redux/modules/categoryBooking/selector.ts) | TypeScript | 19 | 1 | 3 | 23 |
| [src/@redux/modules/summary/index.ts](/src/@redux/modules/summary/index.ts) | TypeScript | 70 | 0 | 10 | 80 |
| [src/components/Layout/index.tsx](/src/components/Layout/index.tsx) | TypeScript JSX | 68 | -23 | 3 | 48 |
| [src/components/Modal/ConfirmCancelModal.tsx](/src/components/Modal/ConfirmCancelModal.tsx) | TypeScript JSX | 12 | 0 | 0 | 12 |
| [src/components/Stepper/index.tsx](/src/components/Stepper/index.tsx) | TypeScript JSX | -2 | 0 | 0 | -2 |
| [src/components/VerticalStepper/index.tsx](/src/components/VerticalStepper/index.tsx) | TypeScript JSX | 133 | 0 | 8 | 141 |
| [src/components/atom/AuthInput/index.tsx](/src/components/atom/AuthInput/index.tsx) | TypeScript JSX | 12 | 0 | 1 | 13 |
| [src/components/atom/BookingDateDisplay.tsx](/src/components/atom/BookingDateDisplay.tsx) | TypeScript JSX | 33 | 0 | 6 | 39 |
| [src/components/atom/Buttons/AuthButton/index.tsx](/src/components/atom/Buttons/AuthButton/index.tsx) | TypeScript JSX | 0 | 0 | 1 | 1 |
| [src/components/atom/Buttons/BackgroundSmallButton/index.tsx](/src/components/atom/Buttons/BackgroundSmallButton/index.tsx) | TypeScript JSX | 4 | 0 | 0 | 4 |
| [src/components/atom/Buttons/BorderButton/index.tsx](/src/components/atom/Buttons/BorderButton/index.tsx) | TypeScript JSX | 22 | 0 | 3 | 25 |
| [src/components/atom/Buttons/BorderSmallButton/index.tsx](/src/components/atom/Buttons/BorderSmallButton/index.tsx) | TypeScript JSX | 2 | 0 | 0 | 2 |
| [src/components/atom/Buttons/GoogleAuthButton/index.tsx](/src/components/atom/Buttons/GoogleAuthButton/index.tsx) | TypeScript JSX | 52 | 0 | 4 | 56 |
| [src/components/atom/Buttons/MenuButton.tsx](/src/components/atom/Buttons/MenuButton.tsx) | TypeScript JSX | 18 | 0 | 2 | 20 |
| [src/components/atom/Buttons/base/SmallButton.tsx](/src/components/atom/Buttons/base/SmallButton.tsx) | TypeScript JSX | -3 | 0 | 0 | -3 |
| [src/components/atom/Icons.tsx](/src/components/atom/Icons.tsx) | TypeScript JSX | 393 | 0 | 17 | 410 |
| [src/components/atom/ImageFileInput.tsx](/src/components/atom/ImageFileInput.tsx) | TypeScript JSX | 15 | 0 | 2 | 17 |
| [src/components/atom/Rating.tsx](/src/components/atom/Rating.tsx) | TypeScript JSX | 31 | 0 | 7 | 38 |
| [src/components/atom/SelectDateTechButton.tsx](/src/components/atom/SelectDateTechButton.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/atom/SizeableImage/index.tsx](/src/components/atom/SizeableImage/index.tsx) | TypeScript JSX | 18 | 0 | 4 | 22 |
| [src/components/atom/Spinner.tsx](/src/components/atom/Spinner.tsx) | TypeScript JSX | 12 | 0 | 4 | 16 |
| [src/components/atom/StickyContainer.tsx](/src/components/atom/StickyContainer.tsx) | TypeScript JSX | 11 | 0 | 3 | 14 |
| [src/components/atom/SummaryPageCarItem.tsx](/src/components/atom/SummaryPageCarItem.tsx) | TypeScript JSX | 202 | 0 | 9 | 211 |
| [src/components/atom/Testimonials/AuthTestimonialCards.tsx](/src/components/atom/Testimonials/AuthTestimonialCards.tsx) | TypeScript JSX | 85 | 0 | 7 | 92 |
| [src/components/atom/Testimonials/TestimonialCard.tsx](/src/components/atom/Testimonials/TestimonialCard.tsx) | TypeScript JSX | 39 | 0 | 5 | 44 |
| [src/components/atom/Testimonials/testimonials.json](/src/components/atom/Testimonials/testimonials.json) | JSON | 120 | 0 | 0 | 120 |
| [src/components/atom/TextInput.tsx](/src/components/atom/TextInput.tsx) | TypeScript JSX | 1 | 0 | 0 | 1 |
| [src/components/atom/Texts/GrayParagraph/index.tsx](/src/components/atom/Texts/GrayParagraph/index.tsx) | TypeScript JSX | -2 | 0 | 0 | -2 |
| [src/components/atom/Texts/MenuSectionTitle/index.tsx](/src/components/atom/Texts/MenuSectionTitle/index.tsx) | TypeScript JSX | -8 | 0 | 0 | -8 |
| [src/components/atom/Texts/base/Text14.tsx](/src/components/atom/Texts/base/Text14.tsx) | TypeScript JSX | 4 | 0 | 0 | 4 |
| [src/components/atom/TitleWithIcon.tsx](/src/components/atom/TitleWithIcon.tsx) | TypeScript JSX | 16 | 0 | 4 | 20 |
| [src/components/molecule/ModalConfirmCancelBar.tsx](/src/components/molecule/ModalConfirmCancelBar.tsx) | TypeScript JSX | 10 | 0 | 0 | 10 |
| [src/components/molecule/ProfileBlockHeader/index.tsx](/src/components/molecule/ProfileBlockHeader/index.tsx) | TypeScript JSX | 28 | 0 | 4 | 32 |
| [src/components/molecule/SelectedTechInfoTab.tsx](/src/components/molecule/SelectedTechInfoTab.tsx) | TypeScript JSX | 56 | 0 | 4 | 60 |
| [src/components/molecule/ServiceCarItem.tsx](/src/components/molecule/ServiceCarItem.tsx) | TypeScript JSX | 48 | 0 | 4 | 52 |
| [src/components/molecule/StepHeader.tsx](/src/components/molecule/StepHeader.tsx) | TypeScript JSX | 22 | 0 | 4 | 26 |
| [src/components/organism/AddCarModal/index.tsx](/src/components/organism/AddCarModal/index.tsx) | TypeScript React | -253 | -15 | -13 | -281 |
| [src/components/organism/AddressModal.tsx](/src/components/organism/AddressModal.tsx) | TypeScript JSX | 107 | 0 | 8 | 115 |
| [src/components/organism/AddressSelector.tsx](/src/components/organism/AddressSelector.tsx) | TypeScript JSX | 56 | 0 | 5 | 61 |
| [src/components/organism/Calendar.tsx](/src/components/organism/Calendar.tsx) | TypeScript JSX | 581 | 18 | 20 | 619 |
| [src/components/organism/CustomerLayout/index.tsx](/src/components/organism/CustomerLayout/index.tsx) | TypeScript JSX | 50 | 0 | 0 | 50 |
| [src/components/organism/CustomerReviewModal.tsx](/src/components/organism/CustomerReviewModal.tsx) | TypeScript JSX | 76 | 8 | 6 | 90 |
| [src/components/organism/Header/index.tsx](/src/components/organism/Header/index.tsx) | TypeScript JSX | 1 | 0 | 0 | 1 |
| [src/components/organism/Header/mobile.tsx](/src/components/organism/Header/mobile.tsx) | TypeScript JSX | -15 | 15 | 0 | 0 |
| [src/components/organism/Menu/index.tsx](/src/components/organism/Menu/index.tsx) | TypeScript JSX | 3 | 0 | 0 | 3 |
| [src/components/organism/SelectCarModal/index.tsx](/src/components/organism/SelectCarModal/index.tsx) | TypeScript JSX | 225 | 2 | 16 | 243 |
| [src/components/organism/TechnicianLayout/index.tsx](/src/components/organism/TechnicianLayout/index.tsx) | TypeScript JSX | 24 | -14 | 1 | 11 |
| [src/components/organism/TechnicianReviewModal.tsx](/src/components/organism/TechnicianReviewModal.tsx) | TypeScript JSX | 40 | 0 | 7 | 47 |
| [src/components/organism/TimeTable/DateRow.tsx](/src/components/organism/TimeTable/DateRow.tsx) | TypeScript React | -44 | 0 | -4 | -48 |
| [src/components/organism/TimeTable/index.tsx](/src/components/organism/TimeTable/index.tsx) | TypeScript React | -336 | -4 | -21 | -361 |
| [src/components/organism/VehicleAddonEditModal/index.tsx](/src/components/organism/VehicleAddonEditModal/index.tsx) | TypeScript JSX | 146 | 0 | 15 | 161 |
| [src/components/organism/VehicleDeleteModal/index.tsx](/src/components/organism/VehicleDeleteModal/index.tsx) | TypeScript JSX | 43 | 0 | 5 | 48 |
| [src/context/global.tsx](/src/context/global.tsx) | TypeScript React | -20 | 0 | -6 | -26 |
| [src/functions/api/booking.ts](/src/functions/api/booking.ts) | TypeScript | -30 | 0 | 1 | -29 |
| [src/functions/api/calendar.ts](/src/functions/api/calendar.ts) | TypeScript | 7 | 0 | 2 | 9 |
| [src/functions/api/car.ts](/src/functions/api/car.ts) | TypeScript | -36 | 0 | -5 | -41 |
| [src/functions/api/technician.ts](/src/functions/api/technician.ts) | TypeScript | 7 | 0 | 1 | 8 |
| [src/functions/api/user.ts](/src/functions/api/user.ts) | TypeScript | 9 | 0 | 1 | 10 |
| [src/functions/appointment.ts](/src/functions/appointment.ts) | TypeScript | -70 | -8 | -12 | -90 |
| [src/functions/calendar.ts](/src/functions/calendar.ts) | TypeScript | -15 | 0 | -4 | -19 |
| [src/hooks/useAddresses.ts](/src/hooks/useAddresses.ts) | TypeScript | 16 | 0 | 4 | 20 |
| [src/hooks/useAvailabilities.ts](/src/hooks/useAvailabilities.ts) | TypeScript | 172 | 30 | 17 | 219 |
| [src/hooks/useAvailability.ts](/src/hooks/useAvailability.ts) | TypeScript | -118 | -1 | -12 | -131 |
| [src/hooks/useCalendarController.ts](/src/hooks/useCalendarController.ts) | TypeScript | -40 | 0 | -7 | -47 |
| [src/hooks/useCarServices/index.ts](/src/hooks/useCarServices/index.ts) | TypeScript | 84 | 0 | 15 | 99 |
| [src/hooks/useCarServices/useContinueButton.ts](/src/hooks/useCarServices/useContinueButton.ts) | TypeScript | 63 | 0 | 8 | 71 |
| [src/hooks/useCategories.ts](/src/hooks/useCategories.ts) | TypeScript | -19 | 0 | -4 | -23 |
| [src/hooks/useReducedMotion.ts](/src/hooks/useReducedMotion.ts) | TypeScript | 17 | 0 | 9 | 26 |
| [src/hooks/useRemoteAddon.ts](/src/hooks/useRemoteAddon.ts) | TypeScript | 23 | 0 | 5 | 28 |
| [src/hooks/useRemoteService.ts](/src/hooks/useRemoteService.ts) | TypeScript | 16 | 0 | 4 | 20 |
| [src/hooks/useTechnicianBooking/index.ts](/src/hooks/useTechnicianBooking/index.ts) | TypeScript | 117 | 8 | 16 | 141 |
| [src/hooks/useTechnicianBooking/useCalendarOffsetController.ts](/src/hooks/useTechnicianBooking/useCalendarOffsetController.ts) | TypeScript | 56 | 0 | 11 | 67 |
| [src/hooks/useTechnicianSearch.ts](/src/hooks/useTechnicianSearch.ts) | TypeScript | 187 | 74 | 42 | 303 |
| [src/hooks/useTimer.ts](/src/hooks/useTimer.ts) | TypeScript | 26 | 0 | 3 | 29 |
| [src/hooks/useWindowDimensions.ts](/src/hooks/useWindowDimensions.ts) | TypeScript | 22 | 0 | 6 | 28 |
| [src/jest.setup.ts](/src/jest.setup.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [src/pageComponents/availability/CalendarDatePicker.tsx](/src/pageComponents/availability/CalendarDatePicker.tsx) | TypeScript JSX | 269 | 1 | 17 | 287 |
| [src/pageComponents/availability/ReviewItem.tsx](/src/pageComponents/availability/ReviewItem.tsx) | TypeScript JSX | -33 | 0 | 1 | -32 |
| [src/pageComponents/availability/SelectDateTechnicianModal.tsx](/src/pageComponents/availability/SelectDateTechnicianModal.tsx) | TypeScript JSX | 97 | 0 | 8 | 105 |
| [src/pageComponents/availability/SideMenu.tsx](/src/pageComponents/availability/SideMenu.tsx) | TypeScript React | -132 | 0 | -7 | -139 |
| [src/pageComponents/availability/TechnicianItem.tsx](/src/pageComponents/availability/TechnicianItem.tsx) | TypeScript JSX | -13 | 0 | 0 | -13 |
| [src/pageComponents/availability/TechnicianList.tsx](/src/pageComponents/availability/TechnicianList.tsx) | TypeScript JSX | -122 | 0 | -3 | -125 |
| [src/pageComponents/customer_profile/AddressBlock.tsx](/src/pageComponents/customer_profile/AddressBlock.tsx) | TypeScript JSX | -44 | 0 | 2 | -42 |
| [src/pageComponents/customer_profile/EditProfileModal.tsx](/src/pageComponents/customer_profile/EditProfileModal.tsx) | TypeScript JSX | 110 | 0 | 11 | 121 |
| [src/pageComponents/customer_profile/InformationBlock.tsx](/src/pageComponents/customer_profile/InformationBlock.tsx) | TypeScript JSX | -104 | 0 | -2 | -106 |
| [src/pageComponents/customer_profile/VehicleBlock.tsx](/src/pageComponents/customer_profile/VehicleBlock.tsx) | TypeScript JSX | 35 | 0 | 5 | 40 |
| [src/pageComponents/customer_profile/VehicleItem.tsx](/src/pageComponents/customer_profile/VehicleItem.tsx) | TypeScript JSX | 61 | 0 | 0 | 61 |
| [src/pageComponents/information/AddonVehicleItem.tsx](/src/pageComponents/information/AddonVehicleItem.tsx) | TypeScript JSX | 75 | 0 | 6 | 81 |
| [src/pageComponents/information/CustomerInfo.tsx](/src/pageComponents/information/CustomerInfo.tsx) | TypeScript JSX | 107 | 0 | 8 | 115 |
| [src/pageComponents/information/PaymentBlock.tsx](/src/pageComponents/information/PaymentBlock.tsx) | TypeScript JSX | 80 | 0 | 11 | 91 |
| [src/pageComponents/information/Review.tsx](/src/pageComponents/information/Review.tsx) | TypeScript JSX | 162 | 0 | 14 | 176 |
| [src/pageComponents/select_service/AddCarButton.tsx](/src/pageComponents/select_service/AddCarButton.tsx) | TypeScript JSX | 8 | 0 | 0 | 8 |
| [src/pageComponents/select_service/AddonSelectionModal.tsx](/src/pageComponents/select_service/AddonSelectionModal.tsx) | TypeScript JSX | 105 | 0 | 7 | 112 |
| [src/pageComponents/select_service/CarMenu.tsx](/src/pageComponents/select_service/CarMenu.tsx) | TypeScript JSX | -1 | 0 | 0 | -1 |
| [src/pageComponents/select_service/MobileServiceItem.tsx](/src/pageComponents/select_service/MobileServiceItem.tsx) | TypeScript JSX | 1 | 0 | 0 | 1 |
| [src/pageComponents/select_service/ServiceItem.tsx](/src/pageComponents/select_service/ServiceItem.tsx) | TypeScript JSX | 38 | 0 | 4 | 42 |
| [src/pageComponents/select_service/ServiceTotalViewer.tsx](/src/pageComponents/select_service/ServiceTotalViewer.tsx) | TypeScript JSX | 0 | -10 | 0 | -10 |
| [src/pageComponents/select_service/ServiceVehicleItem.tsx](/src/pageComponents/select_service/ServiceVehicleItem.tsx) | TypeScript JSX | 122 | 0 | 8 | 130 |
| [src/pageComponents/select_service/Services.tsx](/src/pageComponents/select_service/Services.tsx) | TypeScript JSX | 85 | -9 | 1 | 77 |
| [src/pageComponents/tech_booking/AvailabilityBlock.tsx](/src/pageComponents/tech_booking/AvailabilityBlock.tsx) | TypeScript JSX | 1 | 0 | 0 | 1 |
| [src/pageComponents/tech_booking/AvailabilityModal.tsx](/src/pageComponents/tech_booking/AvailabilityModal.tsx) | TypeScript JSX | -122 | 0 | -8 | -130 |
| [src/pageComponents/tech_booking/AvailabilitySection.tsx](/src/pageComponents/tech_booking/AvailabilitySection.tsx) | TypeScript JSX | 12 | 0 | -1 | 11 |
| [src/pageComponents/tech_booking/CalendarSection.tsx](/src/pageComponents/tech_booking/CalendarSection.tsx) | TypeScript JSX | -13 | 0 | 0 | -13 |
| [src/pageComponents/tech_booking/CalendarSyncOptionsModal.tsx](/src/pageComponents/tech_booking/CalendarSyncOptionsModal.tsx) | TypeScript JSX | 99 | 0 | 10 | 109 |
| [src/pageComponents/technician_profile/BiographyBlock.tsx](/src/pageComponents/technician_profile/BiographyBlock.tsx) | TypeScript JSX | 68 | 1 | 8 | 77 |
| [src/pageComponents/technician_profile/CategoryBlock.tsx](/src/pageComponents/technician_profile/CategoryBlock.tsx) | TypeScript JSX | 126 | 2 | 10 | 138 |
| [src/pageComponents/technician_profile/CertificationBlock.tsx](/src/pageComponents/technician_profile/CertificationBlock.tsx) | TypeScript JSX | 47 | 0 | 5 | 52 |
| [src/pageComponents/technician_profile/ServiceAreaBlock.tsx](/src/pageComponents/technician_profile/ServiceAreaBlock.tsx) | TypeScript JSX | 76 | 0 | 7 | 83 |
| [src/pages/_app.tsx](/src/pages/_app.tsx) | TypeScript JSX | 0 | 0 | 2 | 2 |
| [src/pages/account.tsx](/src/pages/account.tsx) | TypeScript JSX | 15 | -1 | -3 | 11 |
| [src/pages/account_confirm.tsx](/src/pages/account_confirm.tsx) | TypeScript JSX | 70 | 0 | 6 | 76 |
| [src/pages/availability/index.tsx](/src/pages/availability/index.tsx) | TypeScript React | -149 | 0 | -11 | -160 |
| [src/pages/booking_calendar.tsx](/src/pages/booking_calendar.tsx) | TypeScript JSX | 185 | 1 | 11 | 197 |
| [src/pages/change_password/index.tsx](/src/pages/change_password/index.tsx) | TypeScript JSX | 151 | 1 | 6 | 158 |
| [src/pages/confirm.tsx](/src/pages/confirm.tsx) | TypeScript JSX | 172 | 0 | 13 | 185 |
| [src/pages/customer_booking.tsx](/src/pages/customer_booking.tsx) | TypeScript JSX | 58 | 0 | 2 | 60 |
| [src/pages/customer_profile/index.tsx](/src/pages/customer_profile/index.tsx) | TypeScript JSX | 31 | 0 | 1 | 32 |
| [src/pages/index.tsx](/src/pages/index.tsx) | TypeScript JSX | -90 | 0 | -9 | -99 |
| [src/pages/information.tsx](/src/pages/information.tsx) | TypeScript JSX | 85 | 0 | 13 | 98 |
| [src/pages/information/index.tsx](/src/pages/information/index.tsx) | TypeScript React | -209 | -14 | -13 | -236 |
| [src/pages/login.tsx](/src/pages/login.tsx) | TypeScript JSX | 82 | 0 | 5 | 87 |
| [src/pages/signup.tsx](/src/pages/signup.tsx) | TypeScript JSX | 98 | 0 | 0 | 98 |
| [src/pages/success.tsx](/src/pages/success.tsx) | TypeScript JSX | -4 | 0 | -1 | -5 |
| [src/pages/tech_booking.tsx](/src/pages/tech_booking.tsx) | TypeScript JSX | 231 | 17 | 16 | 264 |
| [src/pages/tech_booking/index.tsx](/src/pages/tech_booking/index.tsx) | TypeScript React | -189 | -9 | -15 | -213 |
| [src/pages/technicians.tsx](/src/pages/technicians.tsx) | TypeScript React | -91 | 0 | -6 | -97 |
| [src/pages/test.tsx](/src/pages/test.tsx) | TypeScript React | -26 | 0 | -5 | -31 |
| [src/pages/test_page.tsx](/src/pages/test_page.tsx) | TypeScript JSX | 38 | 0 | 4 | 42 |
| [src/services/AddressService.ts](/src/services/AddressService.ts) | TypeScript | 76 | 0 | 9 | 85 |
| [src/services/CalendarService.ts](/src/services/CalendarService.ts) | TypeScript | 133 | 0 | 10 | 143 |
| [src/services/OccupationService.ts](/src/services/OccupationService.ts) | TypeScript | 22 | 0 | 2 | 24 |
| [src/services/ReviewService.ts](/src/services/ReviewService.ts) | TypeScript | 33 | 0 | 5 | 38 |
| [src/services/SessionService.ts](/src/services/SessionService.ts) | TypeScript | 61 | 0 | 7 | 68 |
| [src/services/VehicleService.ts](/src/services/VehicleService.ts) | TypeScript | 33 | 0 | 5 | 38 |
| [src/theme/index.ts](/src/theme/index.ts) | TypeScript | -9 | 0 | -3 | -12 |
| [src/types/Address.ts](/src/types/Address.ts) | TypeScript | 10 | 0 | 0 | 10 |
| [src/types/Booking.ts](/src/types/Booking.ts) | TypeScript | 36 | 0 | 2 | 38 |
| [src/types/BusinessRule.test.ts](/src/types/BusinessRule.test.ts) | TypeScript | 220 | 4 | 17 | 241 |
| [src/types/BusinessRule.ts](/src/types/BusinessRule.ts) | TypeScript | 119 | 1 | 19 | 139 |
| [src/types/CalendarBlock.ts](/src/types/CalendarBlock.ts) | TypeScript | 15 | 0 | 1 | 16 |
| [src/types/Color.ts](/src/types/Color.ts) | TypeScript | 15 | 0 | 2 | 17 |
| [src/types/Customer.ts](/src/types/Customer.ts) | TypeScript | 16 | 0 | 2 | 18 |
| [src/types/GoogleCalendarBlock.ts](/src/types/GoogleCalendarBlock.ts) | TypeScript | 18 | 0 | 1 | 19 |
| [src/types/Review.ts](/src/types/Review.ts) | TypeScript | 19 | 0 | 0 | 19 |
| [src/types/Technician.ts](/src/types/Technician.ts) | TypeScript | 17 | 0 | 2 | 19 |
| [src/types/User.ts](/src/types/User.ts) | TypeScript | 15 | 0 | 1 | 16 |
| [src/types/addon.ts](/src/types/addon.ts) | TypeScript | -3 | 0 | -4 | -7 |
| [src/types/calendarTypeBooking.ts](/src/types/calendarTypeBooking.ts) | TypeScript | -8 | 1 | -2 | -9 |
| [src/types/car.ts](/src/types/car.ts) | TypeScript | -20 | 0 | -7 | -27 |
| [src/types/carServiceExtension.ts](/src/types/carServiceExtension.ts) | TypeScript | 33 | 0 | 4 | 37 |
| [src/types/category.ts](/src/types/category.ts) | TypeScript | -25 | 2 | -2 | -25 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [src/types/user.ts](/src/types/user.ts) | TypeScript | -48 | 0 | -13 | -61 |
| [src/values/index.ts](/src/values/index.ts) | TypeScript | 51 | 0 | 3 | 54 |
| [styles/globals.css](/styles/globals.css) | CSS | 91 | 0 | 8 | 99 |
| [tailwind.config.js](/tailwind.config.js) | JavaScript | 10 | 0 | 0 | 10 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details