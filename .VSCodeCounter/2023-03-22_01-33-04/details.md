# Details

Date : 2023-03-22 01:33:04

Directory c:\\Users\\<USER>\\Desktop\\Desktop\\desk\\luxe\\wheeleasy_web

Total : 226 files,  51060 codes, 389 comments, 1340 blanks, all 52789 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.eslintrc.json](/.eslintrc.json) | JSON | 3 | 0 | 1 | 4 |
| [.prettierrc](/.prettierrc) | JSON | 6 | 0 | 1 | 7 |
| [README.md](/README.md) | Markdown | 20 | 0 | 15 | 35 |
| [amplify/backend/auth/wheeleasyaa553eff/parameters.json](/amplify/backend/auth/wheeleasyaa553eff/parameters.json) | JSON | 25 | 0 | 0 | 25 |
| [amplify/backend/backend-config.json](/amplify/backend/backend-config.json) | JSON | 45 | 0 | 0 | 45 |
| [amplify/backend/tags.json](/amplify/backend/tags.json) | JSON | 10 | 0 | 0 | 10 |
| [amplify/backend/types/amplify-dependent-resources-ref.d.ts](/amplify/backend/types/amplify-dependent-resources-ref.d.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [amplify/cli.json](/amplify/cli.json) | JSON | 62 | 0 | 0 | 62 |
| [amplify/team-provider-info.json](/amplify/team-provider-info.json) | JSON | 25 | 0 | 0 | 25 |
| [next-env.d.ts](/next-env.d.ts) | TypeScript | 0 | 4 | 2 | 6 |
| [next.config.js](/next.config.js) | JavaScript | 10 | 1 | 2 | 13 |
| [package-lock.json](/package-lock.json) | JSON | 36,148 | 0 | 1 | 36,149 |
| [package.json](/package.json) | JSON | 89 | 0 | 1 | 90 |
| [postcss.config.js](/postcss.config.js) | JavaScript | 6 | 0 | 1 | 7 |
| [public/google_icon.svg](/public/google_icon.svg) | XML | 1 | 0 | 0 | 1 |
| [public/vercel.svg](/public/vercel.svg) | XML | 4 | 0 | 0 | 4 |
| [src/@redux/hook.ts](/src/@redux/hook.ts) | TypeScript | 4 | 0 | 1 | 5 |
| [src/@redux/index.ts](/src/@redux/index.ts) | TypeScript | 23 | 0 | 3 | 26 |
| [src/@redux/modules/auth/index.ts](/src/@redux/modules/auth/index.ts) | TypeScript | 73 | 0 | 5 | 78 |
| [src/@redux/modules/auth/thunks.ts](/src/@redux/modules/auth/thunks.ts) | TypeScript | 37 | 0 | 6 | 43 |
| [src/@redux/modules/categoryBooking/index.ts](/src/@redux/modules/categoryBooking/index.ts) | TypeScript | 45 | 0 | 5 | 50 |
| [src/@redux/modules/categoryBooking/selector.ts](/src/@redux/modules/categoryBooking/selector.ts) | TypeScript | 19 | 1 | 3 | 23 |
| [src/@redux/modules/summary/index.ts](/src/@redux/modules/summary/index.ts) | TypeScript | 70 | 0 | 10 | 80 |
| [src/@redux/modules/techBooking/index.ts](/src/@redux/modules/techBooking/index.ts) | TypeScript | 57 | 0 | 6 | 63 |
| [src/@redux/modules/techBooking/thunk.ts](/src/@redux/modules/techBooking/thunk.ts) | TypeScript | 8 | 0 | 2 | 10 |
| [src/components/Layout/index.tsx](/src/components/Layout/index.tsx) | TypeScript JSX | 257 | 2 | 12 | 271 |
| [src/components/Modal/ConfirmCancelModal.tsx](/src/components/Modal/ConfirmCancelModal.tsx) | TypeScript JSX | 72 | 0 | 4 | 76 |
| [src/components/Modal/index.tsx](/src/components/Modal/index.tsx) | TypeScript JSX | 10 | 0 | 3 | 13 |
| [src/components/Stepper/index.tsx](/src/components/Stepper/index.tsx) | TypeScript JSX | 101 | 0 | 5 | 106 |
| [src/components/VerticalStepper/index.tsx](/src/components/VerticalStepper/index.tsx) | TypeScript JSX | 133 | 0 | 8 | 141 |
| [src/components/atom/AuthInput/index.tsx](/src/components/atom/AuthInput/index.tsx) | TypeScript JSX | 31 | 0 | 5 | 36 |
| [src/components/atom/BookingDateDisplay.tsx](/src/components/atom/BookingDateDisplay.tsx) | TypeScript JSX | 33 | 0 | 6 | 39 |
| [src/components/atom/BorderSelect.tsx](/src/components/atom/BorderSelect.tsx) | TypeScript JSX | 20 | 0 | 3 | 23 |
| [src/components/atom/Buttons/AccentTextButton.tsx](/src/components/atom/Buttons/AccentTextButton.tsx) | TypeScript JSX | 18 | 0 | 4 | 22 |
| [src/components/atom/Buttons/AuthButton/index.tsx](/src/components/atom/Buttons/AuthButton/index.tsx) | TypeScript JSX | 38 | 0 | 5 | 43 |
| [src/components/atom/Buttons/BackgroundMiddleButton.tsx](/src/components/atom/Buttons/BackgroundMiddleButton.tsx) | TypeScript JSX | 33 | 0 | 4 | 37 |
| [src/components/atom/Buttons/BackgroundSmallButton/index.tsx](/src/components/atom/Buttons/BackgroundSmallButton/index.tsx) | TypeScript JSX | 37 | 0 | 5 | 42 |
| [src/components/atom/Buttons/BorderButton/index.tsx](/src/components/atom/Buttons/BorderButton/index.tsx) | TypeScript JSX | 22 | 0 | 3 | 25 |
| [src/components/atom/Buttons/BorderSmallButton/index.tsx](/src/components/atom/Buttons/BorderSmallButton/index.tsx) | TypeScript JSX | 24 | 0 | 4 | 28 |
| [src/components/atom/Buttons/GoogleAuthButton/index.tsx](/src/components/atom/Buttons/GoogleAuthButton/index.tsx) | TypeScript JSX | 52 | 0 | 4 | 56 |
| [src/components/atom/Buttons/MenuButton.tsx](/src/components/atom/Buttons/MenuButton.tsx) | TypeScript JSX | 42 | 0 | 6 | 48 |
| [src/components/atom/Buttons/base/MiddleButton.tsx](/src/components/atom/Buttons/base/MiddleButton.tsx) | TypeScript JSX | 37 | 0 | 4 | 41 |
| [src/components/atom/Buttons/base/SmallButton.tsx](/src/components/atom/Buttons/base/SmallButton.tsx) | TypeScript JSX | 29 | 0 | 4 | 33 |
| [src/components/atom/Buttons/base/TextButton.tsx](/src/components/atom/Buttons/base/TextButton.tsx) | TypeScript JSX | 20 | 0 | 3 | 23 |
| [src/components/atom/Icons.tsx](/src/components/atom/Icons.tsx) | TypeScript JSX | 393 | 0 | 17 | 410 |
| [src/components/atom/ImageFileInput.tsx](/src/components/atom/ImageFileInput.tsx) | TypeScript JSX | 55 | 0 | 10 | 65 |
| [src/components/atom/NoPaddingPaper.tsx](/src/components/atom/NoPaddingPaper.tsx) | TypeScript JSX | 19 | 0 | 3 | 22 |
| [src/components/atom/Paper.tsx](/src/components/atom/Paper.tsx) | TypeScript JSX | 20 | 0 | 4 | 24 |
| [src/components/atom/ProfilePlaceholder.tsx](/src/components/atom/ProfilePlaceholder.tsx) | TypeScript JSX | 28 | 0 | 5 | 33 |
| [src/components/atom/Rating.tsx](/src/components/atom/Rating.tsx) | TypeScript JSX | 31 | 0 | 7 | 38 |
| [src/components/atom/SelectDateTechButton.tsx](/src/components/atom/SelectDateTechButton.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/atom/SizeableImage/index.tsx](/src/components/atom/SizeableImage/index.tsx) | TypeScript JSX | 18 | 0 | 4 | 22 |
| [src/components/atom/Spinner.tsx](/src/components/atom/Spinner.tsx) | TypeScript JSX | 12 | 0 | 4 | 16 |
| [src/components/atom/StickyContainer.tsx](/src/components/atom/StickyContainer.tsx) | TypeScript JSX | 11 | 0 | 3 | 14 |
| [src/components/atom/SummaryPageCarItem.tsx](/src/components/atom/SummaryPageCarItem.tsx) | TypeScript JSX | 202 | 0 | 9 | 211 |
| [src/components/atom/Testimonials/AuthTestimonialCards.tsx](/src/components/atom/Testimonials/AuthTestimonialCards.tsx) | TypeScript JSX | 85 | 0 | 7 | 92 |
| [src/components/atom/Testimonials/TestimonialCard.tsx](/src/components/atom/Testimonials/TestimonialCard.tsx) | TypeScript JSX | 39 | 0 | 5 | 44 |
| [src/components/atom/Testimonials/testimonials.json](/src/components/atom/Testimonials/testimonials.json) | JSON | 120 | 0 | 0 | 120 |
| [src/components/atom/TextInput.tsx](/src/components/atom/TextInput.tsx) | TypeScript JSX | 42 | 0 | 4 | 46 |
| [src/components/atom/Texts/GrayParagraph/index.tsx](/src/components/atom/Texts/GrayParagraph/index.tsx) | TypeScript JSX | 16 | 0 | 4 | 20 |
| [src/components/atom/Texts/GrayText14.tsx](/src/components/atom/Texts/GrayText14.tsx) | TypeScript JSX | 18 | 0 | 4 | 22 |
| [src/components/atom/Texts/InstructionText/index.tsx](/src/components/atom/Texts/InstructionText/index.tsx) | TypeScript JSX | 22 | 0 | 4 | 26 |
| [src/components/atom/Texts/MenuSectionTitle/index.tsx](/src/components/atom/Texts/MenuSectionTitle/index.tsx) | TypeScript JSX | 8 | 0 | 4 | 12 |
| [src/components/atom/Texts/ModalTitle/index.tsx](/src/components/atom/Texts/ModalTitle/index.tsx) | TypeScript JSX | 16 | 0 | 4 | 20 |
| [src/components/atom/Texts/ProfileDescription/index.tsx](/src/components/atom/Texts/ProfileDescription/index.tsx) | TypeScript JSX | 16 | 0 | 3 | 19 |
| [src/components/atom/Texts/ProfileFieldTitle/index.tsx](/src/components/atom/Texts/ProfileFieldTitle/index.tsx) | TypeScript JSX | 17 | 0 | 4 | 21 |
| [src/components/atom/Texts/ProfileFieldValue/index.tsx](/src/components/atom/Texts/ProfileFieldValue/index.tsx) | TypeScript JSX | 14 | 0 | 3 | 17 |
| [src/components/atom/Texts/ProfileTitle/index.tsx](/src/components/atom/Texts/ProfileTitle/index.tsx) | TypeScript JSX | 11 | 0 | 4 | 15 |
| [src/components/atom/Texts/base/Paragraph14.tsx](/src/components/atom/Texts/base/Paragraph14.tsx) | TypeScript JSX | 15 | 0 | 4 | 19 |
| [src/components/atom/Texts/base/Text12.tsx](/src/components/atom/Texts/base/Text12.tsx) | TypeScript JSX | 15 | 0 | 4 | 19 |
| [src/components/atom/Texts/base/Text14.tsx](/src/components/atom/Texts/base/Text14.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/atom/Texts/base/Text16.tsx](/src/components/atom/Texts/base/Text16.tsx) | TypeScript JSX | 18 | 0 | 4 | 22 |
| [src/components/atom/Texts/base/Title18.tsx](/src/components/atom/Texts/base/Title18.tsx) | TypeScript JSX | 15 | 0 | 3 | 18 |
| [src/components/atom/Texts/base/Title24.tsx](/src/components/atom/Texts/base/Title24.tsx) | TypeScript JSX | 15 | 0 | 3 | 18 |
| [src/components/atom/Texts/base/Title32.tsx](/src/components/atom/Texts/base/Title32.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/atom/TimeSelect.tsx](/src/components/atom/TimeSelect.tsx) | TypeScript JSX | 32 | 0 | 5 | 37 |
| [src/components/atom/TitleWithIcon.tsx](/src/components/atom/TitleWithIcon.tsx) | TypeScript JSX | 16 | 0 | 4 | 20 |
| [src/components/molecule/AdminSubHeader/index.tsx](/src/components/molecule/AdminSubHeader/index.tsx) | TypeScript JSX | 23 | 0 | 4 | 27 |
| [src/components/molecule/CalendarHeader/index.tsx](/src/components/molecule/CalendarHeader/index.tsx) | TypeScript JSX | 52 | 0 | 4 | 56 |
| [src/components/molecule/MobileStepper/index.tsx](/src/components/molecule/MobileStepper/index.tsx) | TypeScript JSX | 15 | 0 | 4 | 19 |
| [src/components/molecule/ModalConfirmCancelBar.tsx](/src/components/molecule/ModalConfirmCancelBar.tsx) | TypeScript JSX | 42 | 0 | 4 | 46 |
| [src/components/molecule/ProfileBlockHeader/index.tsx](/src/components/molecule/ProfileBlockHeader/index.tsx) | TypeScript JSX | 28 | 0 | 4 | 32 |
| [src/components/molecule/SelectedTechInfoTab.tsx](/src/components/molecule/SelectedTechInfoTab.tsx) | TypeScript JSX | 56 | 0 | 4 | 60 |
| [src/components/molecule/ServiceCarItem.tsx](/src/components/molecule/ServiceCarItem.tsx) | TypeScript JSX | 48 | 0 | 4 | 52 |
| [src/components/molecule/StepHeader.tsx](/src/components/molecule/StepHeader.tsx) | TypeScript JSX | 22 | 0 | 4 | 26 |
| [src/components/organism/AddressModal.tsx](/src/components/organism/AddressModal.tsx) | TypeScript JSX | 107 | 0 | 8 | 115 |
| [src/components/organism/AddressSelector.tsx](/src/components/organism/AddressSelector.tsx) | TypeScript JSX | 56 | 0 | 5 | 61 |
| [src/components/organism/AdminLayout/index.tsx](/src/components/organism/AdminLayout/index.tsx) | TypeScript JSX | 63 | 0 | 4 | 67 |
| [src/components/organism/AdminSideModal/index.tsx](/src/components/organism/AdminSideModal/index.tsx) | TypeScript JSX | 19 | 0 | 4 | 23 |
| [src/components/organism/BookingInfoModal/index.tsx](/src/components/organism/BookingInfoModal/index.tsx) | TypeScript JSX | 83 | 30 | 3 | 116 |
| [src/components/organism/Calendar.tsx](/src/components/organism/Calendar.tsx) | TypeScript JSX | 581 | 18 | 20 | 619 |
| [src/components/organism/CustomerLayout/index.tsx](/src/components/organism/CustomerLayout/index.tsx) | TypeScript JSX | 121 | 0 | 4 | 125 |
| [src/components/organism/CustomerReviewModal.tsx](/src/components/organism/CustomerReviewModal.tsx) | TypeScript JSX | 76 | 8 | 6 | 90 |
| [src/components/organism/Header/index.tsx](/src/components/organism/Header/index.tsx) | TypeScript JSX | 203 | 0 | 9 | 212 |
| [src/components/organism/Header/mobile.tsx](/src/components/organism/Header/mobile.tsx) | TypeScript JSX | 37 | 15 | 5 | 57 |
| [src/components/organism/Menu/index.tsx](/src/components/organism/Menu/index.tsx) | TypeScript JSX | 15 | 0 | 4 | 19 |
| [src/components/organism/SelectCarModal/index.tsx](/src/components/organism/SelectCarModal/index.tsx) | TypeScript JSX | 225 | 2 | 16 | 243 |
| [src/components/organism/TechnicianLayout/index.tsx](/src/components/organism/TechnicianLayout/index.tsx) | TypeScript JSX | 76 | 31 | 5 | 112 |
| [src/components/organism/TechnicianReviewModal.tsx](/src/components/organism/TechnicianReviewModal.tsx) | TypeScript JSX | 40 | 0 | 7 | 47 |
| [src/components/organism/VehicleAddonEditModal/index.tsx](/src/components/organism/VehicleAddonEditModal/index.tsx) | TypeScript JSX | 146 | 0 | 15 | 161 |
| [src/components/organism/VehicleDeleteModal/index.tsx](/src/components/organism/VehicleDeleteModal/index.tsx) | TypeScript JSX | 43 | 0 | 5 | 48 |
| [src/functions/api/addon.ts](/src/functions/api/addon.ts) | TypeScript | 58 | 0 | 8 | 66 |
| [src/functions/api/availability.ts](/src/functions/api/availability.ts) | TypeScript | 92 | 0 | 8 | 100 |
| [src/functions/api/booking.ts](/src/functions/api/booking.ts) | TypeScript | 13 | 0 | 5 | 18 |
| [src/functions/api/calendar.ts](/src/functions/api/calendar.ts) | TypeScript | 25 | 0 | 4 | 29 |
| [src/functions/api/car.ts](/src/functions/api/car.ts) | TypeScript | 15 | 0 | 3 | 18 |
| [src/functions/api/request.ts](/src/functions/api/request.ts) | TypeScript | 37 | 0 | 7 | 44 |
| [src/functions/api/service.ts](/src/functions/api/service.ts) | TypeScript | 31 | 0 | 3 | 34 |
| [src/functions/api/stripe.ts](/src/functions/api/stripe.ts) | TypeScript | 7 | 1 | 3 | 11 |
| [src/functions/api/technician.ts](/src/functions/api/technician.ts) | TypeScript | 24 | 0 | 3 | 27 |
| [src/functions/api/user.ts](/src/functions/api/user.ts) | TypeScript | 49 | 1 | 4 | 54 |
| [src/functions/calendar.ts](/src/functions/calendar.ts) | TypeScript | 5 | 0 | 3 | 8 |
| [src/functions/cognito/index.ts](/src/functions/cognito/index.ts) | TypeScript | 102 | 0 | 13 | 115 |
| [src/functions/cognito/pool.ts](/src/functions/cognito/pool.ts) | TypeScript | 7 | 0 | 2 | 9 |
| [src/functions/cognito/util.ts](/src/functions/cognito/util.ts) | TypeScript | 25 | 0 | 4 | 29 |
| [src/functions/local/index.tsx](/src/functions/local/index.tsx) | TypeScript JSX | 15 | 0 | 3 | 18 |
| [src/functions/util.ts](/src/functions/util.ts) | TypeScript | 23 | 0 | 6 | 29 |
| [src/hooks/useAddresses.ts](/src/hooks/useAddresses.ts) | TypeScript | 16 | 0 | 4 | 20 |
| [src/hooks/useAvailabilities.ts](/src/hooks/useAvailabilities.ts) | TypeScript | 172 | 30 | 17 | 219 |
| [src/hooks/useCarServices/index.ts](/src/hooks/useCarServices/index.ts) | TypeScript | 84 | 0 | 15 | 99 |
| [src/hooks/useCarServices/useContinueButton.ts](/src/hooks/useCarServices/useContinueButton.ts) | TypeScript | 63 | 0 | 8 | 71 |
| [src/hooks/useReducedMotion.ts](/src/hooks/useReducedMotion.ts) | TypeScript | 17 | 0 | 9 | 26 |
| [src/hooks/useRemoteAddon.ts](/src/hooks/useRemoteAddon.ts) | TypeScript | 23 | 0 | 5 | 28 |
| [src/hooks/useRemoteService.ts](/src/hooks/useRemoteService.ts) | TypeScript | 16 | 0 | 4 | 20 |
| [src/hooks/useTechnicianBooking/index.ts](/src/hooks/useTechnicianBooking/index.ts) | TypeScript | 117 | 8 | 16 | 141 |
| [src/hooks/useTechnicianBooking/useCalendarOffsetController.ts](/src/hooks/useTechnicianBooking/useCalendarOffsetController.ts) | TypeScript | 56 | 0 | 11 | 67 |
| [src/hooks/useTechnicianSearch.ts](/src/hooks/useTechnicianSearch.ts) | TypeScript | 232 | 75 | 48 | 355 |
| [src/hooks/useTimer.ts](/src/hooks/useTimer.ts) | TypeScript | 51 | 0 | 8 | 59 |
| [src/hooks/useWindowDimensions.ts](/src/hooks/useWindowDimensions.ts) | TypeScript | 22 | 0 | 6 | 28 |
| [src/jest.setup.ts](/src/jest.setup.ts) | TypeScript | 1 | 0 | 0 | 1 |
| [src/pageComponents/availability/CalendarDatePicker.tsx](/src/pageComponents/availability/CalendarDatePicker.tsx) | TypeScript JSX | 269 | 1 | 17 | 287 |
| [src/pageComponents/availability/CarItem.tsx](/src/pageComponents/availability/CarItem.tsx) | TypeScript JSX | 15 | 0 | 4 | 19 |
| [src/pageComponents/availability/CategoryServiceItem.tsx](/src/pageComponents/availability/CategoryServiceItem.tsx) | TypeScript JSX | 25 | 0 | 4 | 29 |
| [src/pageComponents/availability/ReviewItem.tsx](/src/pageComponents/availability/ReviewItem.tsx) | TypeScript JSX | 49 | 0 | 5 | 54 |
| [src/pageComponents/availability/SelectDateTechnicianModal.tsx](/src/pageComponents/availability/SelectDateTechnicianModal.tsx) | TypeScript JSX | 97 | 0 | 8 | 105 |
| [src/pageComponents/availability/SelectedTechnicianInfo.tsx](/src/pageComponents/availability/SelectedTechnicianInfo.tsx) | TypeScript JSX | 66 | 0 | 4 | 70 |
| [src/pageComponents/availability/TechnicianItem.tsx](/src/pageComponents/availability/TechnicianItem.tsx) | TypeScript JSX | 109 | 0 | 4 | 113 |
| [src/pageComponents/availability/TechnicianList.tsx](/src/pageComponents/availability/TechnicianList.tsx) | TypeScript JSX | 64 | 0 | 4 | 68 |
| [src/pageComponents/customer_profile/AddressBlock.tsx](/src/pageComponents/customer_profile/AddressBlock.tsx) | TypeScript JSX | 166 | 0 | 7 | 173 |
| [src/pageComponents/customer_profile/EditProfileModal.tsx](/src/pageComponents/customer_profile/EditProfileModal.tsx) | TypeScript JSX | 110 | 0 | 11 | 121 |
| [src/pageComponents/customer_profile/InformationBlock.tsx](/src/pageComponents/customer_profile/InformationBlock.tsx) | TypeScript JSX | 104 | 0 | 6 | 110 |
| [src/pageComponents/customer_profile/VehicleBlock.tsx](/src/pageComponents/customer_profile/VehicleBlock.tsx) | TypeScript JSX | 78 | 0 | 10 | 88 |
| [src/pageComponents/customer_profile/VehicleItem.tsx](/src/pageComponents/customer_profile/VehicleItem.tsx) | TypeScript JSX | 91 | 0 | 4 | 95 |
| [src/pageComponents/information/AddonVehicleItem.tsx](/src/pageComponents/information/AddonVehicleItem.tsx) | TypeScript JSX | 75 | 0 | 6 | 81 |
| [src/pageComponents/information/CustomerInfo.tsx](/src/pageComponents/information/CustomerInfo.tsx) | TypeScript JSX | 107 | 0 | 8 | 115 |
| [src/pageComponents/information/PaymentBlock.tsx](/src/pageComponents/information/PaymentBlock.tsx) | TypeScript JSX | 198 | 0 | 19 | 217 |
| [src/pageComponents/information/Review.tsx](/src/pageComponents/information/Review.tsx) | TypeScript JSX | 162 | 0 | 14 | 176 |
| [src/pageComponents/information/ServiceItem.tsx](/src/pageComponents/information/ServiceItem.tsx) | TypeScript JSX | 46 | 1 | 5 | 52 |
| [src/pageComponents/information/TimeBlock.tsx](/src/pageComponents/information/TimeBlock.tsx) | TypeScript JSX | 23 | 0 | 3 | 26 |
| [src/pageComponents/select_service/AddCarButton.tsx](/src/pageComponents/select_service/AddCarButton.tsx) | TypeScript JSX | 34 | 0 | 4 | 38 |
| [src/pageComponents/select_service/AddonSelectionModal.tsx](/src/pageComponents/select_service/AddonSelectionModal.tsx) | TypeScript JSX | 105 | 0 | 7 | 112 |
| [src/pageComponents/select_service/CarItem.tsx](/src/pageComponents/select_service/CarItem.tsx) | TypeScript JSX | 72 | 0 | 3 | 75 |
| [src/pageComponents/select_service/CarMenu.tsx](/src/pageComponents/select_service/CarMenu.tsx) | TypeScript JSX | 37 | 0 | 4 | 41 |
| [src/pageComponents/select_service/MobileServiceItem.tsx](/src/pageComponents/select_service/MobileServiceItem.tsx) | TypeScript JSX | 56 | 0 | 4 | 60 |
| [src/pageComponents/select_service/ServiceItem.tsx](/src/pageComponents/select_service/ServiceItem.tsx) | TypeScript JSX | 115 | 0 | 7 | 122 |
| [src/pageComponents/select_service/ServiceTotalViewer.tsx](/src/pageComponents/select_service/ServiceTotalViewer.tsx) | TypeScript JSX | 89 | 0 | 5 | 94 |
| [src/pageComponents/select_service/ServiceVehicleItem.tsx](/src/pageComponents/select_service/ServiceVehicleItem.tsx) | TypeScript JSX | 122 | 0 | 8 | 130 |
| [src/pageComponents/select_service/Services.tsx](/src/pageComponents/select_service/Services.tsx) | TypeScript JSX | 194 | 0 | 9 | 203 |
| [src/pageComponents/tech_booking/AvailabilityBlock.tsx](/src/pageComponents/tech_booking/AvailabilityBlock.tsx) | TypeScript JSX | 35 | 0 | 4 | 39 |
| [src/pageComponents/tech_booking/AvailabilityModal.tsx](/src/pageComponents/tech_booking/AvailabilityModal.tsx) | TypeScript JSX | 232 | 0 | 11 | 243 |
| [src/pageComponents/tech_booking/AvailabilitySection.tsx](/src/pageComponents/tech_booking/AvailabilitySection.tsx) | TypeScript JSX | 38 | 12 | 4 | 54 |
| [src/pageComponents/tech_booking/CalendarSection.tsx](/src/pageComponents/tech_booking/CalendarSection.tsx) | TypeScript JSX | 41 | 0 | 6 | 47 |
| [src/pageComponents/tech_booking/CalendarSyncOptionsModal.tsx](/src/pageComponents/tech_booking/CalendarSyncOptionsModal.tsx) | TypeScript JSX | 99 | 0 | 10 | 109 |
| [src/pageComponents/technician_profile/BiographyBlock.tsx](/src/pageComponents/technician_profile/BiographyBlock.tsx) | TypeScript JSX | 68 | 1 | 8 | 77 |
| [src/pageComponents/technician_profile/CategoryBlock.tsx](/src/pageComponents/technician_profile/CategoryBlock.tsx) | TypeScript JSX | 126 | 2 | 10 | 138 |
| [src/pageComponents/technician_profile/CertificationBlock.tsx](/src/pageComponents/technician_profile/CertificationBlock.tsx) | TypeScript JSX | 47 | 0 | 5 | 52 |
| [src/pageComponents/technician_profile/ServiceAreaBlock.tsx](/src/pageComponents/technician_profile/ServiceAreaBlock.tsx) | TypeScript JSX | 76 | 0 | 7 | 83 |
| [src/pages/_app.tsx](/src/pages/_app.tsx) | TypeScript JSX | 15 | 0 | 5 | 20 |
| [src/pages/_document.tsx](/src/pages/_document.tsx) | TypeScript JSX | 30 | 0 | 4 | 34 |
| [src/pages/account.tsx](/src/pages/account.tsx) | TypeScript JSX | 97 | 0 | 5 | 102 |
| [src/pages/account_confirm.tsx](/src/pages/account_confirm.tsx) | TypeScript JSX | 70 | 0 | 6 | 76 |
| [src/pages/admin_addons/index.tsx](/src/pages/admin_addons/index.tsx) | TypeScript JSX | 237 | 0 | 14 | 251 |
| [src/pages/admin_service/index.tsx](/src/pages/admin_service/index.tsx) | TypeScript JSX | 193 | 2 | 16 | 211 |
| [src/pages/admin_technicians/index.tsx](/src/pages/admin_technicians/index.tsx) | TypeScript JSX | 212 | 1 | 5 | 218 |
| [src/pages/booking_calendar.tsx](/src/pages/booking_calendar.tsx) | TypeScript JSX | 185 | 1 | 11 | 197 |
| [src/pages/cancel.tsx](/src/pages/cancel.tsx) | TypeScript JSX | 23 | 0 | 4 | 27 |
| [src/pages/change_password/index.tsx](/src/pages/change_password/index.tsx) | TypeScript JSX | 151 | 1 | 6 | 158 |
| [src/pages/confirm.tsx](/src/pages/confirm.tsx) | TypeScript JSX | 243 | 0 | 20 | 263 |
| [src/pages/customer_booking.tsx](/src/pages/customer_booking.tsx) | TypeScript JSX | 252 | 37 | 14 | 303 |
| [src/pages/customer_profile/index.tsx](/src/pages/customer_profile/index.tsx) | TypeScript JSX | 92 | 0 | 5 | 97 |
| [src/pages/index.tsx](/src/pages/index.tsx) | TypeScript JSX | 32 | 77 | 5 | 114 |
| [src/pages/information.tsx](/src/pages/information.tsx) | TypeScript JSX | 85 | 0 | 13 | 98 |
| [src/pages/login.tsx](/src/pages/login.tsx) | TypeScript JSX | 204 | 0 | 14 | 218 |
| [src/pages/new_tech.tsx](/src/pages/new_tech.tsx) | TypeScript JSX | 119 | 0 | 7 | 126 |
| [src/pages/new_tech_confirm.tsx](/src/pages/new_tech_confirm.tsx) | TypeScript JSX | 79 | 0 | 4 | 83 |
| [src/pages/new_tech_info.tsx](/src/pages/new_tech_info.tsx) | TypeScript JSX | 221 | 0 | 14 | 235 |
| [src/pages/rating.tsx](/src/pages/rating.tsx) | TypeScript JSX | 174 | 0 | 10 | 184 |
| [src/pages/signup.tsx](/src/pages/signup.tsx) | TypeScript JSX | 264 | 0 | 7 | 271 |
| [src/pages/success.tsx](/src/pages/success.tsx) | TypeScript JSX | 25 | 0 | 4 | 29 |
| [src/pages/tech_booking.tsx](/src/pages/tech_booking.tsx) | TypeScript JSX | 231 | 17 | 16 | 264 |
| [src/pages/tech_earning.tsx](/src/pages/tech_earning.tsx) | TypeScript JSX | 71 | 0 | 3 | 74 |
| [src/pages/tech_setting.tsx](/src/pages/tech_setting.tsx) | TypeScript JSX | 9 | 0 | 3 | 12 |
| [src/pages/test_buttons.tsx](/src/pages/test_buttons.tsx) | TypeScript JSX | 26 | 0 | 5 | 31 |
| [src/pages/test_page.tsx](/src/pages/test_page.tsx) | TypeScript JSX | 38 | 0 | 4 | 42 |
| [src/pages/test_texts.tsx](/src/pages/test_texts.tsx) | TypeScript JSX | 20 | 0 | 6 | 26 |
| [src/services/AddressService.ts](/src/services/AddressService.ts) | TypeScript | 76 | 0 | 9 | 85 |
| [src/services/CalendarService.ts](/src/services/CalendarService.ts) | TypeScript | 133 | 0 | 10 | 143 |
| [src/services/OccupationService.ts](/src/services/OccupationService.ts) | TypeScript | 22 | 0 | 2 | 24 |
| [src/services/ReviewService.ts](/src/services/ReviewService.ts) | TypeScript | 33 | 0 | 5 | 38 |
| [src/services/SessionService.ts](/src/services/SessionService.ts) | TypeScript | 61 | 0 | 7 | 68 |
| [src/services/VehicleService.ts](/src/services/VehicleService.ts) | TypeScript | 33 | 0 | 5 | 38 |
| [src/templateComponents/Description.tsx](/src/templateComponents/Description.tsx) | TypeScript JSX | 11 | 0 | 4 | 15 |
| [src/theme/index.ts](/src/theme/index.ts) | TypeScript | 2 | 0 | 0 | 2 |
| [src/types/Address.ts](/src/types/Address.ts) | TypeScript | 10 | 0 | 0 | 10 |
| [src/types/Booking.ts](/src/types/Booking.ts) | TypeScript | 36 | 0 | 2 | 38 |
| [src/types/BusinessRule.test.ts](/src/types/BusinessRule.test.ts) | TypeScript | 220 | 4 | 17 | 241 |
| [src/types/BusinessRule.ts](/src/types/BusinessRule.ts) | TypeScript | 119 | 1 | 19 | 139 |
| [src/types/CalendarBlock.ts](/src/types/CalendarBlock.ts) | TypeScript | 15 | 0 | 1 | 16 |
| [src/types/Color.ts](/src/types/Color.ts) | TypeScript | 15 | 0 | 2 | 17 |
| [src/types/Customer.ts](/src/types/Customer.ts) | TypeScript | 16 | 0 | 2 | 18 |
| [src/types/GoogleCalendarBlock.ts](/src/types/GoogleCalendarBlock.ts) | TypeScript | 18 | 0 | 1 | 19 |
| [src/types/Review.ts](/src/types/Review.ts) | TypeScript | 19 | 0 | 0 | 19 |
| [src/types/Technician.ts](/src/types/Technician.ts) | TypeScript | 17 | 0 | 2 | 19 |
| [src/types/User.ts](/src/types/User.ts) | TypeScript | 15 | 0 | 1 | 16 |
| [src/types/addon.ts](/src/types/addon.ts) | TypeScript | 9 | 0 | 0 | 9 |
| [src/types/calendarTypeBooking.ts](/src/types/calendarTypeBooking.ts) | TypeScript | 18 | 1 | 2 | 21 |
| [src/types/car.ts](/src/types/car.ts) | TypeScript | 11 | 0 | 1 | 12 |
| [src/types/carServiceExtension.ts](/src/types/carServiceExtension.ts) | TypeScript | 33 | 0 | 4 | 37 |
| [src/types/category.ts](/src/types/category.ts) | TypeScript | 46 | 3 | 6 | 55 |
| [src/types/index.ts](/src/types/index.ts) | TypeScript | 91 | 0 | 8 | 99 |
| [src/types/interfaces.ts](/src/types/interfaces.ts) | TypeScript | 15 | 0 | 2 | 17 |
| [src/values/index.ts](/src/values/index.ts) | TypeScript | 52 | 0 | 3 | 55 |
| [styles/Home.module.css](/styles/Home.module.css) | CSS | 100 | 0 | 17 | 117 |
| [styles/globals.css](/styles/globals.css) | CSS | 159 | 0 | 22 | 181 |
| [tailwind.config.js](/tailwind.config.js) | JavaScript | 26 | 0 | 2 | 28 |
| [tsconfig.json](/tsconfig.json) | JSON with Comments | 20 | 0 | 1 | 21 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)