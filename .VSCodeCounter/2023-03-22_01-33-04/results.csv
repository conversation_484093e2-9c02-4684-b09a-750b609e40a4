"filename", "language", "JSON with Comments", "CSS", "JavaScript", "TypeScript", "TypeScript JSX", "Markdown", "XML", "JSON", "comment", "blank", "total"
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.eslintrc.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 4
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\.prettierrc", "JSON", 0, 0, 0, 0, 0, 0, 0, 6, 0, 1, 7
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\README.md", "Markdown", 0, 0, 0, 0, 0, 20, 0, 0, 0, 15, 35
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\auth\wheeleasyaa553eff\parameters.json", "<PERSON><PERSON><PERSON>", 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 25
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\backend-config.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 45, 0, 0, 45
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\tags.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 10, 0, 0, 10
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\backend\types\amplify-dependent-resources-ref.d.ts", "TypeScript", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\cli.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 62
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\amplify\team-provider-info.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 25
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\next-env.d.ts", "TypeScript", 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 6
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\next.config.js", "JavaScript", 0, 0, 10, 0, 0, 0, 0, 0, 1, 2, 13
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\package-lock.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 36148, 0, 1, 36149
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\package.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 89, 0, 1, 90
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\postcss.config.js", "JavaScript", 0, 0, 6, 0, 0, 0, 0, 0, 0, 1, 7
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\public\google_icon.svg", "XML", 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\public\vercel.svg", "XML", 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 4
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\hook.ts", "TypeScript", 0, 0, 0, 4, 0, 0, 0, 0, 0, 1, 5
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\index.ts", "TypeScript", 0, 0, 0, 23, 0, 0, 0, 0, 0, 3, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\index.ts", "TypeScript", 0, 0, 0, 73, 0, 0, 0, 0, 0, 5, 78
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\auth\thunks.ts", "TypeScript", 0, 0, 0, 37, 0, 0, 0, 0, 0, 6, 43
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\categoryBooking\index.ts", "TypeScript", 0, 0, 0, 45, 0, 0, 0, 0, 0, 5, 50
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\categoryBooking\selector.ts", "TypeScript", 0, 0, 0, 19, 0, 0, 0, 0, 1, 3, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\summary\index.ts", "TypeScript", 0, 0, 0, 70, 0, 0, 0, 0, 0, 10, 80
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\techBooking\index.ts", "TypeScript", 0, 0, 0, 57, 0, 0, 0, 0, 0, 6, 63
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\@redux\modules\techBooking\thunk.ts", "TypeScript", 0, 0, 0, 8, 0, 0, 0, 0, 0, 2, 10
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Layout\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 257, 0, 0, 0, 2, 12, 271
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\ConfirmCancelModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 72, 0, 0, 0, 0, 4, 76
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Modal\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 10, 0, 0, 0, 0, 3, 13
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\Stepper\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 101, 0, 0, 0, 0, 5, 106
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\VerticalStepper\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 133, 0, 0, 0, 0, 8, 141
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\AuthInput\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 31, 0, 0, 0, 0, 5, 36
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\BookingDateDisplay.tsx", "TypeScript JSX", 0, 0, 0, 0, 33, 0, 0, 0, 0, 6, 39
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\BorderSelect.tsx", "TypeScript JSX", 0, 0, 0, 0, 20, 0, 0, 0, 0, 3, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AccentTextButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 18, 0, 0, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\AuthButton\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 38, 0, 0, 0, 0, 5, 43
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundMiddleButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 33, 0, 0, 0, 0, 4, 37
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BackgroundSmallButton\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 37, 0, 0, 0, 0, 5, 42
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BorderButton\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 22, 0, 0, 0, 0, 3, 25
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\BorderSmallButton\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 24, 0, 0, 0, 0, 4, 28
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\GoogleAuthButton\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 52, 0, 0, 0, 0, 4, 56
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\MenuButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 42, 0, 0, 0, 0, 6, 48
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\MiddleButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 37, 0, 0, 0, 0, 4, 41
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\SmallButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 29, 0, 0, 0, 0, 4, 33
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Buttons\base\TextButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 20, 0, 0, 0, 0, 3, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Icons.tsx", "TypeScript JSX", 0, 0, 0, 0, 393, 0, 0, 0, 0, 17, 410
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ImageFileInput.tsx", "TypeScript JSX", 0, 0, 0, 0, 55, 0, 0, 0, 0, 10, 65
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\NoPaddingPaper.tsx", "TypeScript JSX", 0, 0, 0, 0, 19, 0, 0, 0, 0, 3, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Paper.tsx", "TypeScript JSX", 0, 0, 0, 0, 20, 0, 0, 0, 0, 4, 24
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\ProfilePlaceholder.tsx", "TypeScript JSX", 0, 0, 0, 0, 28, 0, 0, 0, 0, 5, 33
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Rating.tsx", "TypeScript JSX", 0, 0, 0, 0, 31, 0, 0, 0, 0, 7, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\SelectDateTechButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 19, 0, 0, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\SizeableImage\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 18, 0, 0, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Spinner.tsx", "TypeScript JSX", 0, 0, 0, 0, 12, 0, 0, 0, 0, 4, 16
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\StickyContainer.tsx", "TypeScript JSX", 0, 0, 0, 0, 11, 0, 0, 0, 0, 3, 14
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\SummaryPageCarItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 202, 0, 0, 0, 0, 9, 211
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Testimonials\AuthTestimonialCards.tsx", "TypeScript JSX", 0, 0, 0, 0, 85, 0, 0, 0, 0, 7, 92
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Testimonials\TestimonialCard.tsx", "TypeScript JSX", 0, 0, 0, 0, 39, 0, 0, 0, 0, 5, 44
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Testimonials\testimonials.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 120, 0, 0, 120
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TextInput.tsx", "TypeScript JSX", 0, 0, 0, 0, 42, 0, 0, 0, 0, 4, 46
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayParagraph\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 16, 0, 0, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\GrayText14.tsx", "TypeScript JSX", 0, 0, 0, 0, 18, 0, 0, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\InstructionText\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 22, 0, 0, 0, 0, 4, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\MenuSectionTitle\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 8, 0, 0, 0, 0, 4, 12
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ModalTitle\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 16, 0, 0, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileDescription\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 16, 0, 0, 0, 0, 3, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileFieldTitle\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 17, 0, 0, 0, 0, 4, 21
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileFieldValue\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 14, 0, 0, 0, 0, 3, 17
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\ProfileTitle\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 11, 0, 0, 0, 0, 4, 15
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Paragraph14.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text12.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text14.tsx", "TypeScript JSX", 0, 0, 0, 0, 19, 0, 0, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Text16.tsx", "TypeScript JSX", 0, 0, 0, 0, 18, 0, 0, 0, 0, 4, 22
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title18.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title24.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\Texts\base\Title32.tsx", "TypeScript JSX", 0, 0, 0, 0, 19, 0, 0, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TimeSelect.tsx", "TypeScript JSX", 0, 0, 0, 0, 32, 0, 0, 0, 0, 5, 37
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\atom\TitleWithIcon.tsx", "TypeScript JSX", 0, 0, 0, 0, 16, 0, 0, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\AdminSubHeader\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 23, 0, 0, 0, 0, 4, 27
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\CalendarHeader\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 52, 0, 0, 0, 0, 4, 56
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\MobileStepper\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ModalConfirmCancelBar.tsx", "TypeScript JSX", 0, 0, 0, 0, 42, 0, 0, 0, 0, 4, 46
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ProfileBlockHeader\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 28, 0, 0, 0, 0, 4, 32
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\SelectedTechInfoTab.tsx", "TypeScript JSX", 0, 0, 0, 0, 56, 0, 0, 0, 0, 4, 60
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\ServiceCarItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 48, 0, 0, 0, 0, 4, 52
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\molecule\StepHeader.tsx", "TypeScript JSX", 0, 0, 0, 0, 22, 0, 0, 0, 0, 4, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddressModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 107, 0, 0, 0, 0, 8, 115
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AddressSelector.tsx", "TypeScript JSX", 0, 0, 0, 0, 56, 0, 0, 0, 0, 5, 61
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AdminLayout\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 63, 0, 0, 0, 0, 4, 67
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\AdminSideModal\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 19, 0, 0, 0, 0, 4, 23
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\BookingInfoModal\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 83, 0, 0, 0, 30, 3, 116
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Calendar.tsx", "TypeScript JSX", 0, 0, 0, 0, 581, 0, 0, 0, 18, 20, 619
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\CustomerLayout\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 121, 0, 0, 0, 0, 4, 125
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\CustomerReviewModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 76, 0, 0, 0, 8, 6, 90
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 203, 0, 0, 0, 0, 9, 212
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Header\mobile.tsx", "TypeScript JSX", 0, 0, 0, 0, 37, 0, 0, 0, 15, 5, 57
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\Menu\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\SelectCarModal\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 225, 0, 0, 0, 2, 16, 243
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TechnicianLayout\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 76, 0, 0, 0, 31, 5, 112
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\TechnicianReviewModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 40, 0, 0, 0, 0, 7, 47
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\VehicleAddonEditModal\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 146, 0, 0, 0, 0, 15, 161
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\components\organism\VehicleDeleteModal\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 43, 0, 0, 0, 0, 5, 48
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\addon.ts", "TypeScript", 0, 0, 0, 58, 0, 0, 0, 0, 0, 8, 66
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\availability.ts", "TypeScript", 0, 0, 0, 92, 0, 0, 0, 0, 0, 8, 100
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\booking.ts", "TypeScript", 0, 0, 0, 13, 0, 0, 0, 0, 0, 5, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\calendar.ts", "TypeScript", 0, 0, 0, 25, 0, 0, 0, 0, 0, 4, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\car.ts", "TypeScript", 0, 0, 0, 15, 0, 0, 0, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\request.ts", "TypeScript", 0, 0, 0, 37, 0, 0, 0, 0, 0, 7, 44
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\service.ts", "TypeScript", 0, 0, 0, 31, 0, 0, 0, 0, 0, 3, 34
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\stripe.ts", "TypeScript", 0, 0, 0, 7, 0, 0, 0, 0, 1, 3, 11
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\technician.ts", "TypeScript", 0, 0, 0, 24, 0, 0, 0, 0, 0, 3, 27
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\api\user.ts", "TypeScript", 0, 0, 0, 49, 0, 0, 0, 0, 1, 4, 54
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\calendar.ts", "TypeScript", 0, 0, 0, 5, 0, 0, 0, 0, 0, 3, 8
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\index.ts", "TypeScript", 0, 0, 0, 102, 0, 0, 0, 0, 0, 13, 115
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\pool.ts", "TypeScript", 0, 0, 0, 7, 0, 0, 0, 0, 0, 2, 9
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\cognito\util.ts", "TypeScript", 0, 0, 0, 25, 0, 0, 0, 0, 0, 4, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\local\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 3, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\functions\util.ts", "TypeScript", 0, 0, 0, 23, 0, 0, 0, 0, 0, 6, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAddresses.ts", "TypeScript", 0, 0, 0, 16, 0, 0, 0, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useAvailabilities.ts", "TypeScript", 0, 0, 0, 172, 0, 0, 0, 0, 30, 17, 219
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCarServices\index.ts", "TypeScript", 0, 0, 0, 84, 0, 0, 0, 0, 0, 15, 99
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useCarServices\useContinueButton.ts", "TypeScript", 0, 0, 0, 63, 0, 0, 0, 0, 0, 8, 71
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useReducedMotion.ts", "TypeScript", 0, 0, 0, 17, 0, 0, 0, 0, 0, 9, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useRemoteAddon.ts", "TypeScript", 0, 0, 0, 23, 0, 0, 0, 0, 0, 5, 28
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useRemoteService.ts", "TypeScript", 0, 0, 0, 16, 0, 0, 0, 0, 0, 4, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianBooking\index.ts", "TypeScript", 0, 0, 0, 117, 0, 0, 0, 0, 8, 16, 141
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianBooking\useCalendarOffsetController.ts", "TypeScript", 0, 0, 0, 56, 0, 0, 0, 0, 0, 11, 67
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTechnicianSearch.ts", "TypeScript", 0, 0, 0, 232, 0, 0, 0, 0, 75, 48, 355
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useTimer.ts", "TypeScript", 0, 0, 0, 51, 0, 0, 0, 0, 0, 8, 59
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\hooks\useWindowDimensions.ts", "TypeScript", 0, 0, 0, 22, 0, 0, 0, 0, 0, 6, 28
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\jest.setup.ts", "TypeScript", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CalendarDatePicker.tsx", "TypeScript JSX", 0, 0, 0, 0, 269, 0, 0, 0, 1, 17, 287
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CarItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 4, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\CategoryServiceItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 25, 0, 0, 0, 0, 4, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\ReviewItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 49, 0, 0, 0, 0, 5, 54
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SelectDateTechnicianModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 97, 0, 0, 0, 0, 8, 105
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\SelectedTechnicianInfo.tsx", "TypeScript JSX", 0, 0, 0, 0, 66, 0, 0, 0, 0, 4, 70
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 109, 0, 0, 0, 0, 4, 113
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\availability\TechnicianList.tsx", "TypeScript JSX", 0, 0, 0, 0, 64, 0, 0, 0, 0, 4, 68
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\AddressBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 166, 0, 0, 0, 0, 7, 173
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\EditProfileModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 110, 0, 0, 0, 0, 11, 121
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\InformationBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 104, 0, 0, 0, 0, 6, 110
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 78, 0, 0, 0, 0, 10, 88
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\customer_profile\VehicleItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 91, 0, 0, 0, 0, 4, 95
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\AddonVehicleItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 75, 0, 0, 0, 0, 6, 81
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\CustomerInfo.tsx", "TypeScript JSX", 0, 0, 0, 0, 107, 0, 0, 0, 0, 8, 115
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\PaymentBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 198, 0, 0, 0, 0, 19, 217
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\Review.tsx", "TypeScript JSX", 0, 0, 0, 0, 162, 0, 0, 0, 0, 14, 176
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\ServiceItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 46, 0, 0, 0, 1, 5, 52
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\information\TimeBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 23, 0, 0, 0, 0, 3, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\AddCarButton.tsx", "TypeScript JSX", 0, 0, 0, 0, 34, 0, 0, 0, 0, 4, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\AddonSelectionModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 105, 0, 0, 0, 0, 7, 112
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 72, 0, 0, 0, 0, 3, 75
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\CarMenu.tsx", "TypeScript JSX", 0, 0, 0, 0, 37, 0, 0, 0, 0, 4, 41
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\MobileServiceItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 56, 0, 0, 0, 0, 4, 60
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 115, 0, 0, 0, 0, 7, 122
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceTotalViewer.tsx", "TypeScript JSX", 0, 0, 0, 0, 89, 0, 0, 0, 0, 5, 94
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\ServiceVehicleItem.tsx", "TypeScript JSX", 0, 0, 0, 0, 122, 0, 0, 0, 0, 8, 130
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\select_service\Services.tsx", "TypeScript JSX", 0, 0, 0, 0, 194, 0, 0, 0, 0, 9, 203
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 35, 0, 0, 0, 0, 4, 39
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilityModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 232, 0, 0, 0, 0, 11, 243
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\AvailabilitySection.tsx", "TypeScript JSX", 0, 0, 0, 0, 38, 0, 0, 0, 12, 4, 54
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\CalendarSection.tsx", "TypeScript JSX", 0, 0, 0, 0, 41, 0, 0, 0, 0, 6, 47
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\tech_booking\CalendarSyncOptionsModal.tsx", "TypeScript JSX", 0, 0, 0, 0, 99, 0, 0, 0, 0, 10, 109
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\BiographyBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 68, 0, 0, 0, 1, 8, 77
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\CategoryBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 126, 0, 0, 0, 2, 10, 138
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\CertificationBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 47, 0, 0, 0, 0, 5, 52
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pageComponents\technician_profile\ServiceAreaBlock.tsx", "TypeScript JSX", 0, 0, 0, 0, 76, 0, 0, 0, 0, 7, 83
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_app.tsx", "TypeScript JSX", 0, 0, 0, 0, 15, 0, 0, 0, 0, 5, 20
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\_document.tsx", "TypeScript JSX", 0, 0, 0, 0, 30, 0, 0, 0, 0, 4, 34
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\account.tsx", "TypeScript JSX", 0, 0, 0, 0, 97, 0, 0, 0, 0, 5, 102
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\account_confirm.tsx", "TypeScript JSX", 0, 0, 0, 0, 70, 0, 0, 0, 0, 6, 76
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_addons\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 237, 0, 0, 0, 0, 14, 251
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_service\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 193, 0, 0, 0, 2, 16, 211
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\admin_technicians\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 212, 0, 0, 0, 1, 5, 218
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\booking_calendar.tsx", "TypeScript JSX", 0, 0, 0, 0, 185, 0, 0, 0, 1, 11, 197
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\cancel.tsx", "TypeScript JSX", 0, 0, 0, 0, 23, 0, 0, 0, 0, 4, 27
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\change_password\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 151, 0, 0, 0, 1, 6, 158
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\confirm.tsx", "TypeScript JSX", 0, 0, 0, 0, 243, 0, 0, 0, 0, 20, 263
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_booking.tsx", "TypeScript JSX", 0, 0, 0, 0, 252, 0, 0, 0, 37, 14, 303
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\customer_profile\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 92, 0, 0, 0, 0, 5, 97
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\index.tsx", "TypeScript JSX", 0, 0, 0, 0, 32, 0, 0, 0, 77, 5, 114
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\information.tsx", "TypeScript JSX", 0, 0, 0, 0, 85, 0, 0, 0, 0, 13, 98
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\login.tsx", "TypeScript JSX", 0, 0, 0, 0, 204, 0, 0, 0, 0, 14, 218
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech.tsx", "TypeScript JSX", 0, 0, 0, 0, 119, 0, 0, 0, 0, 7, 126
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech_confirm.tsx", "TypeScript JSX", 0, 0, 0, 0, 79, 0, 0, 0, 0, 4, 83
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\new_tech_info.tsx", "TypeScript JSX", 0, 0, 0, 0, 221, 0, 0, 0, 0, 14, 235
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\rating.tsx", "TypeScript JSX", 0, 0, 0, 0, 174, 0, 0, 0, 0, 10, 184
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\signup.tsx", "TypeScript JSX", 0, 0, 0, 0, 264, 0, 0, 0, 0, 7, 271
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\success.tsx", "TypeScript JSX", 0, 0, 0, 0, 25, 0, 0, 0, 0, 4, 29
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_booking.tsx", "TypeScript JSX", 0, 0, 0, 0, 231, 0, 0, 0, 17, 16, 264
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_earning.tsx", "TypeScript JSX", 0, 0, 0, 0, 71, 0, 0, 0, 0, 3, 74
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\tech_setting.tsx", "TypeScript JSX", 0, 0, 0, 0, 9, 0, 0, 0, 0, 3, 12
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_buttons.tsx", "TypeScript JSX", 0, 0, 0, 0, 26, 0, 0, 0, 0, 5, 31
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_page.tsx", "TypeScript JSX", 0, 0, 0, 0, 38, 0, 0, 0, 0, 4, 42
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\pages\test_texts.tsx", "TypeScript JSX", 0, 0, 0, 0, 20, 0, 0, 0, 0, 6, 26
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\AddressService.ts", "TypeScript", 0, 0, 0, 76, 0, 0, 0, 0, 0, 9, 85
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\CalendarService.ts", "TypeScript", 0, 0, 0, 133, 0, 0, 0, 0, 0, 10, 143
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\OccupationService.ts", "TypeScript", 0, 0, 0, 22, 0, 0, 0, 0, 0, 2, 24
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\ReviewService.ts", "TypeScript", 0, 0, 0, 33, 0, 0, 0, 0, 0, 5, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\SessionService.ts", "TypeScript", 0, 0, 0, 61, 0, 0, 0, 0, 0, 7, 68
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\services\VehicleService.ts", "TypeScript", 0, 0, 0, 33, 0, 0, 0, 0, 0, 5, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\templateComponents\Description.tsx", "TypeScript JSX", 0, 0, 0, 0, 11, 0, 0, 0, 0, 4, 15
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\theme\index.ts", "TypeScript", 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 2
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Address.ts", "TypeScript", 0, 0, 0, 10, 0, 0, 0, 0, 0, 0, 10
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Booking.ts", "TypeScript", 0, 0, 0, 36, 0, 0, 0, 0, 0, 2, 38
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\BusinessRule.test.ts", "TypeScript", 0, 0, 0, 220, 0, 0, 0, 0, 4, 17, 241
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\BusinessRule.ts", "TypeScript", 0, 0, 0, 119, 0, 0, 0, 0, 1, 19, 139
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\CalendarBlock.ts", "TypeScript", 0, 0, 0, 15, 0, 0, 0, 0, 0, 1, 16
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Color.ts", "TypeScript", 0, 0, 0, 15, 0, 0, 0, 0, 0, 2, 17
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Customer.ts", "TypeScript", 0, 0, 0, 16, 0, 0, 0, 0, 0, 2, 18
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\GoogleCalendarBlock.ts", "TypeScript", 0, 0, 0, 18, 0, 0, 0, 0, 0, 1, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Review.ts", "TypeScript", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\Technician.ts", "TypeScript", 0, 0, 0, 17, 0, 0, 0, 0, 0, 2, 19
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\User.ts", "TypeScript", 0, 0, 0, 15, 0, 0, 0, 0, 0, 1, 16
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\addon.ts", "TypeScript", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 9
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\calendarTypeBooking.ts", "TypeScript", 0, 0, 0, 18, 0, 0, 0, 0, 1, 2, 21
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\car.ts", "TypeScript", 0, 0, 0, 11, 0, 0, 0, 0, 0, 1, 12
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\carServiceExtension.ts", "TypeScript", 0, 0, 0, 33, 0, 0, 0, 0, 0, 4, 37
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\category.ts", "TypeScript", 0, 0, 0, 46, 0, 0, 0, 0, 3, 6, 55
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\index.ts", "TypeScript", 0, 0, 0, 91, 0, 0, 0, 0, 0, 8, 99
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\types\interfaces.ts", "TypeScript", 0, 0, 0, 15, 0, 0, 0, 0, 0, 2, 17
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\src\values\index.ts", "TypeScript", 0, 0, 0, 52, 0, 0, 0, 0, 0, 3, 55
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\Home.module.css", "CSS", 0, 100, 0, 0, 0, 0, 0, 0, 0, 17, 117
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\styles\globals.css", "CSS", 0, 159, 0, 0, 0, 0, 0, 0, 0, 22, 181
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tailwind.config.js", "JavaScript", 0, 0, 26, 0, 0, 0, 0, 0, 0, 2, 28
"c:\Users\<USER>\Desktop\Desktop\desk\luxe\wheeleasy_web\tsconfig.json", "JSON with Comments", 20, 0, 0, 0, 0, 0, 0, 0, 0, 1, 21
"Total", "-", 20, 259, 42, 2855, 11326, 20, 5, 36533, 389, 1340, 52789