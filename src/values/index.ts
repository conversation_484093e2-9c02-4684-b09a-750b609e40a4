import moment from "moment";

export const API_URL = process.env.NEXT_PUBLIC_API_URL;

export let hourList: string[] = [];
export const DURATION_UNIT = 15;

let tmp = moment("00:00", "HH:mm");
for (let i = 0; i < 24 * (60 / DURATION_UNIT); i++) {
  hourList.push(tmp.format("HH:mm"));
  tmp.add(DURATION_UNIT, "minutes");
}

export const START_TIME = "08:00";
export const END_TIME = "18:00";

export const TRAVEL_FEE = 1000;
export const TAX_RATE = 0.13;
export const PRICE_PLACEHOLDER: string = "$0.00";
export const RATE_OF_DISCOUNT: number = 10;

export const XL = 1280;
export const LG = 1024;
export const MD = 768;

export const XS = 430;
export const XXS = 375;
export const XXXS = 320;
