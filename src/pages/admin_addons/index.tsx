import { useEffect, useMemo, useState } from "react";
import TextButton from "../../components/atom/Buttons/base/TextButton";
import GrayText14 from "../../components/atom/Texts/GrayText14";
import AdminSubHeader from "../../components/molecule/AdminSubHeader";
import AdminLayout from "../../components/organism/AdminLayout";
import AdminSideModal from "../../components/organism/AdminSideModal";
import {
  apiCreateAddon,
  apiDeleteAddon,
  apiListAddons,
  apiUpdateAddon,
} from "../../functions/api/addon";
import { Addon } from "../../types/Addon";
import { Color } from "../../types/Color";

const AdminAddonsPage = () => {
  const [addons, setAddons] = useState<Addon[]>();
  const [nameInput, setNameInput] = useState<string>("");
  const [costInput, setCostInput] = useState<string>("");
  const [hoveredAddonIndex, setHoveredAddonIndex] = useState<number>(-1);

  const [isAddModalOn, setIsAddModalOn] = useState<boolean>(false);
  const [editingAddon, setEditingAddon] = useState<Addon | undefined>();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchAddons = async () => {
      const result = await apiListAddons();
      setAddons(result);
    };
    fetchAddons();
  }, []);

  const isSubmittable = useMemo(() => {
    return nameInput !== "" && costInput !== "" && !isNaN(parseInt(costInput));
  }, [nameInput, costInput]);

  async function handleOnClickModalCreate() {
    setIsLoading(true);
    const result = await apiCreateAddon(nameInput, parseInt(costInput));
    setAddons((prev) => prev?.concat(result));
    resetInputValues();
    setIsAddModalOn(false);
    setIsLoading(false);
  }

  async function handleOnClickModalEdit(id: number, name: string, cost: string) {
    setIsLoading(true);
    const result = await apiUpdateAddon(id, name, parseInt(cost));
    setAddons((prev) =>
      prev?.map((item) =>
        item.id === id
          ? {
              ...item,
              ...result,
            }
          : item
      )
    );
    resetInputValues();
    setEditingAddon(undefined);
    setIsLoading(false);
  }

  function handleOnClickCreate() {
    resetInputValues();
    setEditingAddon(undefined);
    setIsAddModalOn(true);
  }

  function handleOnClickEdit(item: Addon) {
    setIsAddModalOn(false);
    resetInputValues();
    setEditingAddon(item);
    setNameInput(item.name);
    setCostInput(item.cost.toString());
  }

  function resetInputValues() {
    setNameInput("");
    setCostInput("");
  }

  return (
    <AdminLayout>
      <div className="flex flex-col items-stretch flex-1 px-10 mt-10">
        <AdminSubHeader title="Addons" onClickCreate={handleOnClickCreate} />
        <div className="flex bg-[#f9fbfc] py-3 border-t border-b">
          <span
            className="text-sm px-3"
            style={{
              flex: 1,
            }}
          >
            Name
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 1.5,
            }}
          >
            Cost
          </span>
          <div
            style={{
              flex: 0.2,
              paddingRight: 20,
            }}
          />
        </div>
        {addons?.map((item, index) => (
          <div
            className="flex items-center py-3 border-b"
            style={{
              backgroundColor: hoveredAddonIndex === index ? Color.GRAY : undefined,
            }}
            key={index}
            onMouseOver={() => setHoveredAddonIndex(index)}
            onMouseOut={() => setHoveredAddonIndex(-1)}
          >
            <span
              className="text-sm px-3"
              style={{
                flex: 1,
              }}
            >
              {item.name}
            </span>
            <p
              className="text-sm px-3"
              style={{
                flex: 1.5,
              }}
            >
              ${(item.cost / 100).toFixed(2)}
            </p>
            <div
              className="flex justify-end pr-5 space-x-4"
              style={{
                flex: 0.2,
              }}
            >
              {hoveredAddonIndex === index ? (
                <>
                  <TextButton title="Edit" onClick={() => handleOnClickEdit(item)} />
                  <TextButton
                    title="Delete"
                    onClick={async () => {
                      if (confirm(`Are you sure to delete ${item.name}?`)) {
                        const { id } = await apiDeleteAddon(item.id);
                        setAddons((prev) => prev?.filter((item) => item.id !== id));
                      }
                    }}
                  />
                </>
              ) : undefined}
            </div>
          </div>
        ))}
      </div>
      <AdminSideModal isOn={isAddModalOn}>
        <div className="flex flex-col">
          <div className="flex justify-between mb-5 items-center">
            <h2 className="text-2xl font-bold">Create Addon</h2>
            <button
              onClick={() => {
                setNameInput("");
                setCostInput("");
                setIsAddModalOn(false);
              }}
            >
              Close
            </button>
          </div>
          <GrayText14>Name</GrayText14>
          <input
            className="border px-4 py-2 mb-5"
            value={nameInput}
            onChange={(event) => setNameInput(event.target.value)}
          />
          <GrayText14>Cost</GrayText14>
          <input
            className="border px-4 py-2 mb-5"
            value={costInput}
            onChange={(event) => setCostInput(event.target.value)}
          />
        </div>
        <button
          disabled={!isSubmittable || isLoading}
          className="h-12 text-white"
          style={{
            backgroundColor: !isSubmittable || isLoading ? Color.BLACK_04 : Color.ACCENT,
          }}
          onClick={handleOnClickModalCreate}
        >
          {isLoading ? "Loading" : "Create"}
        </button>
      </AdminSideModal>

      <AdminSideModal isOn={editingAddon !== undefined}>
        <div className="flex flex-col">
          <div className="flex justify-between mb-5 items-center">
            <h2 className="text-2xl font-bold">Edit Addon</h2>
            <button
              onClick={() => {
                setNameInput("");
                setCostInput("");
                setEditingAddon(undefined);
              }}
            >
              Close
            </button>
          </div>

          <GrayText14>Name (prev : {editingAddon?.name})</GrayText14>
          <input
            className="border px-4 py-2 mb-5"
            value={nameInput}
            onChange={(event) => setNameInput(event.target.value)}
          />
          <GrayText14>
            Cost (prev : ${editingAddon ? (editingAddon.cost / 100).toFixed(2) : ""})
          </GrayText14>
          <input
            className="border px-4 py-2 mb-5"
            value={costInput}
            onChange={(event) => setCostInput(event.target.value)}
          />
        </div>
        <button
          disabled={!isSubmittable || isLoading}
          className="h-12 text-white"
          style={{
            backgroundColor: !isSubmittable || isLoading ? Color.BLACK_04 : Color.ACCENT,
          }}
          onClick={() => {
            if (editingAddon) {
              handleOnClickModalEdit(editingAddon.id, nameInput, costInput);
            }
          }}
        >
          {isLoading ? "Loading" : "Edit"}
        </button>
      </AdminSideModal>
    </AdminLayout>
  );
};

export default AdminAddonsPage;
