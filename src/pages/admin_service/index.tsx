import imageCompression from "browser-image-compression";
import React, { useEffect, useMemo, useRef, useState } from "react";
import AdminSubHeader from "../../components/molecule/AdminSubHeader";
import AdminLayout from "../../components/organism/AdminLayout";
import AdminSideModal from "../../components/organism/AdminSideModal";
import { apiCreateService, apiListServices } from "../../functions/api/service";
import { getCurrentUser } from "../../functions/cognito/util";
import { Service } from "../../types";
import { Color } from "../../types/Color";

const AdminServicePage = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [services, setServices] = useState<Service[]>();

  const [isAddModalOn, setIsAddModalOn] = useState<boolean>(false);
  const inputRef = useRef<any>(null);

  const [nameInput, setNameInput] = useState<string>("");
  const [descriptionInput, setDescriptionInput] = useState<string>("");
  const [priceInput, setPriceInput] = useState<string>("");

  const [uploadedImage, setUploadedImage] = useState<string>();
  const [uploadedFileName, setUploadedFileName] = useState<string>();

  useEffect(() => {
    fetchServices();
  }, []);

  async function fetchServices() {
    const result = await apiListServices();
    setServices(result);
  }

  async function createService() {
    const result = await apiCreateService(
      nameInput,
      parseInt(priceInput),
      descriptionInput,
      uploadedImage!
    );
    setServices((prev) => prev?.concat(result));
  }

  async function handleImageUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const imageFile = event.target.files![0];
    setUploadedFileName(inputRef.current.files[0].name);

    try {
      const encodedFile = await imageCompression.getDataUrlFromFile(imageFile);
      setUploadedImage(encodedFile);
    } catch (error) {
      console.error(error);
    }
  }

  async function handleOnClickButton() {
    const currentUser = await getCurrentUser();
    if (currentUser !== null) {
      // setIsLoading(true)
      await createService();
      // setIsLoading(false)

      setNameInput("");
      setPriceInput("");
      setDescriptionInput("");
      setUploadedFileName(undefined);
      setUploadedImage(undefined);
      setIsAddModalOn(false);
    } else {
      alert("not logged in");
    }
  }

  const isSubmittable = useMemo(() => {
    return (
      nameInput !== "" &&
      descriptionInput !== "" &&
      priceInput !== "" &&
      uploadedImage !== undefined
    );
  }, [nameInput, descriptionInput, priceInput, uploadedImage]);

  return (
    <AdminLayout>
      <div className="flex flex-col items-stretch flex-1 px-10 mt-10">
        <AdminSubHeader
          title="Services"
          onClickCreate={() => {
            setIsAddModalOn((prev) => !prev);
          }}
        />
        <div className="flex bg-[#f9fbfc] py-3 border-t border-b">
          <span
            className="text-sm px-3"
            style={{
              flex: 1,
            }}
          >
            Name
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 1.5,
            }}
          >
            Description
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 1.5,
            }}
          >
            Image
          </span>
        </div>
        {services?.map((item) => (
          <div className="flex items-center py-3 border-b">
            <span
              className="text-sm px-3"
              style={{
                flex: 1,
              }}
            >
              {item.name}
            </span>
            <p
              className="text-sm"
              style={{
                flex: 1.5,
              }}
            >
              {item.description}
            </p>
            <div
              style={{
                flex: 1.5,
              }}
            >
              <div />
            </div>
          </div>
        ))}
      </div>
      <AdminSideModal isOn={isAddModalOn}>
        <div className="flex flex-col">
          <div className="flex justify-between mb-5 items-center">
            <h2 className="text-2xl font-bold">Create Service</h2>
            <button>Close</button>
          </div>
          <span className="text-sm text-[rgba(0,0,0,0.6)]">Name</span>
          <input
            className="border px-4 py-2 mb-5"
            value={nameInput}
            onChange={(event) => setNameInput(event.target.value)}
          />
          <span className="text-sm text-[rgba(0,0,0,0.6)]">Price</span>
          <input
            className="border px-4 py-2 mb-5"
            type="number"
            value={priceInput}
            onChange={(event) => setPriceInput(event.target.value)}
          />
          <span className="text-sm text-[rgba(0,0,0,0.6)]">Description</span>
          <textarea
            className="border px-4 py-2 mb-8"
            value={descriptionInput}
            onChange={(event) => setDescriptionInput(event.target.value)}
          />

          <span className="text-sm text-[rgba(0,0,0,0.6)] mb-2">Image</span>
          {uploadedImage ? (
            <div className="flex flex-col">
              <img src={uploadedImage} />
              <span className="mb-4">{uploadedFileName}</span>
            </div>
          ) : undefined}
          <div className="mb-10">
            <label
              className="flex cursor-pointer bg-black justify-center items-center w-28 h-10 rounded-sm"
              htmlFor="file-input"
            >
              <span className="text-white">Upload</span>
            </label>
            <input
              id="file-input"
              ref={inputRef}
              type="file"
              className="hidden"
              onChange={handleImageUpload}
            />
          </div>
        </div>
        <button
          disabled={!isSubmittable || isLoading}
          className="h-12 text-white"
          style={{
            backgroundColor: isLoading ? Color.GRAY : Color.ACCENT,
          }}
          onClick={handleOnClickButton}
        >
          Create
        </button>
      </AdminSideModal>
    </AdminLayout>
  );
};

export default AdminServicePage;
