import Image from "next/image";
import { useRouter } from "next/router";
import React from "react";
import Title24 from "../components/atom/Texts/base/Title24";
import Layout from "../components/Layout";
import { Color } from "../types/Color";

const NewTechConfirmPage = () => {
  const router = useRouter();

  return (
    <Layout>
      <Title24>Create Account</Title24>
      <div className="flex mt-36 self-stretch justify-between px-10">
        <div className="flex flex-col mr-10">
          <div
            className="flex justify-center items-center mb-7"
            style={{
              width: 120,
            }}
          >
            <Image src={require("../../public/black_logo.png")} />
          </div>
          <span className="text-[rgba(0,0,0,0.4)]">
            Are you a technician who knows how to swap tires or do oil changes?
          </span>
          <h4 className="text-2xl mt-1 mb-3">
            Welcome to Wheel Easy! Your account will be reviewed by our team to get you certified.
          </h4>
          <span
            style={{
              color: Color.ACCENT,
            }}
          >
            Typically takes 2-3 business days to process.
          </span>
        </div>
        <div
          className="flex flex-col justify-between p-10 rounded-sm bg-[#F2F3F4]"
          style={{ width: 477, height: 391 }}
        >
          <div className="flex flex-col space-y-3">
            <h3 className="text-2xl font-medium">Account Status</h3>
            <div className="flex items-center ">
              <div className="flex justify-center items-center w-5 h-5 mr-2">
                <Image src={require("../../public/check.png")} />
              </div>
              <span>Account is created.</span>
            </div>
            <div className="flex items-center ">
              <div className="flex justify-center items-center w-5 h-5 mr-2">
                <Image src={require("../../public/check.png")} />
              </div>
              <span>The team is reviewing your account.</span>
            </div>
            <div className="flex items-center ">
              <div className="flex justify-center items-center w-5 h-5 mr-2">
                <Image src={require("../../public/check.png")} />
              </div>
              <span>Your account has been approved.</span>
            </div>
            <div className="flex items-center ">
              <div className="flex justify-center items-center w-5 h-5 mr-2">
                <Image src={require("../../public/check.png")} />
              </div>
              <span>You are certified.</span>
            </div>
          </div>
          <button
            className="bg-black rounded-lg text-white py-3"
            onClick={() => {
              router.push("/booking");
            }}
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    </Layout>
  );
};

export default NewTechConfirmPage;
