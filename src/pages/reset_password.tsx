import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useMemo, useRef, useState } from "react";
import AuthInput from "../components/atom/AuthInput";
import AuthButton from "../components/atom/Buttons/AuthButton";
import SizeableImage from "../components/atom/SizeableImage";
import AuthTestimonialCards from "../components/atom/Testimonials/AuthTestimonialCards";
import { apiCompleteNewPassword, apiResetPassword } from "../functions/cognito";
import usePassword from "../hooks/usePasswordValidator";
import ErrorAlertModal from "../pageComponents/booking_date/ErrorAlertModal";
import { CustomError } from "../types/CustomError";
import { Auth } from "aws-amplify";

const ResetPasswordPage = () => {
  const router = useRouter();

  // useEffect(() => {
  //   const pathSplit = router.asPath.split("?");

  //   if (pathSplit.length > 1) {
  //     const query = pathSplit[1];
  //     const querySplit = query.split("=");

  //     if (querySplit.length > 1) {
  //       const username = querySplit[1];
  //       setUsername(decodeURIComponent(username));
  //     }
  //   }
  // }, []);

  useEffect(() => {
    if (router.query.session) {
      setSession(router.query.session as string);
    }
  }, [router.query.session]);

  const [username, setUsername] = useState<string>(router.query.username as string);
  const [session, setSession] = useState<string | null>(router.query.session as string | null);
  const [tmpPwd, setTmpPwd] = useState<string | null>(router.query.tmpPwd as string | null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [code, setCode] = useState<string>("");
  const [codeError, setCodeError] = useState<string>("");
  const [isErrorAlertModalOn, setIsErrorAlertModalOn] = useState<boolean>(false);
  const [serverErrorMsg, setServerErrorMsg] = useState<string>("");
  const isLoadingRef = useRef(false);

  const passwordHook = usePassword();

  async function handleOnClickResetPassword() {
    if (isLoadingRef.current) {
      return;
    }

    const _verifyLenOfCode = () => {
      if (code.length !== 6) {
        throw new CustomError("InvalidCodeException", "The verfication code length is incorrect");
      }
    };

    const _verifyPassword = () => {
      const isValid = passwordHook.validatePassword();
      if (!isValid) {
        throw new CustomError("InvalidPasswordException", passwordHook.passwordErrorMsg);
      }
    };

    setIsLoading(true);
    isLoadingRef.current = true;

    try {
      if (session && username && tmpPwd) {
        // Handle NEW_PASSWORD_REQUIRED flow
        await apiCompleteNewPassword(session, username, passwordHook.pwd, tmpPwd);
      } else {
        _verifyPassword();
        _verifyLenOfCode();
        await apiResetPassword(username, code, passwordHook.pwd);
      }
      await router.push({
        pathname: "/login",
        query: {
          username,
        },
      });
    } catch (error: any) {
      _handleError(error);
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }

  function _handleError(error: any) {
    if (error.name === "InvalidCodeException") {
      setCodeError("Invalid code");
    } else if (error.name === "InvalidPasswordException") {
      //This exception is handled in usePassword().
    } else if (error.name === "CodeMismatchException") {
      setCodeError("Invalid code");
    } else if (error.name === "LimitExceededException") {
      setIsErrorAlertModalOn(true);
      setServerErrorMsg(error.message);
    } else {
      console.log(error);
      setIsErrorAlertModalOn(true);
      setServerErrorMsg("Something went wrong, please try again later");
    }
  }

  function _resetError() {
    setCodeError("");
    passwordHook.resetPasswordMsg();
  }

  const canContinue = useMemo(() => {
    if (session) {
      return passwordHook.canContinue;
    } else {
      return code !== "" && passwordHook.canContinue;
    }
  }, [code, passwordHook.canContinue]);

  return (
    <div className="flex flex-col xl:flex-row items-center justify-between h-full select-none overflow-x-hidden xl:overflow-hidden min-w-[320px]">
      <Head>
        <title>Reset Password - Wheel Easy</title>
      </Head>
      <ErrorAlertModal
        isOn={isErrorAlertModalOn}
        onOff={() => {
          _resetError();
          setIsErrorAlertModalOn(false);
        }}
        title="Server Error"
        description={serverErrorMsg}
      />
      <div className="hidden xl:flex h-full">
        <AuthTestimonialCards />
      </div>
      <div className="flex grow flex-col justify-between h-full w-full items-start -mt-5 px-5 py-10 xl:px-14">
        <div />
        <div className="flex flex-col justify-center items-start mt-5 w-full xl:w-[400px]">
          <h1 className="text-3xl mb-2 font-medium text-start">Reset your password</h1>
          <p className="text-[#5A6068] text-start font-light mb-4">
            {session
              ? "Enter a new password for your account."
              : "Enter the 6-digit code you've received and the new password for this account."}
          </p>
          <p className="text-[#5A6068] text-start font-light">
            If you didn't recieve an email, please call +1 (519) 872-7202 for assistance.
          </p>
          <div style={{ height: 40 }} />
          <div className="flex flex-col items-stretch xl:w-[400px] w-full mb-5">
            {!session && (
              <div className="flex flex-col items-stretch xl:w-[400px] w-full mb-5">
                <label className="text-sm">Verification Code</label>
                <div className="mb-3">
                  <div className="relative flex flex-col items-stretch justify-center">
                    <input
                      type="text"
                      pattern="[0-9]{6}"
                      maxLength={6}
                      inputMode="numeric"
                      placeholder="6-digit code"
                      value={code}
                      onChange={(event) => {
                        if (/^[0-9]*$/.test(event.target.value)) {
                          setCode(event.target.value);
                        }
                      }}
                      className={`border px-3.5 py-2.5 placeholder-[rgba(0,0,0,0.3)] focus:outline-[#FCA311] rounded-xl drop-shadow-sm ${
                        codeError.length > 0 ? "border-[#FDA29B]" : ""
                      }`}
                    />
                    <span className="absolute flex items-center justify-center right-0 mr-3">
                      {codeError.length > 0 && (
                        <SizeableImage
                          size={16}
                          src={require("../../public/alert-circle.png")}
                          style={{
                            marginLeft: 8,
                          }}
                        />
                      )}
                    </span>
                  </div>
                  {codeError.length > 0 && (
                    <p className="text-[#F04438] text-sm mt-1">{`${codeError}`}</p>
                  )}
                </div>
              </div>
            )}
            <label className="text-sm">New Password</label>
            <div className="relative flex flex-col items-stretch">
              <AuthInput
                placeholder="New Password"
                type="password"
                value={passwordHook.pwd}
                error={passwordHook.isPasswordInvalid}
                errorMessage={passwordHook.passwordErrorMsg}
                onChange={(event) => passwordHook.setPwd(event.target.value.trimEnd())}
              />
            </div>
            <label className="text-sm">Confirm Password</label>
            <div className="relative flex flex-col items-stretch">
              <AuthInput
                placeholder="Confirm Password"
                type="password"
                value={passwordHook.confirmPwd}
                error={passwordHook.isPasswordInvalid}
                errorMessage={passwordHook.passwordErrorMsg}
                onChange={(event) => passwordHook.setConfirmPwd(event.target.value.trimEnd())}
              />
            </div>
          </div>
          <AuthButton
            title="Reset Password"
            onClick={handleOnClickResetPassword}
            isFilled
            isLoading={isLoading}
            disabled={!canContinue || isLoading}
          />
        </div>
        <div className="xl:hidden" style={{ height: 107 }} />
        <div className="hidden xl:flex">
          <div className="flex flex-row place-self-start">
            <p className="text-[#667085] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
          </div>
        </div>
      </div>
      <div className="flex xl:hidden items-center justify-start w-screen">
        <AuthTestimonialCards style={{ width: "100vW" }} />
      </div>
    </div>
  );
};

export default ResetPasswordPage;
