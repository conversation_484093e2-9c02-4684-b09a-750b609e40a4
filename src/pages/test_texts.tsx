import GrayParagraph from "../components/atom/Texts/GrayParagraph";
import ModalTitle from "../components/atom/Texts/ModalTitle";

const ComponentTitle = ({ children }: { children: string }) => (
  <h3
    className="font-semibold mt-8 mb-3 text-xl"
    style={{
      fontFamily: "Spectral",
    }}
  >
    {children}
  </h3>
);

const TestButtons = () => {
  return (
    <div className="flex flex-col">
      <ComponentTitle>ModalTitle</ComponentTitle>
      <ModalTitle>Lorem Ipsum</ModalTitle>

      <ComponentTitle>SectionTitle</ComponentTitle>

      <ComponentTitle>GrayText</ComponentTitle>
      <GrayParagraph>Lorem Ipsum</GrayParagraph>
    </div>
  );
};

export default TestButtons;
