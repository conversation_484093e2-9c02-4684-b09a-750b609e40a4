import { useRouter } from "next/router";
import BackgroundMiddleButton from "../components/atom/Buttons/BackgroundMiddleButton";
import GrayParagraph from "../components/atom/Texts/GrayParagraph";
import Title24 from "../components/atom/Texts/base/Title24";

const Success = () => {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <Title24 style={{ marginBottom: 16, textAlign: "center" }}>
        Thank you for booking an appointment!
      </Title24>
      <GrayParagraph style={{ textAlign: "center", marginBottom: 40 }}>
        Your payment has been successfully processed <br /> and the appointment has been booked.
      </GrayParagraph>
      <BackgroundMiddleButton
        title="Go to bookings page"
        onClick={() => router.push("/customer_booking")}
        backgroundColor="DARK_BLUE"
      />
    </div>
  );
};

export default Success;
