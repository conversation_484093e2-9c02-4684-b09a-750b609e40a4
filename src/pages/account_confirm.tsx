import Image from "next/image";
import { useRouter } from "next/router";
import { useEffect, useRef, useState } from "react";
import AuthButton from "../components/atom/Buttons/AuthButton";
import ProfilePlaceholder from "../components/atom/ProfilePlaceholder";
import AuthHeaderMobile from "../components/organism/AuthHeaderMobile";
import { fetchUser } from "./login";

const AccountConfirmPage = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [name, setName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const isLoadingRef = useRef(false);

  useEffect(() => {
    if (router.query.email) {
      const passedEmail = router.query.email as string;
      setEmail(passedEmail);
    }

    if (router.query.name) {
      const passedName = router.query.name as string;
      setName(passedName);
    }
  }, []);

  async function handleOnClickLogin() {
    if (isLoadingRef.current) {
      return;
    }
    setIsLoading(true);
    isLoadingRef.current = true;
    try {
      await fetchUser(router);
    } catch (error) {
      console.log(error);
      await router.push("/login");
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }

  return (
    <div className="self-stretch h-full bg-white">
      <AuthHeaderMobile />
      <div className="flex flex-col items-center xl:justify-between w-full h-full xl:pb-10 xl:px-8 xl:pt-2 min-w-[320px] overflow-y-scroll">
        <div className="hidden flex-row place-self-start xl:flex">
          <div className="flex" style={{ width: 145 }}>
            <Image src={require("../../public/large_logo.png")} alt="Wheel Easy Logo" />
          </div>
        </div>
        <div className="flex flex-col justify-center min-h-[500px] h-full">
          <div className="flex flex-col px-4 justify-center items-center xl:h-auto pt-4 pb-4 xl:pb-14 w-screen rounded-sm xl:w-[640px] xl:px-24">
            <h1 className="text-3xl mb-2 font-medium text-center">Welcome!</h1>
            <p className="text-[#5A6068] text-center font-light mb-10">
              Completed your registration
            </p>
            <div className="flex flex-col items-stretch xl:w-[440px] w-full">
              {name && email && (
                <div className="border border-[rgba(208, 213, 221, 1)] rounded-lg p-4 mb-6">
                  <div className="flex flex-row justify-start items-center">
                    <ProfilePlaceholder
                      size={40}
                      style={{ borderRadius: 8, width: 64, height: 64, minWidth: 64 }}
                    />
                    <div className="flex flex-col">
                      <span className="flex flex-row ml-4 font-medium break-all">{name}</span>
                      <span className="flex flex-row ml-4 font-light break-all">{email}</span>
                    </div>
                  </div>
                </div>
              )}
              <AuthButton
                title="Sign In"
                onClick={handleOnClickLogin}
                isFilled={true}
                disabled={isLoading}
                isLoading={isLoading}
              />
              <div className="py-3 mt-3" style={{ height: 44 }} />
              {/* <button className="py-3 text-sm mt-3" onClick={() => router.push("/signup")}>
            Back to Main
          </button> */}
            </div>
          </div>
        </div>

        <div className="hidden flex-row place-self-start pl-2 xl:flex">
          <p className="text-[#667085] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
        </div>
      </div>
    </div>
  );
};

export default AccountConfirmPage;
