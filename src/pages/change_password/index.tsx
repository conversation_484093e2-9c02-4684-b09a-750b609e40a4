import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import AuthInput from "../../components/atom/AuthInput";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import { ArrowLeftIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import Text14 from "../../components/atom/Texts/base/Text14";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";
import CustomerLayout from "../../components/organism/CustomerLayout";
import TechnicianLayout from "../../components/organism/TechnicianLayout";
import { apiChangePassword, apiSignIn, apiSignOut } from "../../functions/cognito";
import { Color } from "../../types/Color";
import { useDispatch } from "react-redux";
import { thunkSignOutUser } from "../../@redux/modules/auth/thunks";
import AlertModal from "../../pageComponents/customer_booking/AlertModal";
import { useAlertModal } from "../../hooks/useAlertModal";

const ChangePassword = () => {
  const router = useRouter();
  const dispatch = useDispatch<any>();
  const user = useSelector((state: RootState) => state.auth.user);

  const alertModalHook = useAlertModal();

  const [oldPassword, setOldPassword] = useState<string>("");
  const [newPassword, setNewPassword] = useState<string>("");
  const [passwordConfirm, setPasswordConfirm] = useState<string>("");

  const [errorOldPassword, setErrorOldPassword] = useState<string>("");
  const [errorNewPassword, setErrorNewPassword] = useState<string>("");
  const [errorPasswordConfirm, setErrorPasswordConfirm] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const Layout = user?.roleId === 2 ? TechnicianLayout : CustomerLayout;

  useEffect(() => {
    dynamicErrorMessage();
  }, [oldPassword, newPassword, passwordConfirm]);

  useEffect(() => {
    console.log(errorPasswordConfirm);
  }, [errorPasswordConfirm]);

  function dynamicErrorMessage() {
    if (oldPassword === "") {
      setErrorOldPassword("");
    }

    if (newPassword !== "") {
      handlePasswordMismatch(newPassword, setErrorNewPassword);
    } else {
      setErrorNewPassword("");
    }

    if (passwordConfirm !== "") {
      handlePasswordMismatch(passwordConfirm, setErrorPasswordConfirm);
    } else {
      setErrorPasswordConfirm("");
    }

    if (newPassword !== "" && passwordConfirm !== "") {
      if (newPassword !== passwordConfirm) {
        setErrorPasswordConfirm("Passwords don't match!");
      }
    } else if (newPassword === "" && passwordConfirm !== "") {
      setErrorPasswordConfirm("Please set up new password");
    }
  }

  const isContinuableChangePassword = useMemo(() => {
    if (
      passwordConfirm !== "" &&
      oldPassword !== "" &&
      newPassword !== "" &&
      errorNewPassword === "" &&
      errorPasswordConfirm === ""
    ) {
      return true;
    } else return false;
  }, [passwordConfirm, oldPassword, newPassword, errorNewPassword, errorPasswordConfirm]);

  function resetInputVal() {
    setOldPassword("");
    setNewPassword("");
    setPasswordConfirm("");
  }

  function resetError() {
    setErrorOldPassword("");
    setErrorNewPassword("");
    setErrorPasswordConfirm("");
  }

  function handlePasswordMismatch(
    targetPassword: string,
    setError: (errorMessage: string) => void
  ) {
    if (targetPassword.length < 8) {
      setError("Your password needs at least 8 characters");
    } else if (targetPassword.toLowerCase() === targetPassword) {
      setError("Your password needs an uppercase letter");
    } else if (targetPassword.toUpperCase() == targetPassword) {
      setError("Your password needs a lowercase letter");
    } else if (!/\d/.test(targetPassword)) {
      setError("Your password needs a number");
    } else if (/^[A-Za-z0-9]*$/.test(targetPassword)) {
      setError("Your password needs a special character");
    } else {
      setError("");
    }
  }

  function setAlertModalContent(type: "SUCCESS" | "WARNING", errorName?: string) {
    if (type === "SUCCESS") {
      alertModalHook.setType("SUCCESS");
      alertModalHook.setTitle("Success");
      alertModalHook.setDescription(<>Successfully changed password. Please, log in again.</>);
    } else if (type === "WARNING") {
      alertModalHook.setType("WARNING");
      alertModalHook.setTitle("Server Error");
      if (errorName !== undefined) {
        if (errorName === "NotAuthorizedException") {
          alertModalHook.setDescription(<>Current password is incorrect</>);
        } else if (errorName === "LimitExceededException") {
          alertModalHook.setDescription(<>Attempt limit exceeded, please try after some time.</>);
        }
      } else {
        alertModalHook.setDescription(<>Something went wrong. please try after some time.</>);
      }
    } else return;
  }

  async function handleChangePassword() {
    if (user) {
      try {
        setIsLoading(true);
        await apiChangePassword(user.email, oldPassword, newPassword);
        resetError();
        resetInputVal();
        setAlertModalContent("SUCCESS");
      } catch (error: any) {
        if (error.name === "NotAuthorizedException") {
          setAlertModalContent("WARNING", "NotAuthorizedException");
          setErrorOldPassword("Current password is incorrect");
        } else if (error.name === "LimitExceededException") {
          setAlertModalContent("WARNING", "LimitExceededException");
        } else {
          console.log(error);
          setAlertModalContent("WARNING");
        }
      } finally {
        alertModalHook.setIsOn(true);
        setIsLoading(false);
      }
    }
  }

  return (
    <Layout>
      <AlertModal
        isOn={alertModalHook.isOn}
        onOff={() => {
          alertModalHook.setIsOn(false);
          if (!isLoading && alertModalHook.type === "SUCCESS") {
            dispatch(thunkSignOutUser());
            router.replace("/login");
          }
        }}
        title={alertModalHook.title}
        description={alertModalHook.description}
        modalSize={{
          mobile: {
            width: 375,
            height: 196,
          },
          web: {
            width: 375,
            height: 196,
          },
        }}
        type={alertModalHook.type}
      />
      <div className="w-full flex flex-col items-center py-8 px-4 overflow-y-scroll h-[calc(var(--vh,1vh)*100-54px)] lg:h-[calc(var(--vh,1vh)*100-64px)]">
        <div className="flex flex-col gap-4 w-full">
          <div className="flex flex-row gap-x-2">
            <span
              className="font-normal flex flex-row cursor-pointer text-base gap-x-2"
              onClick={() => router.push(user?.roleId === 2 ? "/account" : "/customer_profile")}
            >
              Profile{" "}
              <div className="flex flex-col justify-center">
                <ArrowLeftIcon />
              </div>
            </span>
            <span className="text-[#8A8B96] text-base font-normal">Change Password</span>
          </div>
          <Paper>
            <ProfileBlockHeader title="Change Password" />
            <div
              className="border-t-[0.5px] mb-8"
              style={{
                borderColor: Color.SLIVER_GRAY,
              }}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 mx-5 md:gap-y-8 md:mx-6 mb-6">
              <div className="flex flex-col gap-[6px] md:gap-2 mb-8 md:mb-0">
                <Text14 className="font-medium">Current Password</Text14>
                <div className="relative flex flex-col items-stretch">
                  <input
                    placeholder="Input your current password..."
                    value={oldPassword.trimEnd()}
                    type="password"
                    onChange={(event) => setOldPassword(event.target.value.trimEnd())}
                    className={`border px-3.5 py-2.5 mb-1 placeholder-[#667085] placeholder:text-base placeholder:font-light focus:outline-[#FCA311] rounded-lg drop-shadow-sm ${
                      errorOldPassword === "Current password is incorrect" ? "border-[#FDA29B]" : ""
                    }`}
                  />
                  {errorOldPassword === "Current password is incorrect" && (
                    <>
                      <span className="absolute right-2.5 flex inset-y-2.5 pl-2">
                        <span className="border-2 p-2 w-6 h-6 text-center font-medium flex flex-col justify-center text-sm rounded-full text-[#F04438] border-[#F04438]">
                          !
                        </span>
                      </span>
                      <Text14 className="text-red-500">{errorOldPassword}</Text14>
                    </>
                  )}
                </div>
              </div>
              <div className="hidden md:block" />
              <div className="flex flex-col gap-[6px] md:gap-2 mb-6 md:mb-0">
                <Text14 className="font-medium">New Password</Text14>
                <div className="relative flex flex-col items-stretch">
                  <AuthInput
                    placeholder="Input your new password..."
                    error={
                      errorNewPassword !== "" &&
                      errorNewPassword !== "Current password is incorrect"
                    }
                    errorMessage={errorNewPassword}
                    type="password"
                    value={newPassword.trimEnd()}
                    onChange={(event) => setNewPassword(event.target.value.trimEnd())}
                  />
                </div>
              </div>
              <div className="flex flex-col gap-[6px] md:gap-2 mb-8 md:mb-0">
                <Text14 className="font-medium">Confirm New Password</Text14>
                <div className="relative flex flex-col items-stretch">
                  <AuthInput
                    error={
                      errorPasswordConfirm !== "" &&
                      errorPasswordConfirm !== "Current password is incorrect"
                    }
                    errorMessage={errorPasswordConfirm}
                    placeholder="Input your confirmation password..."
                    value={passwordConfirm.trimEnd()}
                    type="password"
                    onChange={(event) => setPasswordConfirm(event.target.value.trimEnd())}
                  />
                </div>
              </div>
              <div className="w-auto text-xs">
                <BackgroundSmallButton
                  backgroundColor="BLACK_PEARL"
                  title="Change Password"
                  disabled={!isContinuableChangePassword}
                  isLoading={isLoading}
                  onClick={handleChangePassword}
                  style={{
                    paddingTop: 13.5,
                    paddingBottom: 13.5,
                    paddingLeft: 35,
                    paddingRight: 35,
                    width: 188,
                    height: 48,
                    borderRadius: 8,
                    fontWeight: 500,
                    fontSize: 14,
                  }}
                />
              </div>
            </div>
          </Paper>
        </div>
      </div>
    </Layout>
  );
};

export default ChangePassword;
