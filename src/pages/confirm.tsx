import { useRouter } from "next/router";
import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from "react";
import { Rings } from "react-loader-spinner";
import AuthTestimonialCards from "../components/atom/Testimonials/AuthTestimonialCards";
import AuthHeaderMobile from "../components/organism/AuthHeaderMobile";
import { apiConfirmUser, apiResendConfirmCode } from "../functions/cognito";
import { Color } from "../types/Color";
import { apiAdminConfirmUser } from "../functions/api/user";

const ConfirmPage = () => {
  const router = useRouter();
  const [userNameInput, setUserNameInput] = useState<string>("");
  const [nameInput, setNameInput] = useState<string>("");
  const [codeInput, setCodeInput] = useState<string>("");
  const [shouldShowUserNameInput, setShouldShowUserNameInput] = useState<boolean>(true);

  const [countDown, setCountDown] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const [otpValues, setOTPValues] = useState(Array<string>(6).fill(""));
  const [activeInput, setActiveInput] = useState(0);
  const isKeyDownMoved = useRef(false);
  const isLoadingRef = useRef(false);

  const handleOnChange = (value: string, index: number) => {
    if (parseInt(value) || value == "0") {
      let newOTPValues = otpValues;
      newOTPValues[index] = value.substring(0, 1);
      setOTPValues(newOTPValues);
      handleOnFocus(index + 1);
    } else if (value === "") {
      if (!isKeyDownMoved.current) {
        let newOTPValues = otpValues;
        newOTPValues[index] = value;
        setOTPValues(newOTPValues);
        handleOnFocus(index - 1);
      }
    }

    isKeyDownMoved.current = false;
    setCodeInput(otpValues.join(""));
  };

  const handleOnFocus = (index: number) => {
    if (index == 6) {
      setActiveInput(0);
    }
    setActiveInput(index);
  };

  const handleOnPaste = useCallback(
    (e: React.ClipboardEvent<HTMLInputElement>) => {
      e.preventDefault();
      const pastedData = e.clipboardData
        .getData("text/plain")
        .trim()
        .slice(0, 6 - activeInput)
        .split("");
      if (pastedData) {
        let nextFocusIndex = 0;
        const updatedOTPValues = [...otpValues];
        updatedOTPValues.forEach((val, index) => {
          if (index >= activeInput) {
            const changedValue = pastedData.shift() || val;
            if (changedValue) {
              updatedOTPValues[index] = changedValue;
              nextFocusIndex = index;
            }
          }
        });
        setOTPValues(updatedOTPValues);
        setActiveInput(Math.min(nextFocusIndex + 1, 5));
        setCodeInput(updatedOTPValues.join(""));
      }
    },
    [activeInput, otpValues],
  );

  async function hendleContinueWithoutCode() {
    if (isLoadingRef.current) {
      return;
    }
    setIsLoading(true);
    isLoadingRef.current = true;
    try {
      await apiAdminConfirmUser({ email: userNameInput });
      setError("");
      await router.push({
        pathname: "/account_confirm",
        query: {
          email: userNameInput,
          name: nameInput,
        },
      });
    } catch (error) {
      setError("Invalid confirmation code");
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }

  async function handleOnClickConfirm() {
    if (isLoadingRef.current) {
      return;
    }

    setIsLoading(true);
    isLoadingRef.current = true;
    try {
      await apiConfirmUser(userNameInput, codeInput);
      setError("");
      await router.push({
        pathname: "/account_confirm",
        query: {
          email: userNameInput,
          name: nameInput,
        },
      });
    } catch (error) {
      setError("Invalid confirmation code");
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }

  async function handleOnClickResendCode() {
    setIsLoading(true);
    try {
      await apiResendConfirmCode(userNameInput);
      alert("New verification code sent!");
      setCountDown(60);
    } catch (error) {
      setError("Code could not be sent. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    const path = router.asPath;
    const query = path.split("?")[1];
    const paramsArr = query.split("&");
    const usernameParam = paramsArr.find((param) => param.includes("username"));
    const fullnameParam = paramsArr.find((param) => param.includes("fullname"));

    if (usernameParam) {
      setShouldShowUserNameInput(false);
      const passedUsername = usernameParam.split("=")[1];
      setUserNameInput(decodeURIComponent(passedUsername));
    }

    if (fullnameParam) {
      const passedName = fullnameParam.split("=")[1];
      setNameInput(decodeURIComponent(passedName));
    }

    // if (router.query.username) {
    //   setShouldShowUserNameInput(false);
    //   const passedUsername = router.query.username as string;
    //   setUserNameInput(passedUsername);
    // }

    // if (router.query.fullname) {
    //   const passedName = router.query.fullname as string;
    //   setNameInput(passedName);
    // }
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCountDown((t) => {
        if (t === 0) {
          return 0;
        } else {
          return t - 1;
        }
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [countDown]);

  return (
    <div className="flex flex-col xl:flex-row items-center justify-between h-full select-none overflow-x-hidden xl:overflow-hidden min-w-[320px]">
      <div className="hidden xl:flex h-full">
        <AuthTestimonialCards />
      </div>
      <AuthHeaderMobile />
      <div className="flex grow flex-col self-stretch xl:justify-between h-full w-screen items-start xl:pl-5 xl:mx-14 pb-10 px-4 overflow-y-scroll">
        <div className="flex flex-row mt-6 xl:mt-10" onClick={() => router.push("/login")}>
          <div className="flex flex-row items-start justify-start" style={{ cursor: "pointer" }}>
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9.57031 5.93005L3.50031 12.0001L9.57031 18.0701"
                stroke={Color.BLACK_PEARL}
                strokeWidth="2.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M20.5 12H3.67"
                stroke={Color.BLACK_PEARL}
                strokeWidth="2.5"
                strokeMiterlimit="10"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <p className="ml-5 text-sm">Back to login</p>
          </div>
        </div>
        <div className="block xl:hidden" style={{ height: 105 }} />
        <div className="flex flex-col justify-start items-stretch pt-4 pb-14 rounded-sm w-full">
          <div className="flex flex-col justify-start items-start">
            <h1 className="text-3xl mb-2 font-medium text-center">Verification Code</h1>
            <p className="text-[#5A6068] text-center font-light mb-10">
              We{"'"}ve sent an email to {userNameInput}
            </p>
          </div>
          <div className="flex flex-col items-stretch xl:w-[440px]">
            {shouldShowUserNameInput ? (
              <input
                placeholder="Username"
                value={userNameInput}
                onChange={(event) => setUserNameInput(event.target.value)}
                className="border rounded-md px-4 py-3 mb-4"
              />
            ) : undefined}

            <fieldset className="flex flex-row items-center gap-x-3">
              {[0, 1, 2, 6, 3, 4, 5].map((index, _) => {
                if (index == 6) {
                  return (
                    <span
                      key={index}
                      className="flex flex-row items-center justify-center text-4xl text-[#D0D5DD] pb-5"
                    >
                      -
                    </span>
                  );
                }
                return (
                  <Input
                    id={`verificationInput-${index}`}
                    key={index}
                    focus={activeInput === index}
                    value={otpValues ? otpValues[index] : 0}
                    autoFocus={true}
                    onFocus={() => handleOnFocus(index)}
                    onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                      handleOnChange(event.target.value, index)
                    }
                    onKeyDown={(event: React.KeyboardEvent<HTMLInputElement>) => {
                      if (event.key === "Backspace" && otpValues[index] === "") {
                        handleOnFocus(index - 1);
                        isKeyDownMoved.current = true;
                      }
                    }}
                    onPaste={handleOnPaste}
                    placeholder="0"
                  />
                );
              })}
            </fieldset>
            {error != "" && <p className="text-sm text-[#F04438]">{error}</p>}

            <span
              className="text-sm text-[#667085] mt-3"
              style={{
                marginBottom: 4,
              }}
            >
              Haven{"'"}t received code yet?
            </span>
            <div className="flex flex-row items-start mb-9">
              <div className="">
                <button
                  className="text-sm text-[#FCA311] font-medium mr-5"
                  style={{
                    color: countDown !== 0 ? Color.DISABLED_GRAY : Color.ACCENT,
                  }}
                  disabled={countDown !== 0 ? true : false}
                  onClick={handleOnClickResendCode}
                >
                  Resend Code
                </button>
                <text className="test-sm text-[#667085]  mr-5">or</text>
                <button
                  className="text-sm text-[#667085]"
                  style={{ color: Color.ACCENT }}
                  onClick={hendleContinueWithoutCode}
                >
                  Continue Without Code
                </button>
              </div>
              <span
                className="text-sm text-[#667085] mr-5"
                style={{ marginTop: 0.01, display: countDown === 0 ? "none" : undefined }}
              >
                0:{countDown < 10 ? "0" + countDown : countDown}
              </span>
            </div>
            <button
              className={`text-white rounded-lg font-medium justify-center flex ${
                isLoading ? "py-1 bg-[rgba(0,0,0,0.4)]" : "py-3 bg-black"
              }`}
              onClick={handleOnClickConfirm}
              disabled={isLoading}
            >
              {!isLoading ? "Confirm" : <Rings width="40" height="40" color="rgba(0,0,0,0.4)" />}
            </button>
          </div>
        </div>
        <div className="hidden xl:flex">
          <div className="flex flex-row place-self-start">
            <p className="text-[#667085] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
          </div>
        </div>
        <div className="block xl:hidden" style={{ height: 136 }} />
      </div>
      {/* <div className="flex xl:hidden items-center justify-start w-screen">
        <AuthTestimonialCards style={{ width: "100vW" }} />
      </div> */}
    </div>
  );
};

const usePrevious = (value?: boolean) => {
  const ref = useRef<boolean>();

  useEffect(() => {
    ref.current = value;
  }, [value]);

  return ref.current;
};

const Input = (props: any) => {
  const { focus, autoFocus, ...rest } = props;
  const inputRef = useRef<HTMLInputElement>(null);
  const prevFocus = usePrevious(!!focus);
  useLayoutEffect(() => {
    if (inputRef.current) {
      if (focus && autoFocus) {
        inputRef.current.focus();
      }
      if (focus && autoFocus && focus !== prevFocus) {
        inputRef.current.focus();
        inputRef.current.select();
      }
    }
  }, [autoFocus, focus, prevFocus]);

  return (
    <div className="flex-1">
      <input
        type="number"
        ref={inputRef}
        className="border text-center rounded-md h-[80px] px-1 py-[6px] xl:px-4 xl:py-3 mb-2 text-4xl xl:w-[60px] w-full"
        {...rest}
      />
    </div>
  );
};

export default ConfirmPage;
