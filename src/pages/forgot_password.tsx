import Head from "next/head";
import { useRouter } from "next/router";
import { useRef, useState } from "react";
import AuthInput from "../components/atom/AuthInput";
import AuthButton from "../components/atom/Buttons/AuthButton";
import AuthTestimonialCards from "../components/atom/Testimonials/AuthTestimonialCards";
import AuthHeaderMobile from "../components/organism/AuthHeaderMobile";
import { apiForgotPassword, setNewUserPassword } from "../functions/cognito";
import { CustomError } from "../types/CustomError";
import ForgotPasswordDisabledModal from "../pageComponents/forgot_password/ForgotPasswordDisabledModal";
import { apiSendMessageToSlack } from "../functions/api/slack";

const ForgotPasswordPage = () => {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [username, setUsername] = useState<string>("");
  const [error, setError] = useState<string>("");
  const isLoadingRef = useRef(false);
  const [resetPasswordModalOn, setResetPasswordModalOn] = useState<boolean>(false);

  function generateTemporaryPassword(): string {
    const lowercase: string = "abcdefghijklmnopqrstuvwxyz";
    const uppercase: string = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numbers: string = "0123456789";
    const allCharacters: string = lowercase + uppercase + numbers;

    let password: string = "";

    // Add one uppercase letter
    password += uppercase[Math.floor(Math.random() * uppercase.length)];

    // Add one number
    password += numbers[Math.floor(Math.random() * numbers.length)];

    // Fill the remaining characters randomly to meet the minimum length of 8
    for (let i = 2; i < 8; i++) {
      password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
    }

    // Shuffle the password to ensure randomness
    password = password
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("");

    return password;
  }

  async function handleOnClickSendResetCode() {
    if (isLoadingRef.current) {
      return;
    }

    const _verifyUsername = (username: string) => {
      const _isValidEmail = (email: string) => {
        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailPattern.test(email);
      };
      const cognitoPattern = /^[\p{L}\p{M}\p{S}\p{N}\p{P}]+$/u;
      if (!_isValidEmail(username)) {
        throw new CustomError(
          "InvaildEmailAddress",
          "Member must satisfy regular expression pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+.[a-zA-Z]{2,}$/",
        );
      }
      if (!cognitoPattern.test(username)) {
        throw new CustomError(
          "InvaildCognitoPattern",
          "Member must satisfy regular expression pattern: /^[p{L}p{M}p{S}p{N}p{P}]+$/u",
        );
      }
    };
    setIsLoading(true);
    isLoadingRef.current = true;
    try {
      _verifyUsername(username);
      await apiForgotPassword(username);
      await router.push({
        pathname: "/reset_password",
        query: {
          username,
        },
      });
      // const tempPwd = generateTemporaryPassword();
      // await setNewUserPassword(username, tempPwd);
      // const result = await apiSendMessageToSlack({
      //   channel: "reset-password",
      //   message: `Reset Password Requested. Email: ${username}`,
      // });
      // console.log("Slack message result:", result);
      // setResetPasswordModalOn(true);
    } catch (error: any) {
      _handleError(error);
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }

  function _handleError(error: any) {
    if (error.name === "InvaildEmailAddress") {
      setError("This is not a valid email address.");
    } else if (error.name === "InvaildCognitoPattern") {
      setError("Only letters, marks, symbols, numbers, and punctuation are allowed.");
    } else if (error.name === "UserNotFoundException") {
      setError("Can't find this email. Try signing in with Google or create a new account.");
    } else if (error.name === "LimitExceededException") {
      setError("Attempt limit exceeded, please try after some time.");
    } else {
      console.error(error);
      setError("Something went wrong, please try again later");
    }

    throw error;
  }

  function handleKeyDown(e: any) {
    if (e.keyCode === 13) {
      handleOnClickSendResetCode();
    }
  }

  return (
    <div className="self-stretch h-full bg-white">
      <AuthHeaderMobile />
      <div className="flex flex-col xl:flex-row items-center xl:justify-start w-full h-full select-none overflow-x-hidden xl:overflow-hidden min-w-[320px] overflow-y-scroll">
        <Head>
          <title>Forgot Password - Wheel Easy</title>
        </Head>
        <div className="hidden xl:flex h-full">
          <AuthTestimonialCards />
        </div>
        <ForgotPasswordDisabledModal
          isOn={resetPasswordModalOn}
          onOff={() => setResetPasswordModalOn(false)}
          onClickButton={() => setResetPasswordModalOn(false)}
        ></ForgotPasswordDisabledModal>
        <div className="flex flex-col justify-center min-h-[500px] h-full">
          <div className="flex grow flex-col justify-center xl:justify-between h-full w-full py-6 items-start xl:-mt-5 px-5 xl:py-10 xl:px-14">
            <div className="flex flex-row w-full justify-end"></div>
            <div className="flex flex-col justify-center items-start xl:mt-5 w-full xl:w-[400px]">
              <h1 className="text-3xl mb-2 font-medium text-start">Forgot your password?</h1>
              <p className="text-[#5A6068] text-start font-light">
                Please enter the email address you've used to register. We'll send you a 6-digit
                code to the email you enter.
              </p>
              <div style={{ height: 40 }} />
              <div className="flex flex-col items-stretch xl:w-[400px] w-full mb-5">
                <label className="text-sm">Email</label>
                <div className="relative flex flex-col items-stretch">
                  <AuthInput
                    placeholder="Input your email here..."
                    value={username}
                    onChange={(event) => setUsername(event.target.value.trimEnd())}
                    error={error.length > 0}
                    errorMessage={error}
                    onKeyDown={handleKeyDown}
                  />
                  {error && (
                    <span className="absolute right-2.5 flex inset-y-2.5 pl-2">
                      <span className="border-2 p-2 w-6 h-6 text-center font-medium flex flex-col justify-center text-sm rounded-full text-[#F04438] border-[#F04438]">
                        !
                      </span>
                    </span>
                  )}
                </div>
              </div>
              <AuthButton
                title="Send Password Reset Code"
                onClick={handleOnClickSendResetCode}
                isFilled
                isLoading={isLoading}
                disabled={username.length === 0 || isLoading}
              />
            </div>
            <div className="xl:hidden" style={{ height: 107 }} />
            <div className="hidden xl:flex">
              <div className="flex flex-row place-self-start">
                <p className="text-[#667085] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
              </div>
            </div>
          </div>
          {/* <div className="flex xl:hidden items-center justify-start w-screen">
        <AuthTestimonialCards style={{ width: "100vW" }} />
      </div> */}
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
