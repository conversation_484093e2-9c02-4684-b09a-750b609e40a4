import Image from "next/image";
import { useMemo, useState } from "react";
import GrayParagraph from "../components/atom/Texts/GrayParagraph";
import TechnicianLayout from "../components/organism/TechnicianLayout";
const GrayStar = require("../../public/gray_star.png");
const YellowStar = require("../../public/yellow_star.png");
const DarkStar = require("../../public/dark_star.png");

const testRating = [
  {
    score: 5,
    count: 12,
  },
  {
    score: 4,
    count: 7,
  },
  {
    score: 3,
    count: 3,
  },
  {
    score: 2,
    count: 0,
  },
  {
    score: 1,
    count: 0,
  },
];

const testReview = [
  {
    name: "<PERSON>",
    serviceName: "Tire Swap",
    rating: 4,
    ratedDate: new Date(),
  },
];

function renderRatingStar(count: number, width: number, height: number, icon: any) {
  let result = [];
  for (let i = 0; i < 5; i++) {
    if (i < count) {
      result.push(
        <div
          key={i}
          className="flex justify-center items-center"
          style={{
            width,
            height,
          }}
        >
          <Image src={icon} />
        </div>
      );
    } else {
      result.push(
        <div
          key={i}
          className="flex justify-center items-center"
          style={{
            width,
            height,
          }}
        >
          <Image src={GrayStar} />
        </div>
      );
    }
  }
  return result;
}

const ReviewItem = (props: {
  name: string;
  serviceName: string;
  rating: number;
  ratedDate: Date;
}) => {
  return (
    <div className="flex flex-col p-5 border rounded-md">
      <div className="flex justify-between mb-5">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-black rounded-full mr-3" />
          <div className="flex flex-col ">
            <span className="font-semibold">{props.name}</span>
            <span className="text-sm text-[rgba(0,0,0,0.4)]">for {props.serviceName}</span>
          </div>
        </div>
        <div className="flex flex-col items-end">
          <div className="flex mb-2">{renderRatingStar(props.rating, 20, 20, YellowStar)}</div>
          <span className="text-sm text-[rgba(0,0,0,0.4)]">{props.ratedDate.toDateString()}</span>
        </div>
      </div>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis molestie sapien vel nulla
        vehicula, vel suscipit augue auctor. Suspendisse nec venenatis ligula. Sed posuere vel risus
        sit amet accumsan. Morbi quis est semper, scelerisque leo et, dignissim eros. Sed sagittis
        eros auctor nulla interdum, id ullamcorper ipsum pulvinar.
      </p>
    </div>
  );
};

const RatingPage = () => {
  const [rating, setRating] = useState<number>(4);

  const renderedRatingStar = useMemo(() => renderRatingStar(rating, 32, 32, DarkStar), [rating]);

  return (
    <TechnicianLayout>
      <h2 className="text-2xl font-medium mb-5">Rating</h2>
      <div className="flex space-between">
        <div className="flex flex-col ">
          <div className="flex items-center space-x-1 mb-2">{renderedRatingStar}</div>
          <span>Average Rating: {rating}/5</span>
        </div>
        <div className="flex flex-col items-end flex-1">
          {testRating.map((item) => (
            <div className="flex items-center justify-between w-[288px]" key={item.score}>
              <div className="flex items-center ">
                {renderRatingStar(item.score, 12, 12, YellowStar)}
              </div>
              <span>({item.count})</span>
              <div className="w-[170px] h-[6px] bg-[rgba(0,0,0,0.4)]" />
            </div>
          ))}
        </div>
      </div>
      <h2 className="text-2xl font-medium mb-2">Reviews</h2>
      <GrayParagraph style={{ marginBottom: 24 }}>22 Reviews</GrayParagraph>
      {testReview.map((item, index) => (
        <ReviewItem
          name={item.name}
          serviceName={item.serviceName}
          rating={item.rating}
          ratedDate={item.ratedDate}
          key={index}
        />
      ))}
    </TechnicianLayout>
  );
};

export default RatingPage;
