import React, { useEffect, useState } from "react";
import { ZoneService } from "../services/ZoneService";

export interface Zone {
  zoneId: number;
  name: string;
  description: string;
  fsas: FSA[];
}

export interface FSA {
  id: number;
  name: string;
}

const Zone = () => {
  const [zones, setZones] = useState<Zone[]>();

  useEffect(() => {
    fetchZone();
  }, []);

  async function fetchZone() {
    const result = await ZoneService.get();
    setZones(result);
  }

  return (
    <div className="flex flex-col pl-4 pt-4">
      {zones?.map((zone) => (
        <div className="flex flex-col ">
          <div className="flex items-center ">
            <span>{zone.name}</span>
            <span>{zone.description}</span>
          </div>
          <div className="flex flex-col pl-10">
            {zone.fsas.map((fsa) => (
              <span>{fsa.name}</span>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default Zone;
