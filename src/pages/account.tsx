import Head from "next/head";
import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../@redux";
import ConfirmCancelModal from "../components/Modal/ConfirmCancelModal";
import { GarbageIcon, InfoIcon } from "../components/atom/Icons";
import Paper from "../components/atom/Paper";
import TextInput from "../components/atom/TextInput";
import ProfileFieldValue from "../components/atom/Texts/base/ProfileFieldValue";
import Text14 from "../components/atom/Texts/base/Text14";
import ProfileBlockHeader from "../components/molecule/ProfileBlockHeader";
import TechnicianLayout from "../components/organism/TechnicianLayout";
import PasswordBlock from "../pageComponents/account/PasswordBlock";
import InformationBlock from "../pageComponents/customer_profile/InformationBlock";
import BiographyBlock from "../pageComponents/technician_profile/BiographyBlock";
import CategoryBlock from "../pageComponents/technician_profile/CategoryBlock";
import CertificationBlock from "../pageComponents/technician_profile/CertificationBlock";
import ServiceAreaBlock from "../pageComponents/technician_profile/ServiceAreaBlock";
import { Color } from "../types/Color";

const AccountPage = () => {
  const user = useSelector((state: RootState) => state.auth.user);
  const [deleteMessage, setDeleteMessage] = useState<string>("");
  const [isDeleteAccountModalOn, setIsDeleteAccountModalOn] = useState<boolean>(false);

  return (
    <TechnicianLayout>
      <Head>
        <title>Profile - Wheel Easy</title>
      </Head>
      <div className="w-full flex flex-col items-center py-8 px-4 overflow-y-scroll h-[calc(100vh-68px)]">
        <div className="md:min-w-[700px]">
          <InformationBlock />
          {user?.isSSO ? null : <PasswordBlock />}
          <div className="md:max-w-[1000px]">
            <BiographyBlock />
          </div>
          <CategoryBlock />
          <ServiceAreaBlock />
          <CertificationBlock />
          <div className="mt-6">
            <Paper>
              <ConfirmCancelModal
                isOn={isDeleteAccountModalOn}
                onOff={() => setIsDeleteAccountModalOn(false)}
                title="Delete Account"
                description=""
                webHeight={300}
                content={
                  <>
                    <Text14>
                      Are you sure want to delete your account? After you confirm to delete this
                      account the data will permanently deleted too. There’s no going back.
                    </Text14>
                    <Text14 className="my-3">
                      Please type <span className="font-medium">“Delete Me”</span> to confirm
                    </Text14>
                    <TextInput
                      value={deleteMessage}
                      onChange={(val) => setDeleteMessage(val)}
                      placeholder="Delete Me"
                    />
                  </>
                }
                confirmButtonTitle="Delete Account"
                cancelButtonTitle="Cancel"
                onClickConfirmButton={() => {}}
                onClickCancelButton={() => setIsDeleteAccountModalOn(false)}
                isConfirmDisabled={deleteMessage !== "Delete Me"}
                isLoading={false}
              />
              <ProfileBlockHeader title="Account Information" />
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-y-5 py-5 mb-7 border-gray-300 border-y-[0.5px] px-6">
                <p className="font-medium">Joined in</p>
                <ProfileFieldValue>2022-08-15</ProfileFieldValue>
              </div>
              <p className="flex gap-2 p-3 bg-[#FF8F6C]/[0.1] font-light rounded-lg border-[0.5px] border-[#FF8F6C] mx-6">
                <div>
                  <InfoIcon />
                </div>
                Caution : Think twice before you delete your account because this will permanently
                delete account data. There’s no going back.
              </p>
              <button
                className="border-red-500 rounded-lg border-[1px] text-red-500 w-full sm:w-44 py-3 font-medium mx-6 my-6"
                style={{
                  backgroundColor: Color.DISABLED_GRAY,
                  color: Color.WHITE,
                  borderColor: Color.WHITE,
                }}
                onClick={() => setIsDeleteAccountModalOn(true)}
                disabled={true}
              >
                <div className="flex items-center gap-1  justify-center">
                  {/* <GarbageIcon /> */}
                  Delete Account
                </div>
              </button>
            </Paper>
          </div>
        </div>
      </div>
    </TechnicianLayout>
  );
};

export default AccountPage;
