import Head from "next/head";
import { useRouter } from "next/router";
import { event } from "nextjs-google-analytics";
import { useMemo, useRef, useState } from "react";
import PrivacyPolicyModal from "../components/Modal/PrivacyPolicyModal";
import TermsOfUseModal from "../components/Modal/TermsOfUseModal";
import AuthInput from "../components/atom/AuthInput";
import AuthButton from "../components/atom/Buttons/AuthButton";
import AuthTestimonialCards from "../components/atom/Testimonials/AuthTestimonialCards";
import AuthHeaderMobile from "../components/organism/AuthHeaderMobile";
import { apiSignUp } from "../functions/cognito";
import usePassword from "../hooks/usePasswordValidator";
import PhoneSelect from "../pageComponents/booking_address/PhoneSelect";
import ConfirmEmailAddressModal from "../pageComponents/signup/ConfirmEmailAddressModal";
import { Input } from "../types/Input";
import { PhoneNumber } from "../types/PhoneNumber";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { apiGoogleRecaptcha } from "../functions/api/google";
import { apiSendMessageToSlack } from "../functions/api/slack";
import HandleCreateAccountModal from "../pageComponents/signup/CaptchaModal";
import CaptchaFailedModal from "../pageComponents/signup/CaptchaModal";

const emailRegex = /^[\w._-]+[+]?[\w._-]+@[\w.-]+\.[a-zA-Z]{2,6}$/;
const phoneNumberPattern = /^\(\d{3}\) \d{3}-\d{4}$/;

const SignUpPage = () => {
  const [firstNameInput, setFirstNameInput] = useState<string>("");
  const [lastNameInput, setLastNameInput] = useState<string>("");
  const [phoneInput, setPhoneInput] = useState<string>("");
  const [phoneRegion, setPhoneRegion] = useState<string>("+1");
  const [emailAddressInput, setEmailAddressInput] = useState<string>("");
  const [isAgreed, setIsAgreed] = useState<boolean>(false);
  const [emailAvailable, setEmailAvailable] = useState<boolean>(true);
  const [phoneAvailable, setPhoneAvailable] = useState<boolean>(true);

  const passwordHook = usePassword();

  const [errorMessageEmail, setErrorMessageEmail] = useState<string>("");
  const [errorMessagePhone, setErrorMessagePhone] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isOnPrivacyPolicy, setIsOnPrivacyPolicy] = useState<boolean>(false);
  const [isOnTermsOfUse, setIsOnTermsOfuse] = useState<boolean>(false);

  const [isConfirmEmailAddresModalOn, setIsConfirmEmailAddresModalOn] = useState<boolean>(false);

  const [isCaptchaFailedModalOn, setIsCaptchaFailedModalOn] = useState<boolean>(false);

  const { executeRecaptcha } = useGoogleReCaptcha();

  // Flag indicating that onClickCreateAccount is running(prevent to double click)
  const isLoadingRef = useRef<any>(false);

  const router = useRouter();
  const isSubmittable = useMemo(() => {
    return (
      firstNameInput.trim() !== "" &&
      lastNameInput.trim() !== "" &&
      phoneInput.trim() != "" &&
      emailAddressInput.trim() !== "" &&
      passwordHook.canContinue &&
      isAgreed
    );
  }, [
    firstNameInput,
    lastNameInput,
    phoneInput,
    emailAddressInput,
    passwordHook.canContinue,
    isAgreed,
  ]);

  function setFormattedPhoneNumber(value: string) {
    const isBackspace = value.length < phoneInput.length;
    const newValue = Input.formatNewInputIntoPhoneNumber(value, phoneInput, isBackspace);
    setPhoneInput(newValue || "");
  }

  async function handleOnClickCreateAccount(captchaBackupSucceeded: boolean = false) {
    if (isLoadingRef.current) {
      return;
    }
    setIsLoading(true);
    isLoadingRef.current = true;
    // if (captchaBackupSucceeded == false) {
    //   if (!executeRecaptcha) {
    //     return;
    //   }

    //   const gReCaptchaToken = await executeRecaptcha("signup");
    //   console.log("gReCaptchaToken", gReCaptchaToken);

    //   const captchaResult = await apiGoogleRecaptcha(gReCaptchaToken);
    //   console.log("captchaResult", captchaResult);

    //   if (captchaResult.success === false) {
    //     console.log("captcha failed");
    //     setIsCaptchaFailedModalOn(true);
    //     setIsLoading(false);
    //     isLoadingRef.current = false;

    //     const result = await apiSendMessageToSlack({
    //       channel: "recaptcha-failures",
    //       message: `reCAPTCHA FAILED! Form: Sign Up, Email: ${emailAddressInput}, Phone: ${phoneInput}, Name: ${firstNameInput} ${lastNameInput}`,
    //     });
    //     console.log("Slack message result:", result);
    //     return;
    //   }
    // }
    // console.log("captchaBackupSucceeded == true");

    let tmpEmailAvailable,
      tmpPhoneAvailable = true;

    if (!phoneNumberPattern.test(phoneInput)) {
      tmpPhoneAvailable = false;
      setErrorMessagePhone("The phone number must be exactly 10 digits");
    } else {
      tmpPhoneAvailable = true;
      setErrorMessagePhone("");
    }

    if (!emailRegex.test(emailAddressInput)) {
      tmpEmailAvailable = false;
      setErrorMessageEmail("Email is not valid");
    } else {
      tmpEmailAvailable = true;
      setErrorMessageEmail("");
    }

    const isValid = passwordHook.validatePassword();

    if (!isValid) {
      setIsLoading(false);
    }

    setPhoneAvailable(tmpPhoneAvailable);
    setEmailAvailable(tmpEmailAvailable);

    if (!isValid || !tmpPhoneAvailable || !tmpEmailAvailable) {
      isLoadingRef.current = false;
      setIsLoading(false);
      return;
    }

    try {
      await apiSignUp({
        fullName: firstNameInput + " " + lastNameInput,
        username: emailAddressInput,
        password: passwordHook.pwd,
        phone: new PhoneNumber(phoneRegion, phoneInput).getFullPhoneNumberForAPI(),
      });
      event("signup", {
        category: "Auth",
        label: "Sign up with email and password.",
      });
      await router.push({
        pathname: "/confirm",
        query: {
          username: emailAddressInput,
          fullname: firstNameInput + " " + lastNameInput,
        },
      });
    } catch (error) {
      const err = error as { name: string; code: string };

      if (err["name"] == "UsernameExistsException") {
        setEmailAvailable(false);
        setErrorMessageEmail("Email is not available for use");
      } else {
        setEmailAvailable(false);
        setErrorMessageEmail("Something went wrong, please try again later");
      }
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }

  return (
    <div className="flex flex-col xl:flex-row items-center justify-between h-full select-none overflow-x-hidden xl:overflow-hidden min-w-[320px]">
      <ConfirmEmailAddressModal
        isOn={isConfirmEmailAddresModalOn}
        onOff={() => setIsConfirmEmailAddresModalOn(false)}
        title="Please Confirm"
        emailAddress={emailAddressInput}
        buttonTitle="Confirm"
        onClickButton={async () => {
          setIsConfirmEmailAddresModalOn(false);
          handleOnClickCreateAccount();
        }}
        isLoading={isLoading}
      />
      <CaptchaFailedModal
        isOn={isCaptchaFailedModalOn}
        onOff={() => setIsCaptchaFailedModalOn(false)}
        succeeded={async () => {
          setIsCaptchaFailedModalOn(false);
          await handleOnClickCreateAccount(true); // Trigger the function
        }}
      />
      <PrivacyPolicyModal isOn={isOnPrivacyPolicy} onOff={() => setIsOnPrivacyPolicy(false)} />
      <TermsOfUseModal isOn={isOnTermsOfUse} onOff={() => setIsOnTermsOfuse(false)} />
      <Head>
        <title>Sign Up - Wheel Easy</title>
      </Head>
      <div className="hidden xl:flex h-full">
        <AuthTestimonialCards />
      </div>
      <AuthHeaderMobile technicianHeader />
      <div className="flex grow flex-col self-stretch w-screen items-center py-6 xl:justify-between xl:h-full xl:w-full xl:items-start px-5 xl:py-10 xl:px-14 bg-white overflow-y-scroll">
        <div className="flex flex-row w-full justify-end pt-15">
          <p className="hidden text-end text-md text-[#5A6068] font-light xl:flex flex-col">
            Already have an account?{" "}
            <span
              className="px-1 underline font-medium text-black cursor-pointer text-2xl"
              onClick={() => router.push("/login")}
            >
              Sign In
            </span>
          </p>
        </div>
        <div className="flex flex-col justify-center w-full items-center xl:w-[500px]">
          <div className="flex flex-col justify-start items-start w-full xl:w-[500px] xl:justify-start">
            <h1 className="text-3xl mb-2 font-medium text-start">Create your profile now</h1>
            <div className="flex flex-row items-center justify-between">
              <p className="text-[#5A6068] text-start font-light">
                Before you start this journey you can create your profile first
              </p>
              <div />
            </div>
          </div>
          <div style={{ height: 24 }} />
          <div className="flex flex-col justify-center items-stretch w-full -mt-4 gap-y-4">
            <div className="flex flow-row justify-between gap-x-3">
              <div className="flex-1">
                <label className="text-sm">First Name*</label>
                <AuthInput
                  placeholder="Input your first name..."
                  value={firstNameInput}
                  onChange={(event) => setFirstNameInput(event.target.value)}
                />
              </div>
              <div className="hidden flex-1 md:block">
                <label className="text-sm">Last Name</label>
                <AuthInput
                  placeholder="Input your last name..."
                  value={lastNameInput}
                  onChange={(event) => setLastNameInput(event.target.value)}
                />
              </div>
            </div>
            <div className="block md:hidden">
              <label className="text-sm">Last Name</label>
              <AuthInput
                placeholder="Input your last name..."
                value={lastNameInput}
                onChange={(event) => setLastNameInput(event.target.value)}
              />
            </div>
            <div>
              <label className="text-sm">Phone Number*</label>
              <div className="flex flex-col xs:flex-row space-y-2 xs:space-y-0">
                <PhoneSelect
                  imageSrc={require("../../public/flag_US.png")}
                  regionCode="1"
                  style={{
                    flex: "1 1 0%",
                    marginRight: 12,
                  }}
                  onChange={(target) => setPhoneRegion(target)}
                  value={phoneRegion}
                />
                <AuthInput
                  placeholder="Your phone number..."
                  value={phoneInput}
                  error={!phoneAvailable}
                  errorMessage={errorMessagePhone}
                  onChange={(event) => setFormattedPhoneNumber(event.target.value)}
                  style={{
                    flex: "1 1 0%",
                  }}
                />
              </div>
            </div>
            <div>
              <label className="text-sm">Email*</label>
              <AuthInput
                placeholder="Input your email here..."
                value={emailAddressInput}
                error={!emailAvailable}
                errorMessage={errorMessageEmail}
                onChange={(event) => setEmailAddressInput(event.target.value.trim())}
              />
              {errorMessageEmail === "" && emailAvailable && (
                <p className="text-sm text-[#667085] -mt-2">Please enter email address</p>
              )}
            </div>
            <div className="flex flow-row justify-between gap-x-3">
              <div className="flex-1">
                <label className="text-sm">Password*</label>
                <AuthInput
                  placeholder="Input your password..."
                  value={passwordHook.pwd}
                  error={passwordHook.isPasswordInvalid}
                  errorMessage={passwordHook.passwordErrorMsg}
                  type="password"
                  onChange={(event) => passwordHook.setPwd(event.target.value.trim())}
                />
                {!passwordHook.isPasswordInvalid && (
                  <p className="text-sm text-[#667085] -mt-2">
                    Your password must have 8 characters, a capital letter, and a number.
                  </p>
                )}
              </div>
              <div className="hidden flex-1 md:block">
                <label className="text-sm">Confirmation Password</label>
                <AuthInput
                  placeholder="Repeat your password..."
                  value={passwordHook.confirmPwd}
                  error={passwordHook.isPasswordInvalid}
                  errorMessage={passwordHook.passwordErrorMsg}
                  type="password"
                  onChange={(event) => passwordHook.setConfirmPwd(event.target.value.trim())}
                />
              </div>
            </div>
            <div className="block md:hidden">
              <label className="text-sm">Confirmation Password</label>
              <AuthInput
                placeholder="Repeat your password..."
                value={passwordHook.confirmPwd}
                error={passwordHook.isPasswordInvalid}
                errorMessage={passwordHook.passwordErrorMsg}
                type="password"
                onChange={(event) => passwordHook.setConfirmPwd(event.target.value.trim())}
              />
            </div>
          </div>
          <div className="flex flex-row items-center justify-start my-4 opacity-80 text-sm">
            <input
              type="checkbox"
              checked={isAgreed}
              onChange={(event: any) => setIsAgreed(event.target.checked)}
              className="mr-3"
            />
            <span
              className="leading-tight cursor-pointer"
              onClick={(event: any) => setIsAgreed((prev) => !prev)}
            >
              I agree to the{" "}
              <button
                className="underline"
                onClick={(e) => {
                  setIsOnTermsOfuse(true);
                  e.stopPropagation();
                }}
              >
                Terms and Conditions
              </button>{" "}
              and have reviewed the{" "}
              <button
                className="underline"
                onClick={(e) => {
                  setIsOnPrivacyPolicy(true);
                  e.stopPropagation();
                }}
              >
                Privacy Policy
              </button>
              .
            </span>
          </div>
          <AuthButton
            title="Create Account"
            onClick={() => setIsConfirmEmailAddresModalOn(true)}
            disabled={!isSubmittable || isLoading}
            isLoading={isLoading}
            isFilled
          />
          <div style={{ height: 16 }} />
          <div className="flex flex-row w-full items-center border-[0.5px] border-[#DDDDDD] mt-4 xl:hidden" />
          <div className="hidden xl:block" style={{ height: 60 }} />
          <div className="block xl:hidden" style={{ height: 24 }} />
          <p className="flex flex-col items-center justify-between text-center text-md text-[#5A6068] font-light xl:hidden mt-6">
            <span>Already have an account? </span>
            <span
              className="px-1 underline font-medium text-black cursor-pointer text-2xl"
              onClick={() => router.push("/login")}
            >
              Sign In
            </span>
          </p>
          <div className="block xl:hidden" style={{ height: 64 }} />
        </div>
        <div className="hidden flex-row place-self-start xl:block">
          <p className="text-[#667085] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
        </div>
      </div>
      {/* <div className="flex xl:hidden items-center justify-start w-screen">
        <AuthTestimonialCards style={{ width: "100vW" }} />
      </div> */}
    </div>
  );
};

export default SignUpPage;
