export type GoogleFreeBusy = {
  kind: "calendar#freeBusy";
  timeMin: string;
  timeMax: string;
  groups: {
    [key: string]: {
      errors: {
        domain: string;
        reason: string;
      }[];
      calendars: string[];
    };
  };
  calendars: {
    [key: string]: {
      errors: {
        domain: string;
        reason: string;
      }[];
      busy: {
        start: string;
        end: string;
      }[];
    };
  };
};

const TestPage = () => {
  return (
    <div>
      <div className="my-spinner">
        <div className="spinner-item" />
        <div className="spinner-item" />
        <div className="spinner-item" />
      </div>
    </div>
  );
};

export default TestPage;
