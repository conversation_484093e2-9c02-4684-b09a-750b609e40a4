import { Amplify, Auth } from "aws-amplify";
import { getCookie } from "cookies-next";
import type { AppProps } from "next/app";
import { useRouter } from "next/router";
import { GoogleAnalytics } from "nextjs-google-analytics";
import { useEffect } from "react";
import { Provider } from "react-redux";
import "../../styles/globals.css";
import awsconfig from "../aws-exports";
import CookieConsent from "../components/molecule/CookieConsent";
import { Mixpanel } from "../functions/mixpanel";
import { store } from "./../@redux/index";
import GlobalContextProvider from "../contexts/GlobalContext";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";
import Script from "next/script";
import GoogleCaptchaWrapper from "../components/GoogleCaptchaWrapper";

awsconfig.oauth.redirectSignIn =
  process.env.NEXT_PUBLIC_REDIRECT_SIGNIN || awsconfig.oauth.redirectSignIn;
awsconfig.oauth.redirectSignOut =
  process.env.NEXT_PUBLIC_REDIRECT_SIGNIN || awsconfig.oauth.redirectSignOut;

Amplify.configure(awsconfig);

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter();

  useEffect(() => {
    (async () => {
      try {
        const user = await Auth.currentAuthenticatedUser();
      } catch (error) {
        const allowedRoutes = ["/login_9184"];

        // For testing purposes I created another login page for the production site.
        if (!allowedRoutes.includes(router.pathname)) {
          router.push("/login");
        }
        // router.push("/login");
      }
    })();
  }, []);

  useEffect(() => {
    function handleRouteChange(url: string) {
      if (getCookie("localConsent") === true) {
        Mixpanel.track("Page View", {
          url,
        });
      }
    }

    router.events.on("routeChangeComplete", handleRouteChange);
    return () => router.events.off("routeChangeComplete", handleRouteChange);
  }, [router.events]);

  return (
    <>
      <script
        defer
        src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`}
      ></script>
      {/* <Script
        strategy="beforeInteractive"
        src={`https://www.google.com/recaptcha/api.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_KEY}`}
      /> */}
      <CookieConsent />
      <GoogleAnalytics trackPageViews />
      <GlobalContextProvider>
        <Provider store={store}>
          <GoogleCaptchaWrapper>
            <Component {...pageProps} />
          </GoogleCaptchaWrapper>
        </Provider>
      </GlobalContextProvider>
    </>
  );
}

export default MyApp;
