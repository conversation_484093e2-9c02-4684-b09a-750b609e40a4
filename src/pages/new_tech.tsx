import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import AuthInput from "../components/atom/AuthInput";
import Layout from "../components/Layout";
import { apiListServices } from "../functions/api/service";
import { Service } from "../types";
import { Color } from "../types/Color";

const BecomeTechnicianPage = () => {
  const router = useRouter();
  const [locationInput, setLocationInput] = useState<string>("");
  const [serviceInput, setServiceInput] = useState<Service>();
  const [services, setServices] = useState<Service[]>();

  useEffect(() => {
    const fetchServices = async () => {
      const result = await apiListServices();
      setServices(result);
      setServiceInput(result[0]);
    };
    fetchServices();
  }, []);

  return (
    <Layout>
      <div
        className="w-full h-full flex justify-center items-center"
        style={{
          backgroundImage: 'url("/tech_signup_background.png")',
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
        }}
      >
        <div className="flex flex-col mr-14">
          <h1
            className="text-5xl text-[rgba(255,255,255,0.8)] leading-tight mb-8"
            style={{
              fontFamily: "Spectral",
              fontWeight: 700,
              width: 570,
            }}
          >
            Choose your hours, {<br />}earn money your way
          </h1>
          <p
            className="text-[rgba(255,255,255,0.8)] leading-relaxed mb-16"
            style={{
              fontWeight: 300,
              fontFamily: "Proza Libre",
              width: 300,
            }}
          >
            Join our team of technicians making auto service easier for everyone
          </p>
          <button
            className="text-white"
            style={{
              backgroundColor: Color.ACCENT,
              width: 297,
              height: 56,
            }}
            onClick={() => {
              router.push("/new_tech_info");
            }}
          >
            Create Account
          </button>
        </div>
        <div
          className="flex flex-col bg-[rgba(242,243,244,0.6)] rounded-md"
          style={{
            padding: "80px 60px",
          }}
        >
          <span className="text-lg font-semibold mb-2">Which area are you located?</span>
          <span className="text-[rgba(0,0,0,0.6)] text-sm mb-1">Location</span>
          <AuthInput
            value={locationInput}
            onChange={(event) => setLocationInput(event.target.value)}
            placeholder=""
          />
          <span className="text-lg font-semibold mt-6 mb-2">
            Which service are you most interested in providing?
          </span>
          <span className="text-[rgba(0,0,0,0.6)] text-sm mb-1">Service</span>
          <select
            className="h-10 px-2"
            value={serviceInput?.name}
            onChange={(event) => {
              const service = services?.find((item) => item.name === event.target.value);
              setServiceInput(service);
            }}
          >
            {services?.map((item) => (
              <option>{item.name}</option>
            ))}
          </select>
          <span className="mt-12 text-[rgba(0,0,0,0.4)] font-semibold mb-2">You can earn</span>
          <span className="text-4xl font-semibold text-[rgba(0,0,0,0.8)]">
            {`$${serviceInput?.price.toFixed(2)} `}
            <span className="text-sm font-normal">per hour</span>
          </span>
        </div>
      </div>
    </Layout>
  );
};

export default BecomeTechnicianPage;
