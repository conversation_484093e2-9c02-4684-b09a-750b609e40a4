import Head from "next/head";
import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import DeleteAccountModal from "../../components/Modal/DeleteAccountModal";
import Paper from "../../components/atom/Paper";
import IndigoBlueText16 from "../../components/atom/Texts/IndigoBlueText16";
import IndigoBlueTitle18 from "../../components/atom/Texts/IndigoBlueTitle18";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";
import CustomerLayout from "../../components/organism/CustomerLayout";
import AddressBlock from "../../pageComponents/customer_profile/AddressBlock";
import InformationBlock from "../../pageComponents/customer_profile/InformationBlock";
import VehicleBlock from "../../pageComponents/customer_profile/VehicleBlock";
import { Color } from "../../types/Color";
import DeleteAccountBlock from "../../pageComponents/customer_profile/DeleteAccountBlock";

const CustomerProfile = () => {
  const user = useSelector((state: RootState) => state.auth.user);

  const [isDeleteAccountModalOn, setIsDeleteAccountModalOn] = useState<boolean>(false);

  return (
    <CustomerLayout>
      <Head>
        <title>Profile (Customer) - Wheel Easy</title>
      </Head>
      <DeleteAccountModal
        isOn={isDeleteAccountModalOn}
        onOff={() => setIsDeleteAccountModalOn(false)}
        isLoading={false}
      />
      <div className="w-full flex flex-col items-center py-8 px-4 overflow-y-scroll h-[calc(var(--vh,1vh)*100-54px)] lg:h-[calc(var(--vh,1vh)*100-64px)]">
        <div className="md:min-w-[700px] flex flex-col w-full 2xl:w-[1088px]">
          <InformationBlock />
          <AddressBlock />
          <div className="mt-6">
            <VehicleBlock />
          </div>
          <div className="mt-6">
            <Paper borders="rounded-xl">
              <ProfileBlockHeader title="Account Information" />
              <div
                className="grid grid-cols-1 md:grid-cols-3 gap-y-4 mb-5 border-t-[0.5px] px-4 pt-6 pb-2 md:px-6"
                style={{
                  borderColor: Color.SLIVER_GRAY,
                }}
              >
                <IndigoBlueTitle18 style={{ lineHeight: "24.3px" }}>Joined in</IndigoBlueTitle18>
                <IndigoBlueText16 style={{ paddingTop: 2, lineHeight: "21.6px" }}>
                  {user?.createdDateTime.split("T")[0]}
                </IndigoBlueText16>
              </div>
              {/* <DeleteAccountBlock onClick={() => setIsDeleteAccountModalOn(true)} /> */}
            </Paper>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CustomerProfile;
