import Image from "next/image";
import React, { useRef, useState } from "react";
import Layout from "../components/Layout";
import imageCompression from "browser-image-compression";
import { useRouter } from "next/router";
import { Color } from "../types/Color";

const NumberInCircle = ({ children }: { children: string }) => {
  return (
    <div
      className={`flex justify-center items-center rounded-full border mb-5`}
      style={{
        width: 48,
        height: 48,
        borderColor: Color.ACCENT,
      }}
    >
      <span style={{ color: Color.ACCENT, fontSize: 27 }}>{children}</span>
    </div>
  );
};

const Title = ({ children }: { children: string }) => (
  <h3 className="text-2xl font-medium">{children}</h3>
);

const TextInput = (props: { title: string; value: string; onChange: (val: string) => void }) => (
  <div className="flex flex-col ">
    <span className="text-[rgba(0,0,0,0.4)] mb-1">{props.title}</span>
    <input
      className="border rounded-md px-3 py-2"
      type="text"
      value={props.value}
      onChange={(e) => props.onChange(e.target.value)}
    />
  </div>
);

const NewTechInfoPage = () => {
  const [emailAddress, setEmailAddress] = useState<string>("");
  const [confirmEmailAddress, setConfirmEmailAddress] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [phoneInput, setPhoneInput] = useState<string>("");
  const [uploadedImage, setUploadedImage] = useState<string>();
  const [uploadedFileName, setUploadedFileName] = useState<string>();
  const inputRef = useRef<any>(null);

  const router = useRouter();

  async function handleImageUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const imageFile = event.target.files![0];
    setUploadedFileName(inputRef.current.files[0].name);

    try {
      const encodedFile = await imageCompression.getDataUrlFromFile(imageFile);
      setUploadedImage(encodedFile);
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <Layout>
      <div className="flex mt-10">
        <div className="flex flex-col ">
          <NumberInCircle>1</NumberInCircle>
          <Title>Account</Title>
          <span className="text-[rgba(0,0,0,0.6)] mt-1 mb-5">
            Information you'll use to sign in.
          </span>
          <div className="flex items-center space-x-10 mb-8">
            <TextInput title="Email address" value={emailAddress} onChange={setEmailAddress} />
            <TextInput
              title="Confirm email address"
              value={confirmEmailAddress}
              onChange={setConfirmEmailAddress}
            />
          </div>
          <div className="flex items-center space-x-10 mb-14">
            <TextInput title="Password" value={password} onChange={setPassword} />
            <TextInput
              title="Confirm password"
              value={confirmPassword}
              onChange={setConfirmPassword}
            />
          </div>

          <NumberInCircle>2</NumberInCircle>
          <Title>Contact Info</Title>
          <span className="text-[rgba(0,0,0,0.6)] mt-1 mb-5">
            Information you provide us will be used to create your Wheel Easy technician account.
          </span>
          <div className="flex mb-14">
            <div className="flex flex-col mr-10 space-y-8">
              <TextInput title="First name" value={emailAddress} onChange={setEmailAddress} />
              <TextInput title="Phone number" value={phoneInput} onChange={setPhoneInput} />
            </div>
            <TextInput
              title="Last name"
              value={confirmEmailAddress}
              onChange={setConfirmEmailAddress}
            />
          </div>

          <NumberInCircle>3</NumberInCircle>
          <Title>Services</Title>
          <span className="text-[rgba(0,0,0,0.6)] mt-1 mb-5">
            The services you’ll provide and the areas at which you’ll be providing your services.
          </span>
          <div className="flex border rounded-sm mb-10" style={{ height: 180 }}>
            <div className="flex flex-col border-r flex-1 justify-center items-center">
              <div
                className="flex justify-center items-center mb-2"
                style={{
                  width: 40,
                  height: 40,
                }}
              >
                <Image src={require("../../public/location.png")} />
              </div>
              <span className="text-lg font-medium text-[#152D4E]">London, Ontario</span>
            </div>
            <div className="flex flex-col flex-1 justify-center items-center">
              <div
                className="flex justify-center items-center mb-2"
                style={{
                  width: 40,
                  height: 40,
                }}
              >
                <Image src={require("../../public/car_service.png")} />
              </div>
              <span className="text-lg font-medium text-[#152D4E]">Oil Change</span>
            </div>
          </div>
        </div>
        <div
          className="flex flex-col bg-[#F2F3F4] p-10 rounded-sm self-baseline"
          style={{
            width: 477,
          }}
        >
          <NumberInCircle>4</NumberInCircle>
          <h4 className="text-2xl font-medium mb-8">Upload a picture of your mechanic license.</h4>
          {uploadedImage !== undefined ? <img src={uploadedImage} /> : undefined}
          <div
            className="bg-[#DADDE0] flex justify-center items-center rounded-lg mb-3"
            style={{
              height: 208,
            }}
          >
            <label className="flex items-center cursor-pointer" htmlFor="file-input">
              <div className="flex justify-center items-center w-5 h-5 mr-1">
                <Image src={require("../../public/dark_upload.png")} />
              </div>
              <span className="text-[#152D4E]">Upload Picture</span>
            </label>
            <input
              id="file-input"
              ref={inputRef}
              type="file"
              className="hidden"
              onChange={handleImageUpload}
            />
          </div>
          <span className="text-[rgba(0,0,0,0.4)] text-sm mb-5">
            Only images less than 5MB are accepted.
          </span>
          <p className="mb-10">
            Your mechanic license will be reviewed and Wheel Easy will contact you back for more
            information.
          </p>
          <button
            onClick={() => router.push("/new_tech_confirm")}
            className="bg-black rounded-md py-3 text-white"
          >
            Submit Request
          </button>
        </div>
      </div>
    </Layout>
  );
};

export default NewTechInfoPage;
