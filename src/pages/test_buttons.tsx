import AuthButton from "../components/atom/Buttons/AuthButton";
import SmallButton from "../components/atom/Buttons/base/SmallButton";

const ComponentTitle = ({ children }: { children: string }) => (
  <h3
    className="font-semibold mt-8 mb-3 text-xl"
    style={{
      fontFamily: "Spectral",
    }}
  >
    {children}
  </h3>
);

const TestButtons = () => {
  return (
    <div className="flex flex-col">
      <ComponentTitle>AuthButton</ComponentTitle>
      <AuthButton title="Click" onClick={() => {}} isFilled={true} />
      <AuthButton title="Click" onClick={() => {}} isFilled={false} />
      <ComponentTitle>RoundedButton</ComponentTitle>
      <SmallButton
        title="Click"
        onClick={() => {}}
        disabled={false}
        style={{
          alignSelf: "baseline",
        }}
      />
    </div>
  );
};

export default TestButtons;
