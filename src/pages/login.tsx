import { Next<PERSON>outer, useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import Head from "next/head";
import { event } from "nextjs-google-analytics";
import AuthButton from "../components/atom/Buttons/AuthButton";
import GoogleAuthButton from "../components/atom/Buttons/GoogleAuthButton";
import AuthTestimonialCards from "../components/atom/Testimonials/AuthTestimonialCards";
import { apiGetUser } from "../functions/api/user";
import { apiResendConfirmCode, apiSignIn } from "../functions/cognito";
import useGoogleSignIn from "../hooks/useGoogleSignIn";
import { RoleId } from "../types/User";
import { EyeOffIcon, EyeOnIcon } from "../components/atom/Icons";
import AuthHeaderMobile from "../components/organism/AuthHeaderMobile";
import SizeableImage from "../components/atom/SizeableImage";
import { Mixpanel } from "../functions/mixpanel";
import { getCookie, hasCookie } from "cookies-next";
import SignUpDisabledModal from "../pageComponents/signup/SignUpDisabledModal";
import { apiSendMessageToSlack } from "../functions/api/slack";
import { apiSendEmail } from "../functions/api/email";

export async function fetchUser(router: NextRouter) {
  const userResult = await apiGetUser();
  console.log("userResult", userResult);
  if (userResult.roleId === RoleId.CUSTOMER) {
    await router.push("/customer_booking");
  } else if (userResult.roleId === RoleId.TECHNICIAN || userResult.roleId === RoleId.ADMIN) {
    console.log("tech or admin");
    await router.push("/tech_booking");
  }
}

const LoginPage = () => {
  const [username, setUsername] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [SignUpDisabledModalIsOn, setSignUpDisabledModalIsOn] = useState<boolean>(false);

  const [error, setError] = useState<boolean>(false);
  const router = useRouter();
  const { isGoogleLoading, handleOnClickSignInWithGoogle } = useGoogleSignIn(router);

  const [inputType, setInputType] = useState<string>("password");
  //making a comment so I have something to push

  // console.log("NEXT_PUBLIC_IS_DEV", process.env.NEXT_PUBLIC_IS_DEV);

  const isSignInable = useMemo(() => {
    return username.trim() !== "" && password.trim() !== "";
  }, [username, password]);

  const handleToggleVisibility = () => {
    setInputType((prev) => (prev === "password" ? "text" : "password"));
  };

  function handleKeyDown(e: any) {
    if (e.keyCode === 13) {
      handleOnClickLogin();
    }
  }

  async function handleOnClickSignUp() {
    if (getCookie("localConsent") === true) {
      Mixpanel.track("Sign Up Clicked");
    }
    router.push("/signup");
    // setSignUpDisabledModalIsOn(true);
    // await apiSendEmail({ email: "string;", subject: "string;", body: "string;", html: false });
  }

  async function handleOnClickLogin() {
    setIsLoading(true);
    try {
      console.log("cookie consent", getCookie("localConsent"));
      if (getCookie("localConsent") === true) {
        Mixpanel.track("Sign In Clicked");
      } else {
        console.log("no cookie consent");
      }
      const user = await apiSignIn(username, password);
      console.log("user", user);
      event("login", {
        category: "Auth",
        label: "Sign in with email and password.",
      });
      if (user.challengeName === "NEW_PASSWORD_REQUIRED") {
        // Redirect to reset password page with username and session details
        router.push({
          pathname: "/reset_password",
          query: {
            username: user.username,
            session: JSON.stringify(user.Session),
            tmpPwd: password,
          },
        });
      } else {
        console.log("fetch user");
        await fetchUser(router);
      }
    } catch (error: any) {
      if (error.name === "UserNotConfirmedException") {
        if (getCookie("localConsent") === true) {
          Mixpanel.track("Unconfirmed User Sign In Clicked");
        }
        await apiResendConfirmCode(username);
        await router.push({
          pathname: "/confirm",
          query: {
            username,
          },
        });
      } else {
        console.log(error);
        setError(true);
        setIsLoading(false);
      }
    } finally {
      setIsLoading(false);
    }
  }

  async function handleOnClickForgotPassword() {
    if (getCookie("localConsent") === true) {
      Mixpanel.track("Forgot Password Clicked");
    }
    router.push({
      pathname: "/forgot_password",
    });
  }

  return (
    <div className="flex flex-col xl:flex-row items-center justify-between h-full select-none overflow-x-hidden xl:overflow-hidden min-w-[320px]">
      <SignUpDisabledModal
        isOn={SignUpDisabledModalIsOn}
        onOff={() => setSignUpDisabledModalIsOn(false)}
        onClickButton={async () => {
          handleOnClickSignInWithGoogle;
        }}
        isLoading={isLoading}
      />
      <Head>
        <title>Sign In - Wheel Easy</title>
      </Head>
      <AuthHeaderMobile />
      <div className="hidden xl:flex h-full">
        <AuthTestimonialCards />
      </div>
      <div className="flex grow self-stretch flex-col xl:justify-between h-full w-full items-start px-5 xl:py-10 xl:px-14 overflow-y-scroll">
        <div className="flex flex-row w-full justify-end">
          <p className="hidden text-center text-md text-[#5A6068] font-light xl:flex flex-col items-end">
            Don{"'"}t have an account?{" "}
            <span
              className="pl-1 underline font-medium text-black text-2xl cursor-pointer "
              onClick={handleOnClickSignUp}
            >
              Sign Up
            </span>
          </p>
        </div>
        <div className="flex flex-col justify-center items-start mt-6 xl:mt-5 w-full xl:w-[400px]">
          {/* <h1 className="text-3xl mb-2 font-medium text-start">Down for Maintenance</h1>
          <p className="text-[#5A6068] mb-2 text-start font-light">
            Wheel Easy is Currently Down for Maintenance.
          </p>
          <p className="text-[#5A6068] mb-2 text-start font-light">
            In the meantime, please call or email us to book an appointment.
          </p>
          <p className="text-[#5A6068] mb-4 text-start font-light">
            Thank you for your understanding.
          </p>
          <h2 className="text-sm font-medium text-start">+1 (519) 870-4546</h2>
          <h2 className="text-sm mb-2 font-medium text-start"><EMAIL></h2>
          <div style={{ height: 40 }} /> */}
          <h1 className="text-3xl mb-2 font-medium text-start">Welcome to Wheel Easy</h1>
          <p className="text-[#5A6068] mb-2 text-start font-light">
            Please enter your details to log into your account.
          </p>
          <div style={{ height: 40 }} />
          <div className="flex flex-col items-stretch xl:w-[400px] w-full">
            <label className="text-sm">Email</label>
            <div className="relative flex flex-col items-stretch justify-center mb-3">
              <input
                placeholder="Input your email here..."
                value={username}
                onChange={(event) => setUsername(event.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isGoogleLoading ? true : false}
                className={`border px-3.5 py-2.5 placeholder-[rgba(0,0,0,0.3)] focus:outline-[#FCA311] rounded-xl drop-shadow-sm ${
                  error ? "border-[#FDA29B]" : ""
                }`}
              />
              <span className="absolute flex items-center justify-center right-0 mr-3">
                {error && (
                  <SizeableImage size={16} src={require("../../public/alert-circle.png")} />
                )}
              </span>
            </div>
            <label className="text-sm">Password</label>
            <div className="relative flex flex-col items-stretch justify-center mb-3">
              <input
                placeholder="Input your password here..."
                value={password}
                type={inputType}
                onChange={(event) => setPassword(event.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isGoogleLoading ? true : false}
                className={`border px-3.5 py-2.5 placeholder-[rgba(0,0,0,0.3)] focus:outline-[#FCA311] rounded-xl drop-shadow-sm ${
                  error ? "border-[#FDA29B]" : ""
                }`}
              />
              <span
                className="absolute flex items-center justify-center right-0 mr-3"
                onClick={handleToggleVisibility}
              >
                <SizeableImage
                  size={20}
                  src={
                    inputType === "password"
                      ? require("../../public/eye-off.png")
                      : require("../../public/eye-on.png")
                  }
                />
                {error && (
                  <SizeableImage
                    size={16}
                    src={require("../../public/alert-circle.png")}
                    style={{
                      marginLeft: 8,
                    }}
                  />
                )}
              </span>
            </div>
            {error && (
              <p className="text-[#F04438] text-sm -mt-2">Incorrect username or password.</p>
            )}
            <div className="flex items-center justify-between mb-10">
              {process.env.NEXT_PUBLIC_IS_DEV === "true" ? (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      setUsername("<EMAIL>");
                      setPassword("Qwerty1234!");
                    }}
                  >
                    CS
                  </button>
                  <button
                    onClick={() => {
                      setUsername("<EMAIL>");
                      setPassword("Qwerty1234!");
                    }}
                  >
                    TC
                  </button>
                  <button
                    onClick={() => {
                      setUsername("<EMAIL>");
                      setPassword("Qwerty1234!");
                    }}
                  >
                    AD
                  </button>
                </div>
              ) : (
                <div />
              )}

              <button
                className="text-[#FCA311] text-sm font-medium"
                onClick={handleOnClickForgotPassword}
              >
                Forgot password?
              </button>
            </div>
            <AuthButton
              title="Sign In"
              onClick={handleOnClickLogin}
              isFilled
              disabled={isLoading || isGoogleLoading || !isSignInable}
              isLoading={isLoading}
            />
            <div style={{ height: 16 }} />
            <GoogleAuthButton
              title="Sign in with Google"
              isFilled={false}
              onClick={handleOnClickSignInWithGoogle}
              disabled={isGoogleLoading || isLoading}
              isLoading={isGoogleLoading}
            />
            <div style={{ height: 24 }} />
            <p className="flex items-center justify-center text-center text-md text-[#5A6068] font-light xl:hidden flex-col">
              <span>Don{"'"}t have an account? </span>
              <span
                className="pl-1 underline font-medium text-black cursor-pointer text-2xl"
                onClick={handleOnClickSignUp}
              >
                Sign Up
              </span>
            </p>
          </div>
        </div>
        <div className="xl:hidden" style={{ paddingBottom: 107 }} />
        <div className="hidden xl:flex">
          <div className="flex flex-row place-self-start">
            <p className="text-[#667085] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
          </div>
        </div>
      </div>
      {/* <div className="flex xl:hidden items-center justify-start w-screen">
        <AuthTestimonialCards style={{ width: "100vW" }} />
      </div> */}
    </div>
  );
};

export default LoginPage;
