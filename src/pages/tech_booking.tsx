import { NextRouter, useRouter } from "next/router";
import { useEffect, useMemo, useRef, useState } from "react";
import BorderSelect from "../components/atom/BorderSelect";
import Spinner from "../components/atom/Spinner";
import TextInput from "../components/atom/TextInput";
import Text14 from "../components/atom/Texts/base/Text14";
import Title24 from "../components/atom/Texts/base/Title24";
import GrayText14 from "../components/atom/Texts/GrayText14";
import TimeSelect from "../components/atom/TimeSelect";
import ConfirmCancelModal from "../components/Modal/ConfirmCancelModal";
import Calendar from "../components/organism/Calendar";
import TechnicianLayout from "../components/organism/TechnicianLayout";
import { useAvailabilities } from "../hooks/useAvailabilities";
import { useTechnicianBooking } from "../hooks/useTechnicianBooking";
import AvailabilityModal from "../components/Modal/AvailabilityModal";
import AvailabilitySection from "../pageComponents/tech_booking/AvailabilitySection";
import CalendarSyncOptionsModal from "../components/Modal/CalendarSyncOptionsModal";
import { CalendarService } from "../services/CalendarService";
import { Color } from "../types/Color";
import { hourList } from "../values";
import Head from "next/head";

const BookingPage = () => {
  const router = useRouter();
  const availabilitiesHook = useAvailabilities();
  const calendarHook = useTechnicianBooking();

  // console.log("calendarHook", calendarHook);

  const [isManualModalOn, setIsManualModalOn] = useState<boolean>(false);
  const [isCalendarSyncOptionsModalOn, setIsCalendarSyncOptionsModalOn] = useState<boolean>(false);
  const [manualModalStartTimeInput, setManualModalStartTimeInput] = useState<string | "none">(
    "none"
  );
  const [duration, setDuration] = useState<string | number>("none");
  const [descriptionInput, setDescriptionInput] = useState<string>("");
  const [isLoadingManualBlock, setIsLoadingManualBlock] = useState<boolean>(false);

  const isFirstCall = useRef<boolean>(true);

  useEffect(() => {
    if (!isFirstCall.current) return;
    else isFirstCall.current = false;

    if (isRoutedWithQueryParams(router)) {
      const params = new URLSearchParams(router.asPath.split("?")[1]);
      const code = params.get("code");
      if (code) {
        saveAccessTokenFromAuth(code);
      } else {
        alert("Failed to retrieve auth code.");
      }
    }
  }, []);

  useEffect(() => {
    // console.log(hourList);
  }, [hourList]);

  function isRoutedWithQueryParams(router: NextRouter) {
    return router.asPath !== router.pathname;
  }

  async function saveAccessTokenFromAuth(code: string) {
    try {
      const result = await CalendarService.saveGoogleTokens(code);
      router.replace("/tech_booking");

      if (result === "SUCCESS") {
        setIsCalendarSyncOptionsModalOn(true);
      } else {
        alert("Error occurred while saving access token. Please try again.");
      }
    } catch (error) {
      console.log(error);
    }
  }

  const durationOptions = useMemo(() => {
    let result = [];
    result.push(<option key="none" value="none">Duration</option>);
    if (manualModalStartTimeInput === "none") {
    } else {
      let hour = parseInt(manualModalStartTimeInput.substring(0, 2));
      let minute = parseInt(manualModalStartTimeInput.substring(3));
      let i = 15;

      while (!(hour === 24 && minute === 0)) {
        result.push(<option key={i} value={i}>{i} minutes</option>);
        i += 15;
        minute += 15;
        if (minute === 60) {
          hour++;
          minute = 0;
        }
      }
    }

    return result;
  }, [manualModalStartTimeInput]);

  async function onClickSyncWithGoogleCalendar() {
    try {
      const result = await CalendarService.getGoogleOAuthLink();
      router.replace(result.uri);
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <TechnicianLayout>
      <Head>
        <title>Bookings (Technician) - Wheel Easy</title>
      </Head>
      <CalendarSyncOptionsModal
        isOn={isCalendarSyncOptionsModalOn}
        onOff={() => {
          setIsCalendarSyncOptionsModalOn(false);
          calendarHook.fetchCalendarData();
        }}
      />
      <ConfirmCancelModal
        isOn={isManualModalOn}
        onOff={() => setIsManualModalOn(false)}
        title="Manual Booking"
        description="Lorem ipsum adding booking"
        webHeight={500}
        content={
          <>
            <GrayText14>Date</GrayText14>
            <Text14
              style={{
                marginBottom: 16,
              }}
            >
              2022.
            </Text14>
            <div className="flex items-center mb-5">
              <div className="flex flex-col mr-3">
                <GrayText14>From</GrayText14>
                <TimeSelect
                  value={manualModalStartTimeInput}
                  onChange={(val) => setManualModalStartTimeInput(val)}
                  style={{ width: 140 }}
                  values={hourList}
                  placeHolderName="Start Time"
                />
              </div>
              <div className="flex flex-col ">
                <GrayText14>Duration</GrayText14>
                <BorderSelect
                  value={duration}
                  onChange={(val) => setDuration(val)}
                  options={durationOptions}
                  style={{ width: 140 }}
                />
              </div>
            </div>
            <GrayText14>Description</GrayText14>
            <TextInput
              value={descriptionInput}
              onChange={(val) => setDescriptionInput(val)}
              placeholder=""
            />
          </>
        }
        confirmButtonTitle="Create"
        cancelButtonTitle="Cancel"
        onClickConfirmButton={async () => {
          // if (selectedDateItem && duration !== "none") {
          //   setIsLoadingManualBlock(true);
          //   const result = await apiCreateBlockedTime(
          //     {
          //       year: 2022,
          //       month: selectedDateItem?.month,
          //       day: selectedDateItem?.date,
          //       hour: parseInt(manualModalStartTimeInput.substring(0, 2)),
          //       minute: parseInt(manualModalStartTimeInput.substring(3)),
          //     },
          //     parseInt(duration!) / 15,
          //     descriptionInput
          //   );
          //   fetchCalendarData();
          //   setIsLoadingManualBlock(false);
          // }
        }}
        onClickCancelButton={() => {
          setIsManualModalOn(false);
          setManualModalStartTimeInput("none");
          setDuration("none");
          setDescriptionInput("");
        }}
        isConfirmDisabled={
          manualModalStartTimeInput === "none" || duration === "none" || descriptionInput === ""
        }
        isLoading={isLoadingManualBlock}
      />
      <div className="w-full h-full overflow-y-scroll flex flex-col">
        <AvailabilityModal
          isOn={availabilitiesHook.isAvailabilityModalOn}
          onOff={() => availabilitiesHook.setIsAvailabilityModalOn(false)}
          isLoading={availabilitiesHook.isLoading}
          availabilityInputData={availabilitiesHook.availabilityInputData}
          setAvailabilityInputData={availabilitiesHook.setAvailabilityInputData}
          handleOnClickAdd={availabilitiesHook.handleOnClickAdd}
          handleOnClickDelete={availabilitiesHook.handleOnClickDelete}
          handleOnClickUpdate={() => {
            calendarHook.fetchCalendarData();
            availabilitiesHook.handleOnClickUpdate();
          }}
          getIsAddedTime={availabilitiesHook.getIsAddedTime}
        />
        <div className="flex items-stretch h-[calc(100vh-76px)]">
          <div className="flex flex-col flex-1 items-stretch px-8 pt-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex flex-col ">
                <Title24
                  style={{
                    fontSize: 28,
                    marginBottom: 8,
                  }}
                >
                  Our Booking Schedule
                </Title24>
                <p
                  className="text-sm"
                  style={{
                    color: Color.GRAY,
                  }}
                >
                  All of your booking schedule, already show on this calendar
                </p>
              </div>
              <button
                className="text-sm font-medium rounded-lg px-4 py-2 flex items-center bg-white"
                onClick={onClickSyncWithGoogleCalendar}
                style={{
                  border: "1px solid " + Color.GRAY,
                }}
              >
                {/** Get google icon from w3*/}
                Sync with Google
              </button>
            </div>

            <AvailabilitySection
              availabilities={availabilitiesHook.availabilities}
              onClick={() => availabilitiesHook.setIsAvailabilityModalOn(true)}
            />
            {calendarHook.isLoading || calendarHook.bookingData === undefined ? (
              <div className="flex justify-center items-center h-full">
                <Spinner />
              </div>
            ) : (
              <Calendar
                onClickPrevWeek={calendarHook.calendarControllerHook.handleOnClickCalendarPrevWeek}
                onClickNextWeek={calendarHook.calendarControllerHook.handleOnClickCalendarNextWeek}
                shownDateItems={calendarHook.calendarControllerHook.getDateItemsFromOffset(7)}
                hourItems={hourList}
                onClickCalendarBlock={() => {}}
                bookingData={calendarHook.bookingData}
              />
            )}
          </div>
        </div>
      </div>
    </TechnicianLayout>
  );
};

export default BookingPage;
