import { useRouter } from "next/router";
import React from "react";
import BackgroundMiddleButton from "../components/atom/Buttons/BackgroundMiddleButton";
import Title24 from "../components/atom/Texts/base/Title24";
import GrayParagraph from "../components/atom/Texts/GrayParagraph";

const CancelPage = () => {
  const router = useRouter();

  return (
    <div className="flex flex-col items-center justify-center h-full">
      <Title24 style={{ marginBottom: 16 }}>Payment has been cancelled.</Title24>
      <GrayParagraph style={{ textAlign: "center", marginBottom: 40 }}>
        You’ve cancelled the payment. Your payment method will not be charged. <br />
        To book again please go through the booking process again, thank you!
      </GrayParagraph>
      <BackgroundMiddleButton
        title="Back to bookings page"
        onClick={() => router.push("/customer_booking")}
        backgroundColor="DARK_BLUE"
      />
    </div>
  );
};

export default CancelPage;
