import Image from "next/image";
import React, { useEffect, useState } from "react";
import AdminLayout from "../../components/organism/AdminLayout";
import {
  apiApproveTechnicianRequest,
  apiListTechnicianRequests,
  apiRejectTechnicianRequest,
} from "../../functions/api/request";
import { TechnicianRequest } from "../../types";
import { Color } from "../../types/Color";

const AdminTechniciansPage = () => {
  const [requests, setRequests] = useState<TechnicianRequest[]>();
  const [hoveredIndex, setHoveredIndex] = useState<number>(-1);

  useEffect(() => {
    const fetchRequests = async () => {
      const result = await apiListTechnicianRequests();
      setRequests(result);
    };
    fetchRequests();
  }, []);

  return (
    <AdminLayout>
      <div className="flex flex-col items-stretch flex-1 px-10 mt-10">
        <div className="flex justify-between mb-10 items-center">
          <h1 className="text-4xl font-bold">Requests</h1>
        </div>
        <div className="flex bg-[#f9fbfc] py-3 border-t border-b">
          <span
            className="text-sm px-3"
            style={{
              flex: 0.8,
            }}
          >
            Name
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 1,
            }}
          >
            Email
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 0.5,
            }}
          >
            Phone
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 1,
            }}
          >
            Service
          </span>
          <span
            className="text-sm px-3"
            style={{
              flex: 1,
            }}
          >
            Location
          </span>
          <span
            className="text-sm px-3 text-center"
            style={{
              flex: 1,
            }}
          >
            Profile/License
          </span>
          <span
            className="text-sm px-3 text-right"
            style={{
              flex: 0.5,
            }}
          >
            Function
          </span>
        </div>
        {requests?.map((item, index) => (
          <div className="flex items-center py-3 border-b">
            <span
              className="text-sm px-3"
              style={{
                flex: 0.8,
              }}
            >
              {item.name}
            </span>
            <p
              className="text-sm px-3"
              style={{
                flex: 1,
              }}
            >
              {item.email}
            </p>
            <span
              className="text-sm px-3"
              style={{
                flex: 0.5,
              }}
            >
              {item.phone}
            </span>
            <div
              className="text-sm px-3 flex flex-col space-y-2"
              style={{
                flex: 1,
              }}
            >
              {item.services.map((item, index) => (
                <span>{item.name}</span>
              ))}
            </div>
            <span
              className="text-sm px-3"
              style={{
                flex: 1,
              }}
            >
              {item.location}
            </span>
            <div
              className="text-sm px-3 flex justify-center items-center relative"
              style={{
                flex: 1,
              }}
            >
              <div
                className="bg-black text-white px-3 py-2 select-none"
                onMouseOver={() => {
                  setHoveredIndex(index);
                }}
                onMouseOut={() => {
                  setHoveredIndex(-1);
                }}
              >
                Show
              </div>
              <div
                style={{
                  visibility: hoveredIndex === index ? "visible" : "hidden",
                }}
                className="flex absolute right-24 p-5 border bg-[#EEEEEE] top-12 z-10"
              >
                <div
                  className="flex justify-center items-center bg-green-300"
                  style={{
                    width: 200,
                    height: 300,
                  }}
                >
                  {/* <Image src={require("../../../public/profile.jpg")} /> */}
                </div>
                <div
                  className="flex justify-center items-center bg-green-300"
                  style={{
                    width: 450,
                    height: 300,
                  }}
                >
                  <Image src={require("../../../public/license.png")} />
                </div>
              </div>
            </div>
            <div
              className="text-sm px-3 flex items-center justify-end"
              style={{
                flex: 0.5,
              }}
            >
              <button
                className="mr-8 font-medium"
                style={{
                  color: Color.ACCENT,
                }}
                onClick={async () => {
                  if (confirm("Approve check")) {
                    await apiApproveTechnicianRequest(item.id);
                    setRequests((prev) => prev?.filter((it) => it.id !== item.id));
                  }
                }}
              >
                Approve
              </button>
              <button
                className="font-medium"
                style={{
                  color: Color.ACCENT,
                }}
                onClick={async () => {
                  if (confirm("Deny check")) {
                    await apiRejectTechnicianRequest(item.id);
                    setRequests((prev) => prev?.filter((it) => it.id !== item.id));
                  }
                }}
              >
                Reject
              </button>
            </div>
          </div>
        ))}
      </div>
    </AdminLayout>
  );
};

export default AdminTechniciansPage;
