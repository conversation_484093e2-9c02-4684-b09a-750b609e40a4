import { useEffect, useState } from "react";
import Title24 from "../components/atom/Texts/base/Title24";
import TechnicianLayout from "../components/organism/TechnicianLayout";
import { Color } from "../types/Color";
import Head from "next/head";
import { apiListAllTechnicians } from "../functions/api/technician";
import TechnicianSection from "../pageComponents/tech_manage/TechnicianSection";
import InviteTechnicianModal from "../components/Modal/InviteTechnicianModal";
import EditTechnicianModal from "../components/Modal/EditTechnicianModal";
import CarServiceContextProvider from "../contexts/CarServiceContext";

const ManageTechnicians = () => {
  const [isFetchLoading, setIsFetchLoading] = useState<boolean>(true);
  const [technicians, setTechnicians] = useState<any[]>([]);
  const [isInviteTechnicianModalModalOn, setIsInviteTechnicianModalModalOn] =
    useState<boolean>(false);

  // State to control EditTechnicianModal
  const [isEditTechnicianModalOn, setIsEditTechnicianModalOn] = useState<boolean>(false);
  const [selectedTechnicianId, setSelectedTechnicianId] = useState<number | null>(null);

  useEffect(() => {
    fetchTechnicians();
    console.log("fetchTechnicians");
  }, []);

  async function fetchTechnicians() {
    setIsFetchLoading(true);
    const result = await apiListAllTechnicians();
    console.log("apiListAllTechnicians: ", result);

    setTechnicians(result);
    setIsFetchLoading(false);
  }

  return (
    <CarServiceContextProvider>
      <TechnicianLayout>
        <InviteTechnicianModal
          isOn={isInviteTechnicianModalModalOn}
          onOff={() => setIsInviteTechnicianModalModalOn(false)}
          fetchTechnicians={fetchTechnicians}
        />
        {/* Edit Technician Modal */}
        {selectedTechnicianId && (
          <EditTechnicianModal
            isOn={selectedTechnicianId !== null}
            onOff={() => setSelectedTechnicianId(null)}
            technicianId={selectedTechnicianId}
          />
        )}
        <Head>
          <title>Manage Technicians - Wheel Easy</title>
        </Head>

        <div className="flex items-stretch h-[calc(100vh-76px)]">
          <div className="flex flex-col flex-1 items-stretch px-8 pt-8 overflow-auto">
            <div className="flex items-center justify-between mb-6">
              <div className="flex flex-col ">
                <Title24
                  style={{
                    fontSize: 28,
                    marginBottom: 8,
                  }}
                >
                  Manage Technicians
                </Title24>
                <p
                  className="text-sm"
                  style={{
                    color: Color.GRAY,
                  }}
                >
                  Invite and remove your technicians or manage their roles.
                </p>
              </div>
              <button
                onClick={() => {
                  setIsInviteTechnicianModalModalOn(true);
                }}
                className={`text-[${Color.ACCENT}] hover:bg-[#F89A000D] hover:font-semibold border rounded-lg px-4 py-2`}
                style={{ borderColor: Color.ACCENT }}
              >
                Add New Technician
              </button>
            </div>
            <TechnicianSection
              technicians={technicians}
              onEditTechnician={(technicianId: number) => {
                setSelectedTechnicianId(technicianId);
              }}
            />
          </div>
        </div>
      </TechnicianLayout>
    </CarServiceContextProvider>
  );
};

export default ManageTechnicians;
