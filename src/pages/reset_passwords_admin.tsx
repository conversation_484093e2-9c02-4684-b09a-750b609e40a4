import { useEffect, useState } from "react";
import Title24 from "../components/atom/Texts/base/Title24";
import TechnicianLayout from "../components/organism/TechnicianLayout";
import { Color } from "../types/Color";
import Head from "next/head";
import { apiListAllTechnicians } from "../functions/api/technician";
import TechnicianSection from "../pageComponents/tech_manage/TechnicianSection";
import InviteTechnicianModal from "../components/Modal/InviteTechnicianModal";
import EditTechnicianModal from "../components/Modal/EditTechnicianModal";
import CarServiceContextProvider from "../contexts/CarServiceContext";
import Paper from "../components/atom/Paper";
import { apiAdminResetPassword } from "../functions/api/user";

const ResetPasswordsAdmin = () => {
  // Add these new states
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [response, setResponse] = useState<any>(null);

  // Add this new handler
  const handleResetPassword = async () => {
    try {
      const result = await apiAdminResetPassword({ email, password });
      setResponse(result);
    } catch (error) {
      setResponse(error);
    }
  };

  return (
    <CarServiceContextProvider>
      <TechnicianLayout>
        <Head>
          <title>Reset Passwords Admin - Wheel Easy</title>
        </Head>

        <div className="flex items-stretch h-[calc(100vh-76px)]">
          <div className="flex flex-col flex-1 items-stretch px-8 pt-8 overflow-auto">
            <div className="flex items-center justify-between mb-6">
              <div className="flex flex-col ">
                <Title24
                  style={{
                    fontSize: 28,
                    marginBottom: 8,
                  }}
                >
                  Reset Passwords
                </Title24>
                <p
                  className="text-sm"
                  style={{
                    color: Color.GRAY,
                  }}
                >
                  Type in an email that a customer has and manually set a new permanent password.
                </p>
              </div>
            </div>
            <Paper style={{ marginBottom: 8, padding: 24, width: "100%" }}>
              <div className="flex flex-col gap-4">
                <div>
                  <label className="block text-sm mb-2">Email</label>
                  <input
                    type="email"
                    className="w-full p-2 border rounded"
                    placeholder="Enter customer email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm mb-2">New Password</label>
                  <input
                    type="text"
                    className="w-full p-2 border rounded"
                    placeholder="Enter new password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
                <button
                  className="bg-blue-500 text-white px-4 py-2 rounded"
                  onClick={handleResetPassword}
                >
                  Reset Password
                </button>
                {response && (
                  <pre className="mt-4 p-4 bg-gray-100 rounded overflow-auto">
                    {JSON.stringify(response, null, 2)}
                  </pre>
                )}
              </div>
            </Paper>
          </div>
        </div>
      </TechnicianLayout>
    </CarServiceContextProvider>
  );
};

export default ResetPasswordsAdmin;
