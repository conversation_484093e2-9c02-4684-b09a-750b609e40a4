import axios from "axios";
import Head from "next/head";
import { useRouter } from "next/router";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../@redux";
import { setCarServiceExtensions } from "../@redux/modules/summary";
import Layout from "../components/Layout";
import StickyContainer from "../components/atom/StickyContainer";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { useAddresses } from "../hooks/useAddresses";
import useTimer from "../hooks/useTimer";
import CustomerInfo from "../pageComponents/information/CustomerInfo";
import PaymentBlock from "../pageComponents/information/PaymentBlock";
import Review from "../pageComponents/information/Review";
import { CarServiceExtension } from "../types/CarServiceExtension";
import { API_URL } from "../values";

const InformationPage = () => {
  const router = useRouter();
  const dispatch = useDispatch<any>();
  const timerHook = useTimer();
  const addressHook = useAddresses();
  const carServiceExtensions = useSelector(
    (state: RootState) => state.summary.carServiceExtensions
  );

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (timerHook.isTimerCalled && timerHook.nowSecond === 0) {
      alert("Time is up! Please start over again from the previous page.");
      router.back();
    }
  }, [timerHook.nowSecond, timerHook.isTimerCalled]);

  async function apiGetSession() {
    const headers = await getTokenContainedHeader();
    const sessionResult = (
      await axios.get(API_URL + "/sessions", {
        headers,
        params: {
          mode: 3,
        },
      })
    ).data as CarServiceExtension[];

    return sessionResult;
  }

  useEffect(() => {
    const fetch = async () => {
      try {
        setIsLoading(true);
        const result = await apiGetSession();
        dispatch(setCarServiceExtensions(result));
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    };
    fetch();
  }, []);

  if (isLoading) {
    return null;
  }

  return (
    <Layout>
      <Head>
        <title>Review Booking Info - Wheel Easy</title>
      </Head>
      <div className="self-stretch h-full bg-[#F8FAFB] min-w-[320px]">
        <div className="flex-col w-full flex-1 bg-[#F8FAFB] flex overflow-y-scroll h-[calc(100vh-68px)] pb-[92px]">
          <div className="flex flex-col mx-auto w-full xl:px-10">
            <div className="flex w-full justify-center">
              <div className="flex flex-col xl:mr-8 xl:mt-5 xl:w-[1088px] w-full">
                <div className="sticky -top-[0.5px] z-10 xl:hidden">
                  <PaymentBlock carServiceExtensions={carServiceExtensions} timerHook={timerHook} />
                </div>
                <Review carServiceExtensions={carServiceExtensions} />
                <CustomerInfo addressHook={addressHook} />
                <div className="mt-6 xl:mt-0">
                  <PaymentBlock
                    carServiceExtensions={carServiceExtensions}
                    timerHook={timerHook}
                    isBottomDividedInResponsive
                  />
                </div>
              </div>
              <div className="hidden xl:block">
                <StickyContainer>
                  <PaymentBlock carServiceExtensions={carServiceExtensions} timerHook={timerHook} />
                </StickyContainer>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default InformationPage;
