import BackgroundSmallButton from "../components/atom/Buttons/BackgroundSmallButton";
import GrayParagraph from "../components/atom/Texts/GrayParagraph";
import Title24 from "../components/atom/Texts/base/Title24";
import TechnicianLayout from "../components/organism/TechnicianLayout";

const EarningPage = () => {
  return (
    <TechnicianLayout>
      <div className="flex flex-col w-full items-center">
        <div className="flex flex-col pt-20">
          <Title24>Total Earnings</Title24>
          <div className="flex items-stretch mb-10">
            <div
              className="flex flex-col border rounded-md p-5 bg-white mr-5"
              style={{
                width: 343,
              }}
            >
              <div className="flex items-center justify-between mb-1">
                <GrayParagraph>Your next payment</GrayParagraph>
                <span>Jun 20, 2022</span>
              </div>
              <div className="flex items-end">
                <span className="text-4xl font-bold">$650.00</span>
                <span>CAD</span>
              </div>
            </div>
            <div className="flex flex-col justify-between border rounded-md p-5 bg-white">
              <GrayParagraph>Your total earnings</GrayParagraph>
              <div className="flex items-end">
                <span className="text-4xl font-bold">$1950.00</span>
                <span>CAD</span>
              </div>
            </div>
          </div>
          <Title24>Bank Account</Title24>
          <GrayParagraph
            style={{
              marginBottom: 4,
            }}
          >
            You haven{"'"}t connected your bank account yet.
          </GrayParagraph>
          <BackgroundSmallButton
            title="Connect bank account via Stripe"
            backgroundColor="DARK_BLUE"
            style={{
              alignSelf: "baseline",
              marginBottom: 40,
            }}
            onClick={() => {}}
          />
          <Title24>Invoices</Title24>
          <div className="flex items-center justify-between ">
            <div className="flex flex-col ">
              <span>Tire Swap</span>
              <span>$65.00</span>
            </div>
            <div className="flex flex-col items-end">
              <span>2022/06/02 at 5:24 PM</span>
              <div className="flex items-center">
                <span>Download Invoice</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </TechnicianLayout>
  );
};

export default EarningPage;
