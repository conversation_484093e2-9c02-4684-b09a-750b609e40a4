import mixpanel, { Dict, Query } from "mixpanel-browser";

const isProd = process.env.NODE_ENV === "production";

mixpanel.init(process.env.NEXT_PUBLIC_MIXPANEL_TOKEN!, {
  debug: !isProd,
});

export const Mixpanel = {
  identify: (id: string) => mixpanel.identify(id),
  alias: (id: string) => mixpanel.alias(id),
  track: (event: string, props?: Dict) => mixpanel.track(event, props),
  people: {
    set: (props: Dict) => mixpanel.people.set(props),
    set_once: (props: Dict) => mixpanel.people.set_once(props),
    increment: (props: Dict) => mixpanel.people.increment(props),
    append: (props: Dict) => mixpanel.people.append(props),
    union: (props: Dict) => mixpanel.people.union(props),
    track_charge: (amount: number, props?: Dict) => mixpanel.people.track_charge(amount, props),
    clear_charges: () => mixpanel.people.clear_charges(),
    delete_user: () => mixpanel.people.delete_user(),
  },
  register: (props: Dict) => mixpanel.register(props),
};
