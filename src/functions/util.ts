export function get12BasedTimeString(time: string) {
  const slicedTime = time.substring(11, 16);
  const hour = 10 * parseInt(slicedTime[0]) + parseInt(slicedTime[1]);

  if (hour >= 12) {
    return `${hour - 12}:${slicedTime.substring(3, 5)} PM`;
  } else {
    return `${slicedTime} AM`;
  }
}

export function getAddedTimeText(prev: string, diff: number) {
  const hourDiff = Math.floor(diff / 60);
  const minDiff = diff % 60;

  let prevHour = parseInt(prev.substring(0, 2));
  let prevMin = parseInt(prev.substring(3));

  prevHour += hourDiff;
  prevMin += minDiff;
  if (prevMin === 60) {
    prevMin = 0;
    prevHour += 1;
  }

  return `${prevHour <= 9 ? "0" + prevHour : prevHour}:${prevMin <= 9 ? "0" + prevMin : prevMin}`;
}

export function getOrdinalNumber(number: number): string {
  const ordinalMap: { [key: number]: string } = {
    1: "First",
    2: "Second",
    3: "Third",
    4: "Fourth",
    5: "Fifth",
    6: "Sixth",
    7: "Seventh",
    8: "Eighth",
    9: "Ninth",
    10: "Tenth",
    11: "Eleventh",
    12: "Twelfth",
    13: "Thirteenth",
    14: "Fourteenth",
    15: "Fifteenth",
    16: "Sixteenth",
    17: "Seventeenth",
    18: "Eighteenth",
    19: "Nineteenth",
    20: "Twentieth",
    30: "Thirtieth",
    40: "Fortieth",
    50: "Fiftieth",
    60: "Sixtieth",
    70: "Seventieth",
    80: "Eightieth",
    90: "Ninetieth",
  };

  if (number <= 20 || number % 10 === 0) {
    return ordinalMap[number] || number + "th";
  } else {
    const tensDigit = Math.floor(number / 10) * 10;
    const onesDigit = number % 10;
    const ordinal = ordinalMap[tensDigit] + "-" + ordinalMap[onesDigit];
    return ordinal;
  }
}
