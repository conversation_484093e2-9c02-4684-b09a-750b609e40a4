import axios from "axios";
import { TechnicianRequest } from "../../types";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListTechnicianRequests() {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.get(API_URL + "/technicians/requests", {
      headers,
    })
  ).data as TechnicianRequest[];

  return result;
}

export async function apiApproveTechnicianRequest(id: number) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(API_URL + "/technicians/requests/" + id, {
      headers,
    })
  ).data as {
    id: number;
    status: "APPROVED";
  };

  return result;
}

export async function apiRejectTechnicianRequest(id: number) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(API_URL + "/technicians/requests/" + id, {
      headers,
    })
  ).data as {
    id: number;
    status: "REJECTED";
  };

  return result;
}
