import axios from "axios";
import { Service } from "../../types";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListServices() {
  const serviceResult = (await axios.get(API_URL + "/services", {})).data as Service[];

  return serviceResult;
}

export async function apiListTechServices(technicianId: number) {
  const services = (await axios.get(API_URL + `/services/${technicianId}`, {})).data;

  return services;
}

export async function apiUpdateTechServices(technicianId: number, services: any[]) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.put(
      API_URL + `/services`,
      {
        technicianId,
        services,
      },
      {
        headers,
      },
    )
  ).data;

  return result;
}

export async function apiCreateService(
  name: string,
  price: number,
  description: string,
  image: string,
) {
  const headers = await getTokenContainedHeader();
  const creationResult = (
    await axios.post(
      API_URL + "/services",
      {
        name,
        price,
        description,
      },
      {
        headers,
      },
    )
  ).data as Service;
  return creationResult;
}
