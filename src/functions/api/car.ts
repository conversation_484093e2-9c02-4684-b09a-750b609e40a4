import axios from "axios";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiDeleteCar(id: number) {
  const headers = await getTokenContainedHeader();
  const deletedResult = (
    await axios.delete(API_URL + "/cars/" + id, {
      headers,
    })
  ).data as {
    id: number;
    deleted: true;
  };

  return deletedResult;
}
