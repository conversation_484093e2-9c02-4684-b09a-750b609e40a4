import axios from "axios";
import { Addon } from "../../types/Addon";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListAddons() {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.get(API_URL + "/addons", {
      headers,
    })
  ).data;

  return result as Addon[];
}

export async function apiCreateAddon(name: string, cost: number) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(
      API_URL + "/addons",
      {
        name,
        cost,
      },
      {
        headers,
      },
    )
  ).data;

  return result as Addon;
}

export async function apiUpdateAddon(id: number, name?: string, cost?: number) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.put(
      API_URL + "/addons/" + id,
      {
        name,
        cost,
      },
      {
        headers,
      },
    )
  ).data as {
    name?: string;
    cost?: number;
  };
  return result;
}

export async function apiDeleteAddon(id: number) {
  const headers = await getTokenContainedHeader();
  const deletedResult = (
    await axios.delete(API_URL + "/addons/" + id, {
      headers,
    })
  ).data as {
    id: number;
    deleted: true;
  };

  return deletedResult;
}
