import axios from "axios";
import { getTokenContainedHeader } from "../cognito/util";
import { API_URL } from "../../values";

export async function apiSendMessageToSlack(data: { channel: string; message: string }) {
  const headers = await getTokenContainedHeader(); // Use your method for headers
  try {
    const result = (
      await axios.post(API_URL + `/slack`, data, {
        headers,
      })
    ).data;
    console.log("Slack message result:", result);
    return result;
  } catch (error) {
    console.error("Error sending message to Slack:", error);
    throw error;
  }
}
