import axios from "axios";
import { Availability } from "../../@redux/modules/techBooking";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListAvailability(
  startDate: string | undefined,
  endDate: string | undefined,
) {
  // console.log("apiListAvailability")
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.get(API_URL + "/availability", {
      headers,
      params: {
        startDate,
        endDate,
      },
    })
  ).data;

  return result as Availability[];
}

export async function apiCreateAvailability(
  availabilities: {
    dayOfWeek: string;
    slots: {
      startTime: string;
      endTime: string;
    }[];
  }[],
) {
  const headers = await getTokenContainedHeader();
  const createdResult = (
    await axios.post(
      API_URL + "/availability",
      {
        availabilities,
      },
      {
        headers,
      },
    )
  ).data as {
    availabilityId: number;
    dayOfWeek: string;
    startTime: string;
    endTime: string;
  };
  return createdResult;
}
