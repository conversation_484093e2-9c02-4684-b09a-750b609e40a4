import axios from "axios";
import { ListTypeBooking, ListTypeBookingByPagination } from "../../types";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListBookings(
  page: number,
  perPage: number,
  sortBy: "APPOINTMENT_TIME" | "CREATED_AT",
  order: "ASC" | "DESC",
  viewType: "UPCOMING" | "PAST",
) {
  const headers = await getTokenContainedHeader();
  const bookingResult = (
    await axios.get(API_URL + "/bookings", {
      headers,
      params: {
        page,
        perPage,
        sortBy,
        order,
        viewType,
      },
    })
  ).data as ListTypeBookingByPagination[];

  return bookingResult;
}

export async function apiGetBookingsCount() {
  const headers = await getTokenContainedHeader();
  const bookingsCount = (
    await axios.get(API_URL + "/bookings/count", {
      headers,
    })
  ).data as number;

  return bookingsCount;
}
