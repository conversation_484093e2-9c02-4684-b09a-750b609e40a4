import axios from "axios";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiGetGoogleCalendarList() {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.get(API_URL + "/calendar/google-calendar-lists", {
      headers,
    })
  ).data;

  return result;
}

export async function apiSyncCalendar(calendarIds: string[]) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(
      API_URL + "/calendar/sync",
      {
        calendarIds,
      },
      {
        headers,
      },
    )
  ).data;

  return result;
}
