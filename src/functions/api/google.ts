import axios from "axios";
import { NextResponse } from "next/server";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiGetGooglePlacesAutocomplete(input: string) {
  try {
    const result = (
      await axios.get(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=amoeba&types=establishment&location=37.76999%2C-122.44696&radius=500&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`,
      )
    ).data;

    console.log(result);

    return result.json();
  } catch (error) {
    console.log(error);
  }
}

export async function apiGoogleRecaptcha(token: string) {
  console.log("apiGoogleRecaptcha getting headers");
  const headers = await getTokenContainedHeader();
  console.log("got headers", token);
  const result = (
    await axios.post(
      API_URL + `/captcha/submit`,
      {
        token,
      },
      {
        headers,
      },
    )
  ).data;
  console.log("result", result);
  return result;
}
