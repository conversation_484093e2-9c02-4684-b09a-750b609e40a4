import axios from "axios";
import { getTokenContainedHeader } from "../cognito/util";
import { API_URL } from "../../values";

export async function apiSendEmail(data: {
  email: string;
  subject: string;
  body: string;
  html: boolean;
}) {
  const headers = await getTokenContainedHeader(); // Use your method for headers
  try {
    const result = (
      await axios.post(API_URL + `/email`, data, {
        headers,
      })
    ).data;
    console.log("Email message result:", result);
    return result;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
}
