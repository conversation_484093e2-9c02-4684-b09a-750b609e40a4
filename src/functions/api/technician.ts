import axios from "axios";
import { TechnicianFreeBusy } from "../../hooks/useTechnicianSearch";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListTechnicians(
  categoryId: number,
  serviceIds: number[],
  startDateTime: string,
  endDateTime: string,
  zoneId: number | null,
  name?: string,
) {
  const headers = await getTokenContainedHeader();

  // const serviceIds = services.map((service) => service.id);

  const techResult = (
    await axios.get(API_URL + "/technicians", {
      headers,
      params: {
        startDateTime,
        endDateTime,
        name,
        categoryId,
        serviceIds,
        zoneId,
      },
    })
  ).data as TechnicianFreeBusy;

  return techResult;
}

export async function apiListAllTechnicians() {
  const headers = await getTokenContainedHeader();
  const techResult = (
    await axios.get(API_URL + "/technicians/all", {
      headers,
    })
  ).data as TechnicianFreeBusy;

  return techResult;
}

export async function apiFetchTechnicianPriority(technicianId: number) {
  const priority = (await axios.get(API_URL + `/technician/priority/${technicianId}`, {})).data;

  return priority;
}

export async function apiFetchTechnicianEfficiency(technicianId: number) {
  const efficiency = (await axios.get(API_URL + `/technician/efficiency/${technicianId}`, {})).data;

  return efficiency;
}

export async function apiUpdateTechPriority(technicianId: number, priority: number) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.put(
      API_URL + `/technician/priority`,
      {
        technicianId,
        priority,
      },
      {
        headers,
      },
    )
  ).data;

  return result;
}

export async function apiUpdateTechEfficiency(technicianId: number, isEfficient: boolean) {
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.put(
      API_URL + `/technician/efficiency`,
      {
        technicianId,
        isEfficient,
      },
      {
        headers,
      },
    )
  ).data;

  return result;
}

export async function apiCreateTechnician(sub: string, email: string) {
  const headers = await getTokenContainedHeader();
  const techResult = (
    await axios.post(
      API_URL + "/technician",
      {
        sub,
        email,
      },
      {
        headers,
      },
    )
  ).data as TechnicianFreeBusy;

  return techResult;
}
