import axios from "axios";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiListTechZones(technicianId: number) {
const zones = (await axios.get(API_URL + `/zones/${technicianId}`, {})).data;

return zones;
}

export async function apiUpdateTechZones(technicianId: number, zones: any[]) {
    const headers = await getTokenContainedHeader();
    const result = (
      await axios.put(
        API_URL + `/zones`,
        {
          technicianId,
          zones,
        },
        {
          headers,
        },
      )
    ).data;
  
    return result;
  }