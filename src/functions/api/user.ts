import axios from "axios";
import { Address } from "../../types";
import { User } from "../../types/User";
import { API_URL } from "../../values";
import { getTokenContainedHeader } from "../cognito/util";

export async function apiGetUser() {
  const headers = await getTokenContainedHeader();
  // console.log("headers", headers);
  const userResult = (
    await axios.get(API_URL + "/user", {
      headers,
    })
  ).data;

  return new User(
    userResult.id,
    userResult.firstName,
    userResult.lastName,
    userResult.email,
    userResult.phone,
    userResult.roleId,
    userResult.profilePictureUri,
    userResult.createdDateTime,
    userResult.biography,
    false,
  );
}

// Should fix. This function is used generally.
export async function apiUpdateUserAddress(data: {
  profilePictureUri?: string;
  address?: Address;
  firstName?: string;
  lastName?: string;
  phone?: string;
}) {
  const headers = await getTokenContainedHeader();
  const addressResult = (
    await axios.put(
      API_URL + "/user",
      {
        ...data,
      },
      {
        headers,
      },
    )
  ).data as {
    address: Address;
    profilePictureUri: string;
    firstName: string;
    lastName: string;
    phone: string;
  };
  return addressResult;
}

export async function apiUpdateUserBiography(data: { biography?: string }) {
  const headers = await getTokenContainedHeader();
  const biographyResult = (
    await axios.put(
      API_URL + "/user",
      {
        ...data,
      },
      {
        headers,
      },
    )
  ).data as {
    biography: string;
  };
  return biographyResult;
}

export async function apiAdminResetPassword(data: { email: string; password: string }) {
  console.log("apiAdminResetPassword", data);
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(
      API_URL + "/user/admin-reset-password",
      {
        ...data,
      },
      {
        headers,
      },
    )
  ).data as {
    result: string;
  };
  return result;
}

export async function apiAdminConfirmUser(data: { email: string }) {
  console.log("apiAdminConfirmUser", data);
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(
      API_URL + "/user/admin-confirm-user",
      {
        ...data,
      },
      {
        headers,
      },
    )
  ).data as {
    result: string;
  };
  return result;
}

export async function apiInviteTechnician(data: { email: string }) {
  console.log("apiInviteTechnician", data);
  const headers = await getTokenContainedHeader();
  const result = (
    await axios.post(
      API_URL + "/user/admin-invite-technician",
      {
        ...data,
      },
      {
        headers,
      },
    )
  ).data as any;
  return result;
}
