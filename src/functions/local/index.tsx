import { Car } from "../../types/Car";

export async function loadCarLocalStorage() {
  const savedCarString = localStorage.getItem("cars");
  if (savedCarString) {
    return JSON.parse(savedCarString) as Car[];
  } else {
    return null;
  }
}

export async function saveCarLocalStorage(cars: Car[]) {
  localStorage.setItem("cars", JSON.stringify(cars));
}

export function convertCentToDollar(value: number) {
  return (value / 100).toFixed(2);
}
