import { Auth } from "aws-amplify";

export async function getCurrentUser() {
  console.log("getCurrentUser");
  return await Auth.currentAuthenticatedUser();
}

export async function getUserToken(): Promise<string> {
  const user = await getCurrentUser();
  console.log("user", user);
  return user.getSignInUserSession()?.getAccessToken().getJwtToken() || "";
}

export async function getTokenContainedHeader() {
  try {
    const token = await getUserToken();
    return {
      Authorization: token,
    };
  } catch (error) {
    console.log("No authenticated user, skipping Authorization header.");
    return { Authorization: false };
  }
}
