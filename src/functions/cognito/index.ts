import { Auth } from "aws-amplify";
import <PERSON><PERSON> from "aws-sdk";
import { UserPoolId } from "./pool";

AWS.config.update({
  accessKeyId: process.env.NEXT_PUBLIC_IAM_ACCESS_KEY_ID,
  secretAccessKey: process.env.NEXT_PUBLIC_IAM_SECRET_ACCESS_KEY_ID,
  region: "ca-central-1",
});

// Initialize the Cognito Identity Service Provider
const cognito = new AWS.CognitoIdentityServiceProvider({
  region: "ca-central-1", // Ensure this matches your pool's region
});

export async function apiSignIn(username: string, password?: string) {
  return await Auth.signIn(username, password);

  // let authenticationDetails = new AuthenticationDetails({
  //   Username: username,
  //   Password: password,
  // });

  // let user = new CognitoUser({
  //   Username: username,
  //   Pool: userPool,
  // });

  // return await new Promise((resolve, reject) => {
  //   user.authenticateUser(authenticationDetails, {
  //     onSuccess: function (result) {
  //       resolve(result);
  //     },
  //     onFailure: function (err) {
  //       reject(err);
  //     },
  //     newPasswordRequired: function (userAttributes, requiredAttributes) {
  //       delete userAttributes.email;
  //       delete userAttributes.email_verified;
  //       resolve(userAttributes);
  //     },
  //   });
  // });
}

export async function apiDeleteUser() {
  try {
    await Auth.deleteUser(); // AWS Amplify call to delete the user from Cognito
  } catch (error) {
    console.error("Error deleting user", error);
  }
}

export async function apiSignOut() {
  await Auth.signOut();
  // const cognitoUser = await getCurrentUser();
  // if (cognitoUser) {
  //   cognitoUser.signOut();
  // } else {
  //   throw new Error("Not logged in");
  // }
}

export async function apiSendVerificationCode(username: string) {}

export async function apiSignUp(userParams: {
  fullName: string;
  username: string;
  password: string;
  phone: string;
}) {
  return await Auth.signUp({
    username: userParams.username,
    password: userParams.password,
    attributes: {
      name: userParams.fullName,
      "custom:phone": userParams.phone,
    },
    autoSignIn: {
      enabled: true,
    },
  });

  // let attributeList: CognitoUserAttribute[] = [
  //   new CognitoUserAttribute({
  //     Name: "name",
  //     Value: userParams.fullName,
  //   }),
  //   new CognitoUserAttribute({
  //     Name: "custom:phone",
  //     Value: userParams.phone,
  //   }),
  // ];

  // return await new Promise((resolve, reject) => {
  //   userPool.signUp(
  //     userParams.username,
  //     userParams.password,
  //     attributeList,
  //     [],
  //     function (err, result) {
  //       if (err) {
  //         alert(err.message || JSON.stringify(err));
  //         reject(err);
  //         return;
  //       }

  //       let cognitoUser = result?.user;
  //       resolve(cognitoUser);
  //     }
  //   );
  // });
}

export async function apiConfirmUser(username: string, confirmCode: string) {
  return await Auth.confirmSignUp(username, confirmCode);
}

export async function apiResendConfirmCode(username: string) {
  return await Auth.resendSignUp(username);

  // let cognitoUser = new CognitoUser({
  //   Username: username,
  //   Pool: userPool,
  // });

  // if (cognitoUser) {
  //   return await new Promise((resolve, reject) => {
  //     cognitoUser.resendConfirmationCode(function (err, result) {
  //       if (err) {
  //         reject(err);
  //       }
  //       resolve(result);
  //     });
  //   });
  // } else {
  //   throw new Error("No user found");
  // }
}

export async function apiForgotPassword(username: string) {
  return await Auth.forgotPassword(username);
  // let cognitoUser = new CognitoUser({
  //   Username: username,
  //   Pool: userPool,
  // });

  // return await new Promise((resolve, reject) => {
  //   cognitoUser.forgotPassword({
  //     onSuccess: function (result) {
  //       resolve(result);
  //     },
  //     onFailure: function (err) {
  //       reject(err);
  //     },
  //   });
  // });
}

export async function apiCompleteNewPassword(
  session: string,
  username: string,
  newPassword: string,
  tmpPwd: string,
) {
  try {
    console.log("username", username);
    console.log("newPassword", newPassword);
    console.log("tmpPwd", tmpPwd);

    // Sign in the user to get the user object
    const user = await Auth.signIn(username, tmpPwd);

    // Complete the new password challenge using the user object
    const completedUser = await Auth.completeNewPassword(user, newPassword);

    return completedUser;
  } catch (error) {
    console.error("Error completing new password challenge", error);
    throw error;
  }
}

export async function apiResetPassword(username: string, code: string, newPassword: string) {
  return await Auth.forgotPasswordSubmit(username, code, newPassword);
  // let cognitoUser = new CognitoUser({
  //   Username: username,
  //   Pool: userPool,
  // });

  // return await new Promise((resolve, reject) => {
  //   cognitoUser.confirmPassword(code, newPassword, {
  //     onSuccess: function (result) {
  //       resolve(result);
  //     },
  //     onFailure: function (err) {
  //       reject(err);
  //     },
  //   });
  // });
}

export async function apiChangePassword(
  username: string,
  oldPassword: string,
  newPassword: string,
) {
  const user = await Auth.currentAuthenticatedUser();
  return await Auth.changePassword(user, oldPassword, newPassword);
  // let authenticationDetails = new AuthenticationDetails({
  //   Username: username,
  //   Password: oldPassword,
  // });

  // let cognitoUser = new CognitoUser({
  //   Username: username,
  //   Pool: userPool,
  // });

  // return await new Promise((resolve, reject) => {
  //   cognitoUser.authenticateUser(authenticationDetails, {
  //     onSuccess: () => {},
  //     onFailure: (err) => {
  //       console.log(err);
  //     },
  //     newPasswordRequired: () => {
  //       cognitoUser.completeNewPasswordChallenge(
  //         newPassword,
  //         {},
  //         {
  //           onSuccess: function (result) {
  //             resolve(result);
  //           },
  //           onFailure: function (err) {
  //             console.log(err);
  //             reject(err);
  //           },
  //         }
  //       );
  //     },
  //   });
  // });
}

// export async function apiInviteUser(email: string) {
//   try {
//     const result = await Auth.signUp({
//       username: email,
//       password: "Qwerty1234!", // Generate a temporary random password
//       attributes: {
//         email: email,
//       },
//       autoSignIn: {
//         enabled: false,
//       },
//     });
//     console.log("User invited successfully", result);
//     return result;
//   } catch (error) {
//     console.error("Error inviting user", error);
//     throw error;
//   }
// }

async function updateUserAttributes(email: string) {
  try {
    console.log("Starting to update user attributes for:", email);

    const updateParams = {
      UserPoolId: UserPoolId,
      Username: email,
      UserAttributes: [
        {
          Name: "email_verified",
          Value: "true",
        },
      ],
    };

    console.log("Update parameters:", updateParams);

    const data = await cognito.adminUpdateUserAttributes(updateParams).promise();
    console.log("User attributes updated successfully", data);
    return data;
  } catch (error) {
    console.error("Error updating attributes", error);
    throw error;
  }
}

// export async function apiInviteUser(email: string) {
//   const params = {
//     UserPoolId: UserPoolId, // Your Cognito User Pool ID
//     Username: email,
//     UserAttributes: [
//       {
//         Name: "email",
//         Value: email,
//       },
//     ],
//     DesiredDeliveryMediums: ["EMAIL"], // Send the invitation via email
//     ForceAliasCreation: false,
//     // TemporaryPassword: "Qwerty1234!", // Optional: Generate a temporary password
//     // MessageAction: 'SUPPRESS', // Prevents Cognito from sending the default welcome email
//   };

//   try {
//     console.log("Inviting user with email:", email);
//     const result = await cognito.adminCreateUser(params).promise();
//     console.log("User invited successfully", result);

//     // After creation, explicitly set email_verified to true
//     // console.log('Invoking updateUserAttributes for email:', email);
//     //const data = await updateUserAttributes(email);
//     return result;
//   } catch (error) {
//     console.error("Error inviting user", error);
//     throw error;
//   }
// }

export async function setNewUserPassword(username: string, newPassword: string): Promise<void> {
  try {
    // Create the command to set the new password
    const command = {
      UserPoolId: UserPoolId,
      Username: username,
      Password: newPassword,
      Permanent: true, // This sets the password as permanent
    };

    // Send the command to Cognito
    await cognito.adminSetUserPassword(command).promise();
    console.log(`New password set successfully for user: ${username}`);
  } catch (error) {
    console.error("Error setting new password:", error);
  }
}
