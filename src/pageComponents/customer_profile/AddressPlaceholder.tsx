import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface AddressPlaceHolderProps {}

const AddressPlaceHolder = (props: AddressPlaceHolderProps) => {
  return (
    <div
      className="flex flex-col items-start justify-center border p-4 rounded-lg"
      style={{
        borderColor: Color.SLIVER_GRAY,
      }}
    >
      <div className="flex flex-row gap-2">
        <SizeableImage src={require("../../../public/address_empty.png")} size={72} />
        <span
          className="flex flex-col justify-center font-semibold text-sm"
          style={{
            color: Color.BLACK,
          }}
        >
          You have no available addresses.
        </span>
      </div>
    </div>
  );
};

export default AddressPlaceHolder;
