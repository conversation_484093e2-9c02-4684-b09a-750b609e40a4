import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import { EmailIcon, PencilIcon, PhoneIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import ProfilePlaceholder from "../../components/atom/ProfilePlaceholder";
import IndigoBlueText14 from "../../components/atom/Texts/IndigoBlueText14";
import IndigoBlueText20 from "../../components/atom/Texts/IndigoBlueText20";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";
import EditProfileModal from "../../components/Modal/EditProfileModal";
import { PhoneNumber } from "../../types/PhoneNumber";

interface InformationBlockProps {}

const InformationBlock = (props: InformationBlockProps) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const userFirstName = useSelector((state: RootState) => state.auth.user?.firstName);
  const userLastName = useSelector((state: RootState) => state.auth.user?.lastName);
  const userProfileUri = useSelector((state: RootState) => state.auth.user?.profilePictureUri);
  const userPhone = useSelector((state: RootState) => state.auth.user?.phone);
  const [isInformationModalOn, setIsInformationModalOn] = useState<boolean>(false);

  return (
    <Paper borders="rounded-t-lg">
      <EditProfileModal
        isOn={isInformationModalOn}
        onOff={() => setIsInformationModalOn(false)}
        profilePictureUri={userProfileUri || ""}
        firstName={userFirstName || ""}
        lastName={userLastName || ""}
        phone={userPhone || ""}
      />
      <ProfileBlockHeader
        title="Profile Information"
        onClick={() => {
          if (user) {
            setIsInformationModalOn(true);
          }
        }}
        buttonIcon={<PencilIcon />}
        buttonText="Edit Profile"
      />
      <div className="border-t-[0.5px] border-[#E6E6E8]" />
      <div className="flex flex-col md:flex-row items-start md:items-center px-4 py-6 md:p-6">
        <div className="mb-6 md:mb-0">
          {user?.profilePictureUri ? (
            <img
              src={user.profilePictureUri}
              className="rounded-lg border mr-6 object-cover"
              style={{
                width: 116,
                height: 116,
              }}
            />
          ) : (
            <ProfilePlaceholder size={116} style={{ marginRight: 24 }} />
          )}
        </div>
        <div className="flex flex-col flex-1">
          <div className="flex flex-col md:flex-row items-start md:items-end justify-between md:mb-3">
            <div className="flex flex-col gap-3">
              <div className="flex items-center font-medium">
                <IndigoBlueText20 style={{ fontWeight: 600, lineHeight: "27px" }}>
                  {user ? userFirstName + " " + userLastName : ""}
                </IndigoBlueText20>
              </div>
              <div className="flex flex-col md:flex-row gap-6 md:gap-3 font-light md:mr-4">
                <div className="flex flex-row gap-2">
                  <EmailIcon />
                  <IndigoBlueText14 style={{ lineHeight: "18.9px" }}>
                    {user?.email}
                  </IndigoBlueText14>
                </div>
                <div className="flex flex-row gap-2">
                  <PhoneIcon />
                  <IndigoBlueText14 style={{ lineHeight: "18.9px" }}>
                    {userPhone
                      ? PhoneNumber.getPhoneNumberFromAPI(userPhone).getFormattedFullPhoneNumber()
                      : "N/A"}
                  </IndigoBlueText14>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Paper>
  );
};

export default InformationBlock;
