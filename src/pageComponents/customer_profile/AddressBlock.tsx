import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import AddressModalGroup from "../../components/AddressModalGroup";
import { PlusIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import IndigoBlueText14 from "../../components/atom/Texts/IndigoBlueText14";
import IndigoBlueText16 from "../../components/atom/Texts/IndigoBlueText16";
import IndigoBlueTitle18 from "../../components/atom/Texts/IndigoBlueTitle18";
import { getOrdinalNumber } from "../../functions/util";
import { useAddresses } from "../../hooks/useAddresses";
import { Color } from "../../types/Color";
import AddressItemWithoutZip from "./AddressItemWithoutZip";
import AddressItemWithoutZipMobile from "./AddressItemWithoutZipMobile";
import AddressPlaceHolder from "./AddressPlaceholder";
import AddressPlaceholderMobile from "./AddressPlaceholderMobile";
import Spinner from "../../components/atom/Spinner";

const AddressBlock = () => {
  const router = useRouter();
  const user = useSelector((state: RootState) => state.auth.user);
  const addressHook = useAddresses();

  function isAddressItemSelected(index: number) {
    if (addressHook.selectedAddressIndex === index) {
      return true;
    } else return false;
  }

  return (
    <>
      <AddressModalGroup user={user} addressHook={addressHook} />
      <Paper borders="rounded-b-lg border-t-0">
        <div className="flex flex-col px-4 py-6 md:p-6 gap-6">
          <div className="flex items-center justify-between ">
            <div className="flex flex-col gap-2">
              <IndigoBlueTitle18>Addresses</IndigoBlueTitle18>
              <div className="hidden md:block">
                <IndigoBlueText14 style={{ color: Color.INDIGO_BLUE_06 }}>
                  Technician will visit this address.
                </IndigoBlueText14>
              </div>
            </div>
            <div className="hidden md:block">
              <button
                className="flex items-center space-x-2 bg-white rounded-lg px-5 py-[13.5px] hover:bg-[rgba(0,0,0,0.01)]"
                style={{
                  borderColor: Color.MISCHKA,
                  borderWidth: 1,
                  boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0)",
                }}
                onClick={addressHook.openAddModal}
              >
                <PlusIcon />
                <IndigoBlueText14 style={{ fontWeight: 600, lineHeight: "21px" }}>
                  Add New Address
                </IndigoBlueText14>
              </button>
            </div>
          </div>
          {/* web */}
          <div className="hidden md:block">
            <div className="hidden relative flex-col gap-4 md:flex">
              {addressHook.isFetchLoading ? (
                <div className="w-full h-full flex flex-col justify-center items-center bg-white">
                  <div className="flex flex-col items-center">
                    <Spinner />
                  </div>
                </div>
              ) : (
                <>
                  {addressHook.addresses.length > 0 ? (
                    <>
                      {addressHook.addresses?.map((address, index) => (
                        <AddressItemWithoutZip
                          title={getOrdinalNumber(index + 1)}
                          name={address.customerName || ""}
                          address={address}
                          phone={address.customerPhone}
                          isSelected={isAddressItemSelected(index)}
                          onClickChangeAddress={() => addressHook.openEditModal(index)}
                          onClickRemoveAddress={() => addressHook.openDeleteModal(index)}
                          onClickUseAddress={() => addressHook.select(index)}
                        />
                      ))}
                    </>
                  ) : (
                    <AddressPlaceHolder />
                  )}
                </>
              )}
            </div>
            <div className="border-t -mx-6 mt-6 mb-6" />
            {user?.isSSO ? null : (
              <div className="flex justify-between">
                <IndigoBlueTitle18 style={{ flex: 0.75 }}>Password</IndigoBlueTitle18>
                <div className="flex flex-col flex-1">
                  <IndigoBlueText16>************</IndigoBlueText16>
                  <div className="flex flex-row items-stretch">
                    <span
                      onClick={() => {
                        router.push("/change_password");
                      }}
                      className={`text-xs underline cursor-pointer`}
                      style={{
                        color: Color.ACCENT,
                      }}
                    >
                      Change password
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Mobile */}
          <div className="block md:hidden">
            <div className="flex relative flex-col gap-4">
              {addressHook.isFetchLoading ? (
                <div className="w-full h-full flex flex-col justify-center items-center bg-white">
                  <div className="flex flex-col items-center">
                    <Spinner />
                  </div>
                </div>
              ) : (
                <>
                  {addressHook.addresses.length > 0 ? (
                    <>
                      {addressHook.addresses?.map((address, index) => (
                        <AddressItemWithoutZipMobile
                          title={getOrdinalNumber(index + 1)}
                          name={address.customerName || ""}
                          address={address}
                          phone={address.customerPhone}
                          isSelected={isAddressItemSelected(index)}
                          onClickChangeAddress={() => addressHook.openEditModal(index)}
                          onClickRemoveAddress={() => addressHook.openDeleteModal(index)}
                          onClickUseAddress={() => addressHook.select(index)}
                        />
                      ))}
                    </>
                  ) : (
                    <AddressPlaceholderMobile />
                  )}
                </>
              )}

              <button
                className="flex items-center justify-center space-x-2 bg-white rounded-lg px-5 py-[13.5px] hover:bg-[rgba(0,0,0,0.01)]"
                style={{
                  borderColor: Color.MISCHKA,
                  borderWidth: 1,
                  boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0)",
                }}
                onClick={addressHook.openAddModal}
              >
                <PlusIcon />
                <IndigoBlueText14 style={{ fontWeight: 600, lineHeight: "21px" }}>
                  Add New Address
                </IndigoBlueText14>
              </button>
            </div>
            <div className="border-t -mx-4 mt-6 mb-6" />
            {user?.isSSO ? null : (
              <div className="flex flex-col gap-4">
                <IndigoBlueTitle18>Password</IndigoBlueTitle18>
                <div className="flex flex-col gap-1">
                  <IndigoBlueText16>************</IndigoBlueText16>
                  <div className="flex flex-row items-stretch">
                    <span
                      onClick={() => {
                        router.push("/change_password");
                      }}
                      className={`text-xs underline cursor-pointer`}
                      style={{
                        color: Color.ACCENT,
                      }}
                    >
                      Change password
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Paper>
    </>
  );
};

export default AddressBlock;
