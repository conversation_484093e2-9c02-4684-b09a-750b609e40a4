import { InfoIcon } from "../../components/atom/Icons";
import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface DeleteAccountBlockProps {
  onClick: () => void;
}
const DeleteAccountBlock = (props: DeleteAccountBlockProps) => {
  return (
    <div>
      <div className="px-4 py-[10px] md:px-6">
        <div
          className="flex flex-row gap-5 p-3 font-light rounded-lg border-[0.5px] "
          style={{
            backgroundColor: Color.SALMON_005,
            borderColor: Color.SALMON,
          }}
        >
          <div className="flex flex-col justify-center md:justify-start">
            <InfoIcon />
          </div>
          <p
            className="font-normal grow text-sm leading-5 md:text-base md:leading-6"
            style={{
              color: Color.COD_GRAY,
            }}
          >
            Caution : Think twice before you delete your account because this will permanently
            delete account data. There’s no going back.
          </p>
        </div>
      </div>
      <div className="px-4 pt-4 pb-6 md:px-6">
        <button
          className="flex flex-row gap-2 justify-center rounded-lg border w-full md:w-44 py-[13.5px] font-medium"
          onClick={props.onClick}
          style={{
            backgroundColor: Color.WHITE,
            color: Color.CORAL_MID_RED,
            borderColor: Color.CORAL_MID_RED,
          }}
          disabled={false}
        >
          <SizeableImage src={require("../../../public/trash_can_red_warning.png")} size={20} />
          <span className="flex items-center gap-1 justify-center font-semibold text-sm leading-5">
            Delete Account
          </span>
        </button>
      </div>
    </div>
  );
};

export default DeleteAccountBlock;
