import React from "react";
import { GarbageIcon, PencilIcon } from "../../components/atom/Icons";
import { Car } from "../../types/Car";
import { Color } from "../../types/Color";
import NoCarImage from "../index/NoCarImage";

interface VehicleItemProps {
  index?: number;
  car: Car;
  onClickEdit?: () => void;
  onClickDelete: () => void;
  style?: React.CSSProperties;
  isMobile?: boolean;
}

const VehicleItem = (props: VehicleItemProps) => {
  return (
    <div
      className={`flex items-center justify-start flex-1 border-b-[rgba(0,0,0,0.2)]`}
      style={props.style}
    >
      <div
        className="relative"
        style={{
          marginRight: props.isMobile ? 17 : props.index !== undefined ? 32 : 16,
        }}
      >
        {props.car.pictureUri ? (
          <div className="w-[76px] h-[76px] flex items-center justify-center overflow-hidden">
            <img
              className="object-cover object-center w-[76px] h-[76px] rounded-lg"
              src={props.car.pictureUri}
            />
          </div>
        ) : (
          <NoCarImage size={88} style={{ margin: -6 }} />
        )}
        {props.index !== undefined ? (
          <div
            className="absolute flex top-[-12px] right-[-12px] items-center justify-center border rounded-xl"
            style={{
              backgroundColor: Color.BACKGROUND_ACCENT,
              color: Color.ACCENT,
              borderColor: Color.ACCENT,
              fontWeight: "600",
              width: 32,
              height: 32,
            }}
          >
            {props.index + 1}
          </div>
        ) : null}
      </div>
      <div className="flex flex-row w-full justify-between">
        <div className="flex flex-col">
          <span
            style={{
              fontWeight: 400,
              fontSize: 14,
              lineHeight: 1,
              marginBottom: 9,
              color: Color.INDIGO_BLUE_06,
            }}
          >
            {props.car.make}
          </span>
          <span
            className="font-semibold"
            style={{
              fontSize: 18,
              lineHeight: 1,
              color: Color.INDIGO_BLUE,
            }}
          >
            {props.car.year} {props.car.model} :{" "}
            <span className="font-medium">({props.car.color})</span>
          </span>
        </div>
        <div className="flex items-center ">
          {props.onClickEdit ? (
            <button className="mr-3 bg-[#EBF2FF] py-3 px-4 rounded-sm" onClick={props.onClickEdit}>
              <PencilIcon />
            </button>
          ) : null}
          <button
            className="bg-[#FEE9E9] py-3 px-4 rounded-sm"
            onClick={props.onClickDelete}
            id="car-delete-button"
          >
            <GarbageIcon />
          </button>
        </div>
      </div>
    </div>
  );
};

export default VehicleItem;
