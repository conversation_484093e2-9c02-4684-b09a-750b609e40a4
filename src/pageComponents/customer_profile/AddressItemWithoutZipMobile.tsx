import Image from "next/image";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import SizeableImage from "../../components/atom/SizeableImage";
import IndigoBlueText14 from "../../components/atom/Texts/IndigoBlueText14";
import { NewAddress } from "../../services/AddressService";
import { Color } from "../../types/Color";
import MarkedSelection from "../booking_address/MarkedSelection";
import { Address } from "../../types/Address";

interface AddressItemWithoutZipMobileProps {
  isSelected: boolean;
  title: string;
  name: string;
  address: NewAddress;
  phone: string;
  onClickChangeAddress: () => void;
  onClickRemoveAddress: () => void;
  onClickUseAddress: () => void;
}

const AddressItemWithoutZipMobile = (props: AddressItemWithoutZipMobileProps) => {
  return (
    <div
      className="rounded-lg p-4"
      style={{
        border: "1px solid #E6E6E8",
        backgroundColor: props.isSelected ? Color.SOAP_STONE : Color.WHITE,
      }}
    >
      <div className="flex flex-col gap-6">
        <div className="flex flex-row justify-between">
          <div className="flex">
            <div className="flex flex-col justify-center min-w-[97px]">
              <span
                className="mr-3"
                style={{
                  color: Color.INDIGO_BLUE_08,
                  fontWeight: 600,
                  fontSize: 14,
                  lineHeight: "135%",
                }}
              >
                {Address.getPresentedTitle(props.title)}
              </span>
            </div>
          </div>
          <div className="flex flex-row gap-3">
            <button onClick={props.onClickChangeAddress}>
              <SizeableImage src={require("../../../public/pencil_indigo_blue.png")} size={18} />
            </button>
            <div className="flex items-center justify-center">
              <div className="flex flex-col items-center justify-center w-[1px] h-[18px]">
                <Image src={require("../../../public/line_vertical_Indigo_blue.png")} />
              </div>
            </div>
            <button onClick={props.onClickRemoveAddress}>
              <SizeableImage
                src={require("../../../public/trash_can_alizarin_crimson.png")}
                size={18}
              />
            </button>
          </div>
        </div>
        <div className="flex flex-row justify-between">
          <div className="flex flex-col items-start gap-3">
            <span
              style={{
                color: Color.BLACK_PEARL,
                fontWeight: 600,
                fontSize: 16,
                lineHeight: "135%",
              }}
            >
              {props.name}
            </span>
            <IndigoBlueText14 style={{ lineHeight: "135%" }}>{props.phone}</IndigoBlueText14>
            <div className="flex flex-col space-y-2 mt-2">
              <IndigoBlueText14>{`${props.address.street1}`}</IndigoBlueText14>
              {props.address.street2 ? (
                <IndigoBlueText14>{props.address.street2}</IndigoBlueText14>
              ) : null}
              <IndigoBlueText14>{`${props.address.city}, ${props.address.province}, ${props.address.country}`}</IndigoBlueText14>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressItemWithoutZipMobile;
