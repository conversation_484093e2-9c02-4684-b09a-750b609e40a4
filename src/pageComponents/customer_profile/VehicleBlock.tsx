import { useEffect, useState } from "react";
import { Car } from "../../types/Car";
import VehicleItem from "../../pageComponents/customer_profile/VehicleItem";
import VehicleDeleteModal from "../../components/organism/VehicleDeleteModal";
import VehicleAddonEditModal from "../../components/organism/VehicleAddonEditModal";
import { PlusIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";
import { VehicleService } from "../../services/VehicleService";
import { Color } from "../../types/Color";
import VehicleItemMobile from "./VehicleItemMobile";
import Spinner from "../../components/atom/Spinner";

const VehicleBlock = () => {
  const [vehicles, setVehicles] = useState<Car[]>();
  const [isAddVehicleModalOn, setIsAddVehicleModalOn] = useState<boolean>(false);
  const [isDeleteVehicleModalOn, setIsDeleteVehicleModalOn] = useState<boolean>(false);
  const [isEditVehicleModalOn, setIsEditVehicleModalOn] = useState<boolean>(false);
  const [selectedCar, setSelectedCar] = useState<Car>();

  const fetchVehicles = async () => {
    setVehicles(await VehicleService.getAll());
  };

  useEffect(() => {
    fetchVehicles();
  }, []);

  return (
    <Paper>
      <VehicleAddonEditModal
        isOn={isAddVehicleModalOn || isEditVehicleModalOn}
        isEdit={isEditVehicleModalOn}
        car={selectedCar}
        onOff={() => {
          setIsAddVehicleModalOn(false);
          setIsEditVehicleModalOn(false);
        }}
        onCompletion={() => {
          fetchVehicles();
          setIsAddVehicleModalOn(false);
          setIsEditVehicleModalOn(false);
        }}
      />
      <VehicleDeleteModal
        isOn={isDeleteVehicleModalOn}
        onOff={() => setIsDeleteVehicleModalOn(false)}
        car={selectedCar}
        title="Delete Vehicle"
        description="Are you sure you want to remove this vehicle from the list? This action cannot be undone."
        iconSrc={require("../../../public/trash_can_red_icon.png")}
        iconSize={52}
        onCompletion={() => fetchVehicles()}
      />
      <ProfileBlockHeader
        title="Vehicles"
        onClick={() => setIsAddVehicleModalOn(true)}
        buttonIcon={<PlusIcon />}
        buttonText="Add New Vehicle"
      />
      <div
        className="border-t-[0.5px]"
        style={{
          borderColor: Color.SLIVER_GRAY,
        }}
      />
      {/* Web */}
      <div className="hidden flex-col divide-y md:flex">
        {vehicles !== undefined ? (
          <>
            {vehicles?.map((item, index) => (
              <div className="p-6">
                <VehicleItem
                  key={index}
                  car={item}
                  onClickEdit={() => {
                    setSelectedCar(item);
                    setIsEditVehicleModalOn(true);
                  }}
                  onClickDelete={() => {
                    setSelectedCar(item);
                    setIsDeleteVehicleModalOn(true);
                  }}
                />
              </div>
            ))}
          </>
        ) : (
          <div className="w-full h-full flex flex-col justify-center items-center bg-white">
            <div className="flex flex-col items-center">
              <Spinner />
            </div>
          </div>
        )}
      </div>
      {/* Mobile */}
      <div className="flex flex-col divide-y md:hidden">
        {vehicles !== undefined ? (
          <>
            {vehicles?.map((item, index) => (
              <div className="px-4 py-6">
                <VehicleItemMobile
                  key={index}
                  car={item}
                  onClickEdit={() => {
                    setSelectedCar(item);
                    setIsEditVehicleModalOn(true);
                  }}
                  onClickDelete={() => {
                    setSelectedCar(item);
                    setIsDeleteVehicleModalOn(true);
                  }}
                />
              </div>
            ))}
          </>
        ) : (
          <div className="w-full h-full flex flex-col justify-center items-center bg-white">
            <div className="flex flex-col items-center">
              <Spinner />
            </div>
          </div>
        )}
      </div>
    </Paper>
  );
};

export default VehicleBlock;
