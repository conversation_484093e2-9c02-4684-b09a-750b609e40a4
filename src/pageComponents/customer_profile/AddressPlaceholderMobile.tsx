import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface AddressPlaceholderMobileProps {}

const AddressPlaceholderMobile = (props: AddressPlaceholderMobileProps) => {
  return (
    <div
      className="flex flex-col items-center justify-center border p-4 rounded-lg"
      style={{
        borderColor: Color.SLIVER_GRAY,
      }}
    >
      <div className="flex flex-col gap-2">
        <div className="flex flex-row justify-center">
          <SizeableImage src={require("../../../public/address_empty.png")} size={72} />
        </div>
        <span
          className="flex flex-col justify-center font-semibold text-sm text-center"
          style={{
            color: Color.BLACK,
          }}
        >
          You have no available addresses. Please add a service location to continue to the next
          page.
        </span>
      </div>
    </div>
  );
};

export default AddressPlaceholderMobile;
