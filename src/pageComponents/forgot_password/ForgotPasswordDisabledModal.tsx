import { useRouter } from "next/router";
import ResizingModal from "../../components/Modal/ResizingModal";
import CloseButton from "../../components/atom/Buttons/CloseButton";
import GoogleAuthButton from "../../components/atom/Buttons/GoogleAuthButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import WarningIcon from "../../components/atom/Icons/WarningIcon";
import useGoogleSignIn from "../../hooks/useGoogleSignIn";
import { Color } from "../../types/Color";
import { useState } from "react";

interface ForgotPasswordDisabledModalProps {
  isOn: boolean;
  onOff: () => void;
  onClickButton: () => void;
  isLoading?: boolean;
}

const ForgotPasswordDisabledModal = (props: ForgotPasswordDisabledModalProps) => {
  const router = useRouter();
  const { isGoogleLoading, handleOnClickSignInWithGoogle } = useGoogleSignIn(router);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  function handleCloseModal() {
    props.onOff();
  }

  return (
    <ResizingModal
      isOn={props.isOn}
      onOff={handleCloseModal}
      modalSize={{
        mobile: {
          width: 400,
          height: 312,
        },
        web: {
          width: 400,
          height: 312,
        },
      }}
    >
      <div className="relative flex flex-col bg-white rounded-lg h-full w-full p-6 pr-2 overflow-y-scroll">
        <div className="absolute top-5 right-4">
          <CloseButton onClick={handleCloseModal} />
        </div>
        <div className="flex flex-col flex-1 h-full items-stretch">
          <WarningIcon />
          <div className="flex flex-col justify-between h-full">
            <div className="flex flex-col pt-5 gap-5">
              <span
                className=""
                style={{
                  fontWeight: 500,
                  fontSize: 18,
                  lineHeight: "156%",
                  color: Color.EBONY,
                }}
              >
                New Password Requested
              </span>
              <span
                className="flex flex-col justify-between gap-2.5"
                style={{
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "20px",
                  color: Color.PALE_SKY,
                }}
              >
                We've requested a new password for your account. A member of our team will be in
                touch with you shortly!
              </span>
            </div>
            {/* <GoogleAuthButton
              title="Sign up with Google"
              isFilled={false}
              onClick={handleOnClickSignInWithGoogle}
              disabled={isGoogleLoading || isLoading}
              isLoading={isGoogleLoading}
            /> */}
          </div>
        </div>
      </div>
    </ResizingModal>
  );
};

export default ForgotPasswordDisabledModal;
