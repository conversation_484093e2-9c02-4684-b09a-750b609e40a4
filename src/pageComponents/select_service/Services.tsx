import Image from "next/image";
import { useContext } from "react";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import WhiteButtonWithIcon from "../../components/atom/Buttons/WhiteButtonWithIcon";
import NoPaddingPaper from "../../components/atom/NoPaddingPaper";
import Spinner from "../../components/atom/Spinner";
import StepHeaderIndigoBlue from "../../components/molecule/StepHeaderIndigoBlue";
import MobileOrderSummary from "../../components/organism/MobileOrderSummary";
import OrderSummary from "../../components/organism/OrderSummary";
import { carServiceContext } from "../../contexts/CarServiceContext";
import { useOrderSummarySticky } from "../../hooks/useOrderSummarySticky";
import { Color } from "../../types/Color";
import ListCarBlock from "../index/ListCarBlock";

const Services = () => {
  const context = useContext(carServiceContext);
  const carServiceHook = context!.carServiceHook;
  const continueButtonHook = context!.continueButtonHook;
  const orderSummaryHook = context!.orderSummaryHook;
  const orderSummaryStickyHook = useOrderSummarySticky(
    context!.scrollableElementRef,
    orderSummaryHook.dynamicHeightRef
  );

  return (
    <div className="flex md:grow flex-row md:shrink-0 justify-center md:justify-start md:w-full md:pt-10 md:h-full">
      <div className="flex flex-col w-full h-full">
        {/* Web */}
        <div className="hidden md:flex flex-row justify-between h-full">
          <div className="flex flex-col items-stretch w-full mr-6">
            <div className="pb-10">
              <NoPaddingPaper borders="rounded-xl border shadow-[0_5px_15px_rgba(0,0,0,0.03)]">
                <div className="flex flex-col">
                  <div className="flex items-center justify-between p-6 pb-4">
                    <div className="w-1/2">
                      <StepHeaderIndigoBlue title="Select Cars" description="" />
                    </div>
                    <WhiteButtonWithIcon
                      title="Add New Car"
                      iconSrc={require("../../../public/vector_indigo_blue.png")}
                      iconSize={20}
                      style={{
                        height: 40,
                      }}
                      onClick={() => {
                        if (context) {
                          context.setIsAddCarModalOn(true);
                          context.fetchCar();
                        }
                      }}
                    />
                  </div>
                  {carServiceHook.isLoading ? (
                    <div className="w-full flex items-center justify-center h-40">
                      <Spinner />
                    </div>
                  ) : carServiceHook.carServices.length > 0 ? (
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 grid-flow-row gap-4 px-6 pb-6">
                      {carServiceHook.carServices.map((carService, index) => (
                        <>
                          <ListCarBlock
                            isSeeMoreOn={context?.seeMoreServiceIndexArr[index] !== -1}
                            carService={carService}
                            onClickEditService={() => {
                              if (context) {
                                context.handleOnClickEditService(
                                  carService.car,
                                  carService.services
                                );
                                context.setIsSelectServiceUpdateModalOn(true);
                              }
                            }}
                            onClickRemoveCar={() => {
                              if (context) {
                                context.setRemovingCarServiceIndex(index);
                              }
                            }}
                            onClickSeeMoreService={() => {
                              if (context) {
                                let tmpArr: number[] = [...context.seeMoreServiceIndexArr];
                                tmpArr[index] = index;
                                context.setSeeMoreServiceIndexArr([...tmpArr]);
                              }
                            }}
                            onClickFolded={() => {
                              if (context) {
                                let tmpArr: number[] = [...context.seeMoreServiceIndexArr];
                                tmpArr[index] = -1;
                                context.setSeeMoreServiceIndexArr([...tmpArr]);
                              }
                            }}
                          />
                        </>
                      ))}
                    </div>
                  ) : (
                    <SelectCarFirstUi />
                  )}
                  <div
                    className="flex flex-row justify-end p-6"
                    style={{
                      borderTop: "0.5px solid #E6E6E8",
                    }}
                  >
                    <BackgroundSmallButton
                      disabled={!continueButtonHook.isContinuable}
                      backgroundColor="BLACK_PEARL"
                      title="Continue"
                      onClick={continueButtonHook.handleOnClickContinue}
                      style={{
                        borderRadius: 8,
                        paddingTop: 10,
                        paddingBottom: 10,
                        paddingLeft: 40,
                        paddingRight: 40,
                      }}
                      isLoading={continueButtonHook.isLoading}
                    />
                  </div>
                </div>
              </NoPaddingPaper>
            </div>
          </div>
          <div
            className=""
            style={{
              position: orderSummaryStickyHook.getPosition(),
              top: orderSummaryStickyHook.getTop(),
              right: orderSummaryStickyHook.getRight(),
            }}
          >
            <OrderSummary
              orderSummaryHook={orderSummaryHook}
              serviceCars={orderSummaryHook.serviceCars}
              isFetchLoading={carServiceHook.isLoading}
            />
          </div>
          <div
            className="hidden min-w-[280px] md:block"
            style={{
              display: orderSummaryStickyHook.getDisplay(),
            }}
          />
        </div>

        {/* Mobile */}
        <div className="flex flex-col w-full h-full justify-between md:hidden">
          <div className="flex flex-col h-full pb-14">
            <NoPaddingPaper
              borders="rounded-none shadow-none border-0 border-b-[0.5px] border-t-[0.5px]"
              style={{
                borderColor: Color.SLIVER_GRAY,
              }}
            >
              <div className="flex flex-col">
                <div className="flex p-4 gap-6">
                  <StepHeaderIndigoBlue title="Select Cars and Services" description="" />
                </div>
                {carServiceHook.isLoading ? (
                  <div className="w-full flex items-center justify-center h-40">
                    <Spinner />
                  </div>
                ) : carServiceHook.carServices.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 grid-flow-row gap-4 px-4">
                    {carServiceHook.carServices.map((carService, index) => (
                      <div>
                        <ListCarBlock
                          isSeeMoreOn={context?.seeMoreServiceIndexArr[index] !== -1}
                          carService={carService}
                          onClickEditService={() => {
                            if (context) {
                              context.handleOnClickEditService(carService.car, carService.services);
                              context.setIsSelectServiceUpdateModalOn(true);
                            }
                          }}
                          onClickRemoveCar={() => {
                            if (context) {
                              context.setRemovingCarServiceIndex(index);
                            }
                          }}
                          onClickSeeMoreService={() => {
                            if (context) {
                              let tmpArr: number[] = [...context.seeMoreServiceIndexArr];
                              tmpArr[index] = index;
                              context.setSeeMoreServiceIndexArr([...tmpArr]);
                            }
                          }}
                          onClickFolded={() => {
                            if (context) {
                              let tmpArr: number[] = [...context.seeMoreServiceIndexArr];
                              tmpArr[index] = -1;
                              context.setSeeMoreServiceIndexArr([...tmpArr]);
                            }
                          }}
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <SelectCarFirstUi isMobile />
                )}

                <div className="flex p-4">
                  <WhiteButtonWithIcon
                    title="Add New Car"
                    iconSrc={require("../../../public/vector_indigo_blue.png")}
                    iconSize={20}
                    style={{
                      width: "100%",
                      height: 48,
                      justifyContent: "center",
                    }}
                    onClick={() => {
                      if (context) {
                        context.setIsAddCarModalOn(true);
                        context.fetchCar();
                      }
                    }}
                  />
                </div>
              </div>
            </NoPaddingPaper>
          </div>
          <MobileOrderSummary
            orderSummaryHook={orderSummaryHook}
            serviceCars={orderSummaryHook.serviceCars}
            isFetchLoading={carServiceHook.isLoading}
            disabled={!continueButtonHook.isContinuable}
            onClick={continueButtonHook.handleOnClickContinue}
            isLoading={continueButtonHook.isLoading}
            buttonTitle="Continue"
          />
        </div>
      </div>
    </div>
  );
};

const SelectCarFirstUi = (props: { isMobile?: boolean }) => (
  <>
    {props.isMobile ? (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="flex justify-center items-center opacity-90 mb-1" style={{}}>
          <Image src={require("../../../public/no_car_selected.png")} width={120} height={80} />
        </div>
        <h1
          className="mb-2 text-center"
          style={{ fontSize: 18, fontWeight: 700, lineHeight: 1, color: Color.BLACK_PEARL }}
        >
          You haven’t selected a vehicle yet
        </h1>
        <p
          className="text-center mb-8"
          style={{ fontSize: 16, fontWeight: 400, lineHeight: "140%", color: Color.BLACK_PEARL }}
        >
          Please press the select car button on the right <br /> to add a new or an existing car
          profile.
        </p>
      </div>
    ) : (
      <div className="flex flex-col items-center justify-center py-16 gap-[10px]">
        <div className="flex justify-center items-center opacity-90" style={{}}>
          <Image src={require("../../../public/no_car_selected.png")} width={120} height={80} />
        </div>
        <h1
          className="font-medium mb-2 text-center"
          style={{
            fontWeight: 600,
            fontSize: 18,
            lineHeight: 1,
            color: Color.BLACK_PEARL,
          }}
        >
          You haven’t selected a vehicle yet
        </h1>
        <p
          className="text-[rgba(0,0,0,0.6)] text-sm text-center"
          style={{
            fontWeight: 400,
            fontSize: 16,
            lineHeight: "140%",
            color: Color.BLACK_PEARL,
          }}
        >
          Please press the select car button on the right <br /> to add a new or an existing car
          profile.
        </p>
      </div>
    )}
  </>
);

export default Services;
