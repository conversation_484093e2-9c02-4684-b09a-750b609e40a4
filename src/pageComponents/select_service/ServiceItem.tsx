import { useMemo, useState } from "react";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import GrayParagraph from "../../components/atom/Texts/GrayParagraph";
import Text14 from "../../components/atom/Texts/base/Text14";
import ServiceInfoModal from "../../components/organism/ServiceInfoModal";
import { Color } from "../../types/Color";
import AddServiceItemButton from "./AddServiceItemButton";

const ServiceItem = (props: {
  name: string;
  description: string;
  onClick: () => void;
  isSelected: boolean;
  price: number;
  servicePictureUri: string;
  index: number;
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const shortName = useMemo(() => {
    const indexOfBracket = props.name.indexOf("(");

    if (indexOfBracket === -1) {
      return props.name;
    } else {
      return props.name.substring(0, indexOfBracket);
    }
  }, [props.name]);

  const remainingName = useMemo(() => {
    const indexOfBracket = props.name.indexOf("(");

    if (indexOfBracket === -1) {
      return "";
    } else {
      return props.name.substring(indexOfBracket).replaceAll("(", "").replaceAll(")", "");
    }
  }, [props.name]);

  const handleSelection = (e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation();
    }
    props.onClick();
  };

  return (
    <>
      <ServiceInfoModal
        isOn={showDetails}
        onOff={() => setShowDetails(false)}
        name={props.name}
        description={props.description}
        price={props.price}
        servicePictureUri={props.servicePictureUri}
        onClick={props.onClick}
        isSelected={props.isSelected}
      />
      <div
        className={`${
          props.index % 3 === 2 ? "md:mr-0" : "md:mr-5"
        } items-center mr-0 relative w-full md:w-[299px] flex md:flex-col md:items-start rounded-sm mb-4 py-1 md:py-0 md:border select-none self-baseline overflow-hidden`}
        onClick={() => setShowDetails(true)}
      >
        <div className="md:overflow-hidden rounded-t-sm md:h-[206px] h-fit">
          <div className="relative hover:scale-105 transition-all duration-300 overflow-hidden">
            <div className="hidden md:block absolute w-full h-20 bg-gradient-to-b from-black to-transparent p-5 rounded-t-sm overflow-hidden">
              <GrayParagraph style={{ color: "white" }}>{remainingName}</GrayParagraph>
            </div>

            <img
              className="hidden w-full h-full rounded-none md:block"
              src={props.servicePictureUri}
              alt={`${shortName} service image`}
            />
            <div className="w-16 h-16">
              <img
                className="block w-full h-full object-cover rounded-2xl md:hidden"
                src={props.servicePictureUri}
                alt={`${shortName} service image`}
              />
            </div>
          </div>
        </div>
        <div className="w-full flex flex-col pr-0 md:pr-5 pl-[16px] md:h-[200px] md:pt-4">
          <div className="flex justify-between md:mb-[5px]">
            <div className="flex flex-col justify-center">
              <dt
                className="hidden leading-snug font-semibold md:block"
                style={{ fontSize: 16, marginBottom: 2, color: Color.BLACK_08 }}
              >
                {shortName}
              </dt>
              <dt
                className="block leading-snug font-semibold md:hidden"
                style={{
                  fontSize: 16,
                  lineHeight: 1,
                  marginBottom: 7,
                  color: Color.BLACK,
                }}
              >
                {shortName}
              </dt>
              <dd
                className="flex md:hidden"
                style={{
                  fontSize: 14,
                  fontWeight: 400,
                  lineHeight: "140%",
                  color: Color.BLACK,
                }}
              >
                {remainingName}
              </dd>
              {/* <dd
                className="block opacity-60 md:hidden"
                style={{
                  fontSize: 16,
                  fontWeight: 400,
                  lineHeight: 1,
                }}
              >
                Mechanical
              </dd> */}
            </div>
            <div className="flex items-center">
              <Text14
                style={{ marginTop: 5, marginRight: 1 }}
                className="hidden flex-col justify-center md:justify-start md:flex"
              >
                $
              </Text14>
              <dd
                className="hidden text-2xl font-semibold flex-col justify-center md:justify-start md:flex"
                style={{
                  color: Color.NORMAL_BLACK,
                }}
              >
                {(props.price / 100 + 15).toFixed(0)}
              </dd>
              <div className="flex flex-col justify-between md:hidden">
                <AddServiceItemButton
                  isSelected={props.isSelected}
                  onClick={(e) => handleSelection(e)}
                  isMobile
                  price={props.price}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-between">
            <div className="hidden md:block">
              <GrayParagraph
                style={{
                  marginBottom: 20,
                  height: 60,
                  overflow: "hidden",
                }}
              >
                {props.description}
              </GrayParagraph>
              <BackgroundSmallButton
                title={props.isSelected ? "Remove" : "Select"}
                backgroundColor={props.isSelected ? "REMOVEABLE" : "ACCENT"}
                onClick={(e) => handleSelection(e)}
                style={{
                  boxShadow: props.isSelected ? "0px 0px 0px 1px #ff4242" : "none",
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ServiceItem;
