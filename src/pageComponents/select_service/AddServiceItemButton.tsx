import { GarbageIcon } from "../../components/atom/Icons";
import { Color } from "../../types/Color";

const AddServiceItemButton = (props: {
  isSelected: boolean;
  onClick: (event?: any) => void;
  price?: number;
  isMobile?: boolean;
  style?: React.CSSProperties;
}) => {
  return (
    <>
      {!props.isMobile ? null : (
        <button
          className={` h-[40px] w-[72px] flex ml-4 flex-col place-items-center justify-center rounded-xl`}
          onClick={(event) => props.onClick(event)}
          style={{
            backgroundColor: props.isSelected ? Color.BRIDESMAID : Color.ACCENT,
            ...props.style,
          }}
        >
          {props.isSelected ? (
            <GarbageIcon />
          ) : props.price !== undefined ? (
            <span
              className="justify-center"
              style={{
                fontSize: 16,
                fontWeight: 400,
                lineHeight: 1,
                color: Color.WHITE,
              }}
            >{`$ ${(props.price / 100 + 15).toFixed(0)}`}</span>
          ) : null}
        </button>
      )}
    </>
  );
};

export default AddServiceItemButton;
