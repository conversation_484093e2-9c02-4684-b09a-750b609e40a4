import Image from "next/image";
import { Color } from "../../types/Color";

interface AddCarButtonProps {
  onClick: () => void;
  style?: React.CSSProperties;
}

const AddCarButton = (props: AddCarButtonProps) => {
  return (
    <button
      className="flex items-center justify-center border mx-5 rounded-lg"
      style={{
        width: 112,
        height: 112,
        ...props.style,
      }}
      onClick={props.onClick}
    >
      <div className="w-16 h-16 relative flex flex-col items-center justify-center">
        <div className="w-6 h-6">
          <Image src={require("../../../public/plus.png")} />
        </div>
        <span
          className="text-sm mt-1"
          style={{
            color: Color.NORMAL_BLACK,
          }}
        >
          Add Car
        </span>
      </div>
    </button>
  );
};

export default AddCarButton;
