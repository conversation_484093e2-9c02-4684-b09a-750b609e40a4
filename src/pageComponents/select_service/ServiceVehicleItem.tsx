import Image from "next/image";
import { useContext, useMemo } from "react";
import AccentTextButton from "../../components/atom/Buttons/AccentTextButton";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import { CarServiceWithoutAddon } from "../../hooks/useCarServices";
import { Service } from "../../types";
import VehicleItem from "../customer_profile/VehicleItem";
import ServiceItem from "./ServiceItem";
import { carServiceContext } from "../../contexts/CarServiceContext";
import { Color } from "../../types/Color";

interface ServiceVehicleItemProps {
  index: number;
  filteredServices: Service[] | undefined;
  carService?: CarServiceWithoutAddon;

  onClickEditService: () => void;
  onClickConfirmServices: () => void;
  onClickSelectServiceAtSelectedCarService: (service: Service) => void;

  isServiceSectionSelected: boolean;
  style?: React.CSSProperties;
}

export const ServiceVehicleItem = (props: ServiceVehicleItemProps) => {
  const context = useContext(carServiceContext);

  const servicesSelectedText = useMemo(() => {
    if (props.carService?.services?.length === 0) {
      return "No services selected";
    } else {
      return props.carService?.services?.map((service) => service.name).join(", ");
    }
  }, [props.carService?.services]);

  const servicesSelectedTextMobile = useMemo(() => {
    if (props.carService?.services.length === 0) {
      return null;
    } else {
      return props.carService?.services.map((service) => ({
        name: service.name,
        price: service.price,
      }));
    }
  }, [props.carService?.services]);

  if (props.carService) {
    return (
      <div
        className="flex flex-col md:mt-6 md:px-8 mx-5 md:mx-0 border-b border-b-gray"
        style={{
          ...props.style,
        }}
      >
        <div className="flex items-center justify-between self-stretch md:hidden md:mb-3">
          <VehicleItem
            index={props.index}
            car={props.carService.car}
            style={{
              paddingTop: 0,
            }}
            onClickDelete={() => {
              if (context) {
                context.handleOnClickDeleteCarService(props.index);
              }
            }}
            isMobile
          />
        </div>
        <div className="hidden items-center justify-between self-stretch md:block md:mb-3">
          <VehicleItem
            index={props.index}
            car={props.carService.car}
            style={{
              paddingTop: 0,
            }}
            onClickDelete={() => {
              if (context) {
                context.handleOnClickDeleteCarService(props.index);
              }
            }}
          />
        </div>
        <div className="block md:hidden" style={{ height: 24 }} />
        <div className="flex flex-col ">
          <div className="flex flex-col md:flex-row items-stretch md:items-center justify-between mb-[26px] md:mb-6">
            <SelectedServiceMobileDescription servicesSelectedText={servicesSelectedTextMobile} />
            <div className="hidden flex-col md:flex">
              <span className="mr-3" style={{ fontSize: 16, fontWeight: "600", marginBottom: 2 }}>
                Selected services
              </span>
              <span
                style={{
                  fontSize: 14,
                }}
              >
                {servicesSelectedText}
              </span>
            </div>
            <div className="flex items-center md:ml-5">
              <div className="md:block hidden">
                {props.carService.services?.length > 0 ? (
                  <div
                    className="flex justify-center items-center"
                    style={{
                      width: 20,
                      height: 20,
                      marginRight: 16,
                    }}
                  >
                    <Image src={require("../../../public/check.png")} />
                  </div>
                ) : null}
              </div>
              <div className="hidden w-full md:block md:w-32 mt-4 md:mt-0">
                <BackgroundSmallButton
                  backgroundColor="ACCENT"
                  title={
                    props.isServiceSectionSelected
                      ? "Confirm"
                      : props.carService.services.length > 0
                      ? "Edit Services"
                      : "Add Services"
                  }
                  onClick={() => {
                    if (props.isServiceSectionSelected) {
                      props.onClickConfirmServices();
                    } else {
                      props.onClickEditService();
                    }
                  }}
                  style={{
                    width: "100%",
                  }}
                />
              </div>
            </div>
          </div>
          <div
            className="flex flex-col md:hidden"
            style={{ marginBottom: props.isServiceSectionSelected ? 4 : 36 }}
          >
            <div className="flex flex-row justify-between">
              <AccentTextButton
                title="Remove Vehicle"
                onClick={() => {
                  if (context) {
                    context.handleOnClickDeleteCarService(props.index);
                  }
                }}
                disabled={false}
                style={{
                  fontSize: 16,
                  fontWeight: 500,
                  lineHeight: 1,
                  letterSpacing: "-0.04em",
                }}
              />
              <button className="flex flex-row items-center">
                <div
                  className="flex flex-col items-center justify-between object-cover"
                  style={{
                    width: 10,
                    marginRight: 8,
                    minWidth: 10,
                  }}
                >
                  {props.isServiceSectionSelected ? (
                    <Image src={require("../../../public/vector_folded_orange.png")} />
                  ) : (
                    <Image src={require("../../../public/vector_stretched_orange.png")} />
                  )}
                </div>
                <AccentTextButton
                  title={props.isServiceSectionSelected ? "Hide" : "Select Service"}
                  onClick={(event) => {
                    if (props.isServiceSectionSelected) {
                      props.onClickConfirmServices();
                      event?.stopPropagation();
                    } else {
                      props.onClickEditService();
                      event?.stopPropagation();
                    }
                  }}
                  disabled={false}
                  style={{
                    fontSize: 16,
                    fontWeight: 500,
                    lineHeight: 1,
                    letterSpacing: "-0.04em",
                  }}
                />
              </button>
            </div>
          </div>
          {props.isServiceSectionSelected ? (
            <>
              <div className="w-full flex flex-col">
                <div style={{ marginBottom: 24 }} />
                <span
                  className="hidden md:block"
                  style={{ fontSize: 16, fontWeight: "600", marginBottom: 8 }}
                >
                  Selectable Services
                </span>
                <div className="flex flex-wrap md:grid md:grid-cols-2 md:w-fit xl:grid-cols-3 md:pb-6">
                  {props.filteredServices?.map((item, index) => (
                    <ServiceItem
                      key={index}
                      index={index}
                      name={item.name}
                      description={item.description}
                      price={item.price}
                      isSelected={
                        props.carService?.services.findIndex(
                          (service) => service.id === item.id
                        ) !== -1
                      }
                      onClick={() => props.onClickSelectServiceAtSelectedCarService(item)}
                      servicePictureUri={item.servicePictureUri}
                    />
                  ))}
                </div>
              </div>
              <button className="flex flex-row items-center justify-end mb-10 md:hidden">
                <div
                  className="flex flex-col items-center justify-between object-cover"
                  style={{
                    width: 10,
                    minWidth: 10,
                    marginRight: 8,
                  }}
                >
                  <Image src={require("../../../public/vector_folded_orange.png")} />
                </div>
                <AccentTextButton
                  title={`Hide ${props.carService.car.model}’s service selection`}
                  onClick={(event) => {
                    if (props.isServiceSectionSelected) {
                      props.onClickConfirmServices();
                      event?.stopPropagation();
                    } else {
                      props.onClickEditService();
                      event?.stopPropagation();
                    }
                  }}
                  disabled={false}
                  style={{
                    fontWeight: 500,
                    lineHeight: 1,
                    letterSpacing: "-0.04em",
                  }}
                />
              </button>
            </>
          ) : null}
        </div>
        <div className="block md:hidden" />
      </div>
    );
  }

  return null;
};

const SelectedServiceMobileDescription = (props: {
  servicesSelectedText:
    | {
        name: string;
        price: number;
      }[]
    | undefined
    | null;
}) => {
  return (
    <>
      {props.servicesSelectedText !== undefined ? (
        <>
          {props.servicesSelectedText === null ? (
            <div className="flex flex-col items-start md:hidden">
              <span
                className="mr-3"
                style={{
                  fontSize: 14,
                  fontWeight: "500",
                  lineHeight: "135%",
                  color: Color.BLACK_07,
                }}
              >
                No selected service.
                <br />
                You Should select at least one service to proceed.
              </span>
            </div>
          ) : (
            <div className="flex flex-col gap-y-[10px] md:hidden">
              {props.servicesSelectedText.map((item, index) => (
                <div className="flex flex-row items-center justify-between">
                  <span
                    style={{
                      fontSize: 15,
                      fontWeight: 500,
                      color: Color.BLACK_08,
                    }}
                  >{`• ${item.name}`}</span>
                  <span
                    style={{
                      fontSize: 15,
                      fontWeight: 500,
                      lineHeight: 1,
                      color: Color.BLACK_08,
                    }}
                  >{`$${(item.price / 100 + 15).toFixed(0)}`}</span>
                </div>
              ))}
            </div>
          )}
        </>
      ) : null}
    </>
  );
};
