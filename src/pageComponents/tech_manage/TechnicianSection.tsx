import { PencilIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import ProfilePlaceholder from "../../components/atom/ProfilePlaceholder";

interface TechnicianSectionProps {
  technicians: any[];
  onEditTechnician: (technicianId: number) => void;
}

const TechnicianSection = ({ technicians, onEditTechnician }: TechnicianSectionProps) => {
  return (
      <Paper style={{ marginBottom: 8, padding: 24, width: "100%" }}>
        {technicians.map((technician, index) => (
          <div
            key={technician.technicianId}
            className={`flex items-center justify-between p-4 ${index !== technicians.length - 1 ? "border-b border-gray-300" : ""}`}
          >
            
            <div className="flex items-center space-x-4">
              {technician.profilePictureUri ? (
                <img src={technician?.profilePictureUri} alt={`${technician.firstName} ${technician.lastName}`} className="w-16 h-16 rounded-full object-cover" />
              ) : (
                <ProfilePlaceholder size={64} />
              )}
              <div>
                <p className="font-medium text-lg">
                  {technician.firstName} {technician.lastName}
                </p>
                <p className="text-sm text-gray-500">{technician.email}</p>
              </div>
            </div>
            <button
              onClick={() => {
                onEditTechnician(technician.technicianId);
              } }
              className="ml-auto"
            >
              <PencilIcon />
            </button>
          </div>
        ))}
      </Paper>
  );
};

export default TechnicianSection;
