import SizeableImage from "../../components/atom/SizeableImage";
import { useBookingTireStored } from "../../hooks/useBookingTireStored";
import { Color } from "../../types/Color";
import BookingTireStoredRadioButton from "./BookingTireStoredRadioButton";

interface BookingTireStoredProps {
  bookingTireStoredHook: ReturnType<typeof useBookingTireStored>;
  isDisabled: boolean;
  style?: React.CSSProperties;
}

const BookingTireStored = (props: BookingTireStoredProps) => {
  return (
    <div
      className="flex flex-col justify-between border p-5 rounded-lg gap-3 lg:flex-row"
      style={{
        borderColor:
          props.bookingTireStoredHook.error.length > 0 ? Color.SWEET_PINK : "rgb(229 229 229)",
        ...props.style,
      }}
    >
      <div className="flex flex-col gap-3">
        <span
          className="text-base font-semibold"
          style={{
            color: Color.INDIGO_BLUE_08,
          }}
        >
          Are your tires stored with us?*
        </span>
        <BookingTireStoredRadioButton
          checkedValue={props.bookingTireStoredHook.bookingTireStored}
          onClick={(value: "YES" | "NO") => {
            props.bookingTireStoredHook.handleOnClick(value);
          }}
          isDisabled={props.isDisabled}
          type="YES"
        />
        <BookingTireStoredRadioButton
          checkedValue={props.bookingTireStoredHook.bookingTireStored}
          onClick={(value: "YES" | "NO") => {
            props.bookingTireStoredHook.handleOnClick(value);
          }}
          isDisabled={props.isDisabled}
          type="NO"
        />
      </div>

      {props.bookingTireStoredHook.error.length > 0 && (
        <div className="flex flex-row justify-start lg:items-center lg:flex-col lg:justify-center">
          <div className="flex flex-row">
            <SizeableImage
              size={20}
              src={require("../../../public/alert-circle.png")}
              style={{
                marginRight: 8,
              }}
            />
            <p
              className="text-[#F04438] text-base"
              style={{
                lineHeight: "20px",
              }}
            >
              {props.bookingTireStoredHook.error}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingTireStored;
