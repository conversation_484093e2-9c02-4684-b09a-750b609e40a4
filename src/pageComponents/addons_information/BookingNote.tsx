import { Color } from "../../types/Color";

interface BookingNoteProps {
  value: string;
  isDisabled: boolean;
  onChange: (e: any) => void;
  style?: React.CSSProperties;
}

const BookingNote = (props: BookingNoteProps) => {
  return (
    <div
      className="flex flex-col gap-4 p-5 border border-neutral-200 rounded-lg"
      style={props.style}
    >
      <span
        className="test-xs font-semibold"
        style={{
          color: Color.INDIGO_BLUE_08,
        }}
      >
        Note to the technician
      </span>
      <textarea
        className="border border-gray-300 text-gray-900 rounded-lg focus:ring-orange-400 focus:border-orange-400 block w-full py-3 px-4 outline-none transition-all min-h-[120px]"
        value={props.value}
        disabled={props.isDisabled}
        onChange={(event) => props.onChange(event)}
        style={{
          backgroundColor: props.isDisabled ? Color.DISABLED_GRAY : Color.WHITE,
        }}
      />
    </div>
  );
};

export default BookingNote;
