import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface TotalAddonButtonProps {
  onClick: () => void;
  isStretched: boolean;
  numOfTotalAddons: number;
}

const TotalAddonButton = (props: TotalAddonButtonProps) => {
  return (
    <button className="flex flex-row justify-start gap-3" onClick={props.onClick}>
      <span
        className="text-base"
        style={{
          color: Color.ACCENT,
        }}
      >
        {`${props.numOfTotalAddons === 0 ? "No" : props.numOfTotalAddons} Selected`}
      </span>
      {props.isStretched ? (
        <SizeableImage
          src={require("../../../public/folded_accent.png")}
          size={18}
          style={{
            alignSelf: "center",
          }}
        />
      ) : (
        <SizeableImage
          src={require("../../../public/stretched_accent.png")}
          size={18}
          style={{
            alignSelf: "center",
          }}
        />
      )}
    </button>
  );
};

export default TotalAddonButton;
