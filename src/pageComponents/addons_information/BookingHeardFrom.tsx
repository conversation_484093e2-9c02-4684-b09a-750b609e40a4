import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface BookingHeardFromProps {
  value: string;
  isDisabled: boolean;
  onChange: (e: any) => void;
  error: string;
  style?: React.CSSProperties;
}

const BookingHeardFrom = (props: BookingHeardFromProps) => {
  return (
    <div
      className="flex flex-col border border-neutral-200 gap-4 p-5 rounded-lg"
      style={{
        borderColor: props.error.length > 0 ? Color.SWEET_PINK : "rgb(229 229 229)",
        ...props.style,
      }}
    >
      <span
        className="test-xs font-semibold"
        style={{
          color: Color.INDIGO_BLUE_08,
        }}
      >
        How did you hear about us?*
      </span>
      <input
        type="text"
        className="border border-gray-300 text-gray-900 rounded-lg focus:ring-orange-400 focus:border-orange-400 block w-full py-3 px-4 outline-none transition-all min-h-[40px]"
        placeholder="Tell us how you heard about us!"
        value={props.value}
        disabled={props.isDisabled}
        onChange={(event) => props.onChange(event)}
        style={{
          backgroundColor: props.isDisabled ? Color.DISABLED_GRAY : Color.WHITE,
        }}
      />
      {props.error.length > 0 && (
        <div className="flex flex-row">
          <SizeableImage
            size={20}
            src={require("../../../public/alert-circle.png")}
            style={{
              marginRight: 8,
            }}
          />
          <p
            className="text-[#F04438] text-base"
            style={{
              lineHeight: "20px",
            }}
          >
            {props.error}
          </p>
        </div>
      )}
    </div>
  );
};

export default BookingHeardFrom;
