import { Color } from "../../types/Color";

interface BookingTireStoredRadioButtonProps {
  checkedValue: string;
  isDisabled?: boolean;
  onClick: (value: "YES" | "NO") => void;
  type: "YES" | "NO";
}

const BookingTireStoredRadioButton = (props: BookingTireStoredRadioButtonProps) => {
  return (
    <>
      {props.type === "YES" ? (
        <button
          className="flex flex-row w-[60px] h-[28px]"
          onClick={() => {
            props.onClick("YES");
          }}
          disabled={props.isDisabled}
        >
          <div className="flex flex-col justify-center h-full">
            <input
              type="radio"
              className="mr-2"
              checked={props.checkedValue === "YES"}
              disabled={props.isDisabled}
            />
          </div>
          <span
            className="text-base font-medium"
            style={{
              color: Color.INDIGO_BLUE_07,
              lineHeight: "28px",
            }}
          >
            Yes
          </span>
        </button>
      ) : (
        <button
          className="flex flex-row w-[60px] h-[28px]"
          onClick={() => {
            props.onClick("NO");
          }}
          disabled={props.isDisabled}
        >
          <div className="flex flex-col justify-center h-full">
            <input
              type="radio"
              checked={props.checkedValue === "NO"}
              className="mr-2"
              disabled={props.isDisabled}
            />
          </div>
          <span
            className="text-base font-medium"
            style={{
              color: Color.INDIGO_BLUE_07,
              lineHeight: "28px",
            }}
          >
            No
          </span>
        </button>
      )}
    </>
  );
};

export default BookingTireStoredRadioButton;
