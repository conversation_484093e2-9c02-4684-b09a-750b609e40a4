import { Color } from "../../types/Color";

interface SeeAllAddonsButtonProps {
  onClick: () => void;
  style?: React.CSSProperties;
  textStyle?: React.CSSProperties;
}

const SeeAllAddonsButton = (props: SeeAllAddonsButtonProps) => {
  return (
    <button className="min-w-[140px]" onClick={props.onClick} style={props.style}>
      <span
        className="flex flex-row font-semibold"
        style={{
          fontSize: 16,
          lineHeight: "21.6px",
          letterSpacing: "0.02em",
          color: Color.ACCENT,
          textDecorationLine: "underline",
          ...props.textStyle,
        }}
      >
        See All Add-ons
      </span>
    </button>
  );
};

export default SeeAllAddonsButton;
