import moment from "moment";
import Head from "next/head";
import { CSSProperties, ReactNode, useContext } from "react";
import SelectAddonModal from "../../components/Modal/SelectAddonModal";
import NoPaddingPaper from "../../components/atom/NoPaddingPaper";
import SizeableImage from "../../components/atom/SizeableImage";
import Spinner from "../../components/atom/Spinner";
import StepHeaderIndigoBlue from "../../components/molecule/StepHeaderIndigoBlue";
import BookingStepper from "../../components/organism/BookingStepper";
import BookingStepperMobile from "../../components/organism/BookingStepperMobile";
import MobileOrderSummary from "../../components/organism/MobileOrderSummary";
import OrderSummary from "../../components/organism/OrderSummary";
import { addonContext } from "../../contexts/AddonContext";
import { Color } from "../../types/Color";
import { PhoneNumber } from "../../types/PhoneNumber";
import { Time } from "../../types/Time";
import ErrorAlertModal from "../booking_date/ErrorAlertModal";
import AlertModal from "../customer_booking/AlertModal";
import BookingHeardFrom from "./BookingHeardFrom";
import BookingNote from "./BookingNote";
import BookingTireStored from "./BookingTireStored";
import ListAddonBlock from "./ListAddonBlock";
import ListAddonBlockButton from "./ListAddonBlockButton";
import { LG, MD, XL, XS, XXS, XXXS } from "../../values";
import SelectAddonBlock from "./SelectAddonBlock";
import { globalContext } from "../../contexts/GlobalContext";
import ClearAddonButton from "./ClearAddonButton";
import SeeAllAddonsButton from "./SeeAllAddonsButton";
import TotalAddonButton from "./TotalAddonButton";

const AddonInformationContainer = () => {
  const context = useContext(addonContext)!;
  const addons = context!.remoteAddons;
  const deviceWidth = useContext(globalContext)?.dimensionWidth;

  return (
    <>
      {context.isSessionFetchErrorOccured ? null : (
        <div>
          <Head>
            <title>Review Booking Info - Wheel Easy</title>
          </Head>
          {/* Web Stepper */}
          <div className="hidden md:block">
            <BookingStepper step={3} />
          </div>
          {/* Mobile Stepper */}
          <div className="block md:hidden">
            <BookingStepperMobile step={3} />
          </div>
          <div
            className="flex-col flex-1 bg-[#F8FAFB] flex overflow-y-scroll h-[calc(var(--vh,1vh)*100-276px)] md:max-lg:h-[calc(var(--vh,1vh)*100-110px)] lg:h-[calc(var(--vh,1vh)*100-120px)]"
            ref={context.scrollableElementRef}
          >
            <div className="flex flex-col h-full md:px-10">
              <SelectAddonModal />
              <ErrorAlertModal
                isOn={context.errorMessageFromServer !== null}
                onOff={() => {
                  context.deleteOccupation();
                  context.router.back();
                  context.resetErrorMessageFromServer();
                }}
                title="Server Error"
                description={context.errorMessageFromServer!}
              />
              <ErrorAlertModal
                isOn={context.errorMessage !== null}
                onOff={() => context.setErrorMessage(null)}
                title={context.errorMessage ? context.errorMessage.title : ""}
                description={context.errorMessage ? context.errorMessage.description : ""}
              />
              <AlertModal
                isOn={context.isAlertModalOn}
                onOff={() => {
                  context.setIsAlertModalOn(false);
                  context.deleteOccupation();
                  context.router.back();
                }}
                title="Time is up"
                description="Please start over again from the previous page."
                isButton={{
                  buttonTitle: "Continue",
                  onClickButton: () => {
                    context.handleOnClickContinueAlertModal();
                  },
                  isLoading: context.isLoadingContinueAlertModal,
                }}
                modalSize={{
                  mobile: {
                    width: 375,
                    height: 254,
                  },
                  web: {
                    width: 375,
                    height: 254,
                  },
                }}
              />
              {/* web */}
              <div className="hidden w-full md:flex h-full">
                <div className="flex flex-row justify-center mb-auto w-full h-full pt-10">
                  <div className="flex flex-col w-full items-stretch mr-6 xl:w-[1054px]">
                    <NoPaddingPaper borders="rounded-lg">
                      <div className="flex flex-col items-stretch">
                        <div className="flex-row items-center hidden md:flex justify-between p-6 pb-4">
                          <div className="mr-8">
                            <StepHeaderIndigoBlue title="Add-ons" description="" />
                          </div>
                          <TotalAddonButton
                            onClick={() => {
                              context.setIsDisplayedAddonsStretched(
                                !context.isDisplayedAddonsStretched
                              );
                            }}
                            isStretched={context.isDisplayedAddonsStretched}
                            numOfTotalAddons={context.numOfTotalAddons}
                          />
                        </div>
                        {!context.isDisplayedAddonsStretched ? null : (
                          <div className="flex flex-col gap-4 p-6 pt-0">
                            {context.carServiceExtensionHook.isLoading ? (
                              <div className="w-full flex items-center justify-center h-40">
                                <Spinner />
                              </div>
                            ) : context.carServiceExtensionHook.carServices !== undefined &&
                              context.carServiceExtensionHook.carServices.length > 0 ? (
                              <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 grid-flow-row gap-4">
                                {context.carServiceExtensionHook.carServices.map(
                                  (carService, index) => (
                                    <ListAddonBlockButton
                                      carService={carService}
                                      isSelected={context.selectedCarAddonIndex === index}
                                      numOfSelected={carService.addons.length}
                                      onClick={() => {
                                        if (context) {
                                          context.handleOnClickEditAddon(
                                            carService.car,
                                            carService.services,
                                            carService.addons
                                          );
                                          context.setSelectedCarAddonIndex(index);
                                        }
                                      }}
                                    />
                                  )
                                )}
                              </div>
                            ) : null}
                            <div className="border-b" />
                            {addons !== undefined && addons.length !== 0 ? (
                              <div className="flex flex-col gap-3">
                                <div
                                  className="flex flex-row justify-between "
                                  style={{
                                    color: Color.BLACK_06,
                                  }}
                                >
                                  <span className="font-semibold">
                                    {context.carServiceExtensionHook.carServices &&
                                    context.carServiceExtensionHook.carServices[
                                      context.carServiceExtensionHook.selectedCarAddonIndex
                                    ]?.addons.length > 0
                                      ? `${
                                          context.carServiceExtensionHook.carServices[
                                            context.carServiceExtensionHook.selectedCarAddonIndex
                                          ]?.addons.length
                                        } Selected`
                                      : "No Selected"}
                                  </span>
                                  <ClearAddonButton
                                    onClick={() => {
                                      if (context) {
                                        context.handleOnClickClearAddonInPage();
                                      }
                                    }}
                                    style={{
                                      lineHeight: "21.6px",
                                      fontSize: 16,
                                    }}
                                  />
                                </div>
                                <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 grid-flow-row gap-4">
                                  {context.displayedAddons.map((addon, index) => (
                                    <SelectAddonBlock
                                      addon={addon}
                                      key={index.toString()}
                                      onClick={() => {
                                        context?.handleOnClickAddonBlock(addon);
                                        context.displayedAddonClickedRef.current = true;
                                      }}
                                      isSelected={context!.getIsAddonInCarServiceExtensions(addon)}
                                      isLeftEnd={false}
                                      isRightEnd={
                                        deviceWidth && deviceWidth >= XL
                                          ? index % 4 === 3
                                          : deviceWidth && deviceWidth >= LG
                                          ? index % 3 === 2
                                          : deviceWidth && deviceWidth >= MD
                                          ? index % 2 === 1
                                          : false
                                      }
                                      isFirstRow={
                                        deviceWidth && deviceWidth >= XL
                                          ? index <= 3
                                          : deviceWidth && deviceWidth >= LG
                                          ? index <= 2
                                          : deviceWidth && deviceWidth >= MD
                                          ? index <= 1
                                          : false
                                      }
                                      style={{
                                        width: "100%",
                                      }}
                                    />
                                  ))}
                                </div>
                              </div>
                            ) : null}
                            <div className="border-b" />
                            <div className="flex flex-row justify-between">
                              {context.carServiceExtensionHook.carServices &&
                              context.findNonMatchingAddons(
                                context.displayedAddons,
                                context.carServiceExtensionHook.carServices[
                                  context.carServiceExtensionHook.selectedCarAddonIndex
                                ]?.addons
                              ).length > 0 ? (
                                <span className="text-black text-base">
                                  and{" "}
                                  <span className="font-semibold">
                                    {context
                                      .findNonMatchingAddons(
                                        context.displayedAddons,
                                        context.carServiceExtensionHook.carServices[
                                          context.carServiceExtensionHook.selectedCarAddonIndex
                                        ]?.addons
                                      )
                                      .map((item) => item.name)
                                      .join(", ")}
                                  </span>{" "}
                                  selected.
                                </span>
                              ) : (
                                <div />
                              )}
                              <SeeAllAddonsButton
                                onClick={() => {
                                  context.setIsSelectAddonModalOn(true);
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                      <div
                        className="flex flex-col items-stretch border-t-[0.5px]"
                        style={{
                          borderColor: Color.SLIVER_GRAY,
                        }}
                      >
                        <div className="flex-row items-center justify-between p-6 pb-4">
                          <StepHeaderIndigoBlue title="Booking" description="" />
                        </div>
                        {context.isFetchLoading ? (
                          <div className="w-full h-full flex flex-col justify-center items-center rounded-xl bg-white min-h-[400px]">
                            <div className="flex flex-col items-center">
                              <Spinner />
                            </div>
                          </div>
                        ) : (
                          <div className="relative flex flex-col p-6 pt-0 gap-4">
                            <div className="flex flex-col border border-neutral-200 bg-white rounded-md px-6 py-5 gap-4">
                              <div className="flex flex-row justify-between">
                                <span className="text-xl font-semibold text-neutral-900">
                                  Mechanical
                                </span>
                              </div>
                              <div className="border-b " />
                              <div className="flex flex-col justify-between lg:flex-row">
                                <div className="flex flex-col mr-8 xl:mr-16 mb-6 lg:mb-0">
                                  <span
                                    className="test-xs font-semibold"
                                    style={{
                                      color: Color.INDIGO_BLUE_08,
                                      marginBottom: 16,
                                    }}
                                  >
                                    Service Items
                                  </span>
                                  <div className="flex flex-col space-y-4">
                                    {context.categorySessionHook.categorySessionItems.map(
                                      (item, index) => (
                                        <>
                                          {item.services.map((service, index) => (
                                            <ServiceItemContent
                                              pictureUri={service.pictureUri}
                                              quantity={service.cars.length}
                                              serviceName={service.name}
                                              durationInMinutes={service.duration}
                                            />
                                          ))}
                                        </>
                                      )
                                    )}
                                  </div>
                                </div>
                                <div className="flex flex-col gap-6">
                                  <div className="flex flex-col">
                                    <span
                                      className="test-xs font-semibold"
                                      style={{
                                        color: Color.INDIGO_BLUE_08,
                                        marginBottom: 16,
                                      }}
                                    >
                                      Booking Info
                                    </span>
                                    <div className="space-y-4">
                                      <BookingInfoElement
                                        imgSrc={require("../../../public/user_grey.png")}
                                        text={context.nowAddress?.customerName}
                                      />
                                      <BookingInfoElement
                                        imgSrc={require("../../../public/phone_grey.png")}
                                        text={
                                          context.nowAddress
                                            ? PhoneNumber.getPhoneNumberFromAPI(
                                                context.nowAddress?.customerPhone
                                              ).getFormattedFullPhoneNumber()
                                            : null
                                        }
                                      />
                                      <BookingInfoElement
                                        imgSrc={require("../../../public/address_grey.png")}
                                        text={
                                          context.nowAddress ? (
                                            <span>
                                              <span>{context.nowAddress?.street1}</span>
                                              <br />
                                              {context.nowAddress.street2 ? (
                                                <>
                                                  <span>{context.nowAddress?.street2}</span>
                                                  <br />
                                                </>
                                              ) : null}
                                              <span>{context.nowAddress?.city}</span>
                                              <span>, {context.nowAddress?.province}</span>
                                              <span>, {context.nowAddress?.country}</span>
                                              <br />
                                              <span>{context.nowAddress?.postal}</span>
                                            </span>
                                          ) : undefined
                                        }
                                        style={{
                                          alignItems: "flex-start",
                                        }}
                                      />
                                      <BookingInfoElement
                                        text={
                                          context.isReservationReady && context.assignedDateTime
                                            ? context.assignedDateTime?.format("dddd MMMM DD, YYYY")
                                            : undefined
                                        }
                                        imgSrc={require("../../../public/calendar_grey.png")}
                                      />
                                      <BookingInfoElement
                                        text={
                                          context.isReservationReady && context.assignedDateTime
                                            ? context.assignedDateTime?.format("hh:mm A") +
                                              " - " +
                                              moment(context.assignedDateTime)
                                                .add(context.totalDurationByMin, "minutes")
                                                .format("hh:mm A")
                                            : undefined
                                        }
                                        imgSrc={require("../../../public/time_grey.png")}
                                      />
                                    </div>
                                  </div>
                                  {/* <div className="flex flex-col">
                                    <span
                                      className="test-xs font-semibold"
                                      style={{
                                        color: Color.INDIGO_BLUE_08,
                                        marginBottom: 16,
                                      }}
                                    >
                                      Technician
                                    </span>
                                    {context.isReservationReady &&
                                    context.carServiceExtensionHook.carServicesExtensions ? (
                                      <TechProfile
                                        profilePictureUri={
                                          context.carServiceExtensionHook.carServicesExtensions[0]
                                            ?.technician.profilePictureUri
                                        }
                                        name={
                                          context.carServiceExtensionHook.carServicesExtensions[0]
                                            .technician.name
                                        }
                                        rateOfReviews={
                                          context.carServiceExtensionHook.carServicesExtensions[0]
                                            .technician.rating
                                        }
                                        numOfReviews={
                                          context.carServiceExtensionHook.carServicesExtensions[0]
                                            .technician.numOfReviews
                                        }
                                      />
                                    ) : (
                                      <span
                                        className="test-xs font-semibold"
                                        style={{
                                          color: Color.INDIGO_BLUE_08,
                                        }}
                                      >
                                        -
                                      </span>
                                    )}
                                  </div> */}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                      <div
                        className="flex flex-col items-stretch border-t-[0.5px]"
                        style={{
                          borderColor: Color.SLIVER_GRAY,
                        }}
                        ref={context.scrollTargetWebRef}
                      >
                        <div className="flex-row items-center justify-between p-6 pb-4">
                          <StepHeaderIndigoBlue title="Checklist" description="" />
                        </div>
                        <div className="relative flex flex-col p-6 pt-0 gap-4">
                          <BookingTireStored
                            bookingTireStoredHook={context.bookingTireStoredHook}
                            isDisabled={context.isLoadingPayment}
                          />
                          <BookingHeardFrom
                            value={context.bookingHeardFromHook.bookingHeardFrom}
                            onChange={(e: any) =>
                              context.bookingHeardFromHook.handleBookingHeardFrom(e)
                            }
                            isDisabled={context.isLoadingPayment}
                            error={context.bookingHeardFromHook.error}
                          />
                          <BookingNote
                            value={context.bookingNoteHook.bookingNote}
                            onChange={(e: any) => context.bookingNoteHook.handleBookingNote(e)}
                            isDisabled={context.isLoadingPayment}
                          />
                        </div>
                      </div>
                    </NoPaddingPaper>
                    <div style={{ paddingBottom: 40 }} />
                  </div>
                  <div
                    className=""
                    style={{
                      position: context.orderSummaryStickyHook.getPosition(),
                      top: context.orderSummaryStickyHook.getTop(),
                      right: context.orderSummaryStickyHook.getRight(),
                    }}
                  >
                    <OrderSummary
                      orderSummaryHook={context.orderSummaryHook}
                      serviceCars={context.orderSummaryHook.serviceCars}
                      isFetchLoading={context.orderSummaryHook.serviceCars === undefined}
                      fourPage={{
                        addonCars: context.orderSummaryHook.addonCars,
                        onClickPayNow: context.handleOnClickPayNow,
                        onClickGoBack: context.handleOnClickGoBack,
                        remainingTime: Time.convertSecondToMinute(context.timerHook.nowSecond),
                        isLoadingPayment: context.isLoadingPayment,
                        isDisabledPayment: false,
                        isDisabledGoback: context.isDisabledGoback,
                      }}
                    />
                  </div>
                  <div
                    className="min-w-[280px]"
                    style={{
                      display: context.orderSummaryStickyHook.getDisplay(),
                    }}
                  />
                </div>
              </div>

              {/* mobile */}
              <div className="flex flex-col w-full h-full justify-between md:hidden">
                <div className="flex flex-col pb-14">
                  <NoPaddingPaper
                    borders="rounded-none shadow-none border-0 border-b-[0.5px] border-t-[0.5px]"
                    style={{
                      borderColor: Color.SLIVER_GRAY,
                    }}
                  >
                    <div className="flex flex-row justify-between p-4">
                      <StepHeaderIndigoBlue title="Add-ons" description="" />
                      <div className="flex flex-col justify-center">
                        <TotalAddonButton
                          onClick={() => {
                            context.setIsDisplayedAddonsStretched(
                              !context.isDisplayedAddonsStretched
                            );
                          }}
                          isStretched={context.isDisplayedAddonsStretched}
                          numOfTotalAddons={context.numOfTotalAddons}
                        />
                      </div>
                    </div>
                    {!context.isDisplayedAddonsStretched ? null : (
                      <div className="flex flex-col gap-4">
                        {context.carServiceExtensionHook.isLoading ? (
                          <div className="w-full flex items-center justify-center h-40">
                            <Spinner />
                          </div>
                        ) : context.carServiceExtensionHook.carServices !== undefined &&
                          context.carServiceExtensionHook.carServices.length > 0 ? (
                          <div
                            className="flex flex-row p-4 pt-0 gap-4 border-b-[0.5px] overflow-x-scroll pb-4"
                            style={{
                              borderColor: Color.SLIVER_GRAY,
                            }}
                          >
                            {context.carServiceExtensionHook.carServices.map(
                              (carService, index) => (
                                <>
                                  <ListAddonBlockButton
                                    carService={carService}
                                    isSelected={context.selectedCarAddonIndex === index}
                                    numOfSelected={carService.addons.length}
                                    onClick={() => {
                                      if (context) {
                                        context.handleOnClickEditAddon(
                                          carService.car,
                                          carService.services,
                                          carService.addons
                                        );
                                        context.setSelectedCarAddonIndex(index);
                                      }
                                    }}
                                    style={{
                                      minWidth: 240,
                                    }}
                                  />
                                </>
                              )
                            )}
                          </div>
                        ) : null}
                        {addons !== undefined && addons.length !== 0 ? (
                          <div className="flex flex-col p-4 pt-0 pb-6 gap-3">
                            <div
                              className="flex flex-row justify-between "
                              style={{
                                color: Color.BLACK_06,
                              }}
                            >
                              <span className="font-semibold">
                                {context.carServiceExtensionHook.carServices &&
                                context.carServiceExtensionHook.carServices[
                                  context.carServiceExtensionHook.selectedCarAddonIndex
                                ]?.addons.length > 0
                                  ? `${
                                      context.carServiceExtensionHook.carServices[
                                        context.carServiceExtensionHook.selectedCarAddonIndex
                                      ]?.addons.length
                                    } Selected`
                                  : "No Selected"}
                              </span>
                              <ClearAddonButton
                                onClick={() => {
                                  if (context) {
                                    context.handleOnClickClearAddonInPage();
                                  }
                                }}
                                style={{
                                  lineHeight: "21.6px",
                                  fontSize: 16,
                                }}
                              />
                            </div>
                            <div className="flex flex-row gap-4 overflow-x-scroll overflow-y-hidden pb-4 border-b-[0.5px]">
                              {context.displayedAddons.map((addon, index) => (
                                <SelectAddonBlock
                                  addon={addon}
                                  key={index.toString()}
                                  onClick={() => {
                                    context?.handleOnClickAddonBlock(addon);
                                    context.displayedAddonClickedRef.current = true;
                                  }}
                                  isSelected={context!.getIsAddonInCarServiceExtensions(addon)}
                                  isRightEnd={index % context.displayedAddons.length === 3}
                                  isLeftEnd={index % context.displayedAddons.length === 0}
                                  isFirstRow={false}
                                  isOverFlowX={true}
                                  style={{
                                    minWidth: 190,
                                  }}
                                  imageStyle={{
                                    height: 150,
                                  }}
                                />
                              ))}
                            </div>
                            <div className="flex flex-col justify-start gap-3">
                              {context.carServiceExtensionHook.carServices &&
                              context.findNonMatchingAddons(
                                context.displayedAddons,
                                context.carServiceExtensionHook.carServices[
                                  context.carServiceExtensionHook.selectedCarAddonIndex
                                ]?.addons
                              ).length > 0 ? (
                                <span className="text-black text-base">
                                  and{" "}
                                  <span className="font-semibold">
                                    {context
                                      .findNonMatchingAddons(
                                        context.displayedAddons,
                                        context.carServiceExtensionHook.carServices[
                                          context.carServiceExtensionHook.selectedCarAddonIndex
                                        ]?.addons
                                      )
                                      .map((item) => item.name)
                                      .join(", ")}
                                  </span>{" "}
                                  selected.
                                </span>
                              ) : null}
                              <SeeAllAddonsButton
                                onClick={() => {
                                  context.setIsSelectAddonModalOn(true);
                                }}
                              />
                            </div>
                          </div>
                        ) : null}
                      </div>
                    )}
                    <div className="flex p-4 border-t-[0.5px]">
                      <StepHeaderIndigoBlue title="Booking" description="" />
                    </div>
                    <div className="flex flex-col p-4 pt-0 gap-4">
                      <div
                        className="flex flex-col p-4 border rounded-lg h-full"
                        style={{
                          borderColor: Color.ATHENS_GRAY_MID,
                        }}
                      >
                        <div className="flex flex-row justify-between">
                          <span className="text-xl font-semibold text-neutral-900 mb-4">
                            Mechanical
                          </span>
                        </div>
                        <div
                          className="border-b mb-4"
                          style={{
                            borderColor: Color.ATHENS_GRAY_MID,
                          }}
                        />
                        <div className="flex flex-col">
                          <span
                            className="test-xs font-semibold"
                            style={{
                              color: Color.INDIGO_BLUE_08,
                              marginBottom: 16,
                            }}
                          >
                            Service Items
                          </span>
                          <div className="flex flex-col gap-4">
                            {context.categorySessionHook.categorySessionItems.map((item, index) => (
                              <>
                                {item.services.map((service, index) => (
                                  <ServiceItemContent
                                    pictureUri={service.pictureUri}
                                    quantity={service.cars.length}
                                    serviceName={service.name}
                                    durationInMinutes={service.duration}
                                  />
                                ))}
                              </>
                            ))}
                          </div>
                        </div>
                        <div style={{ marginBottom: 24 }} />
                        <div className="flex flex-col gap-6">
                          <div className="flex flex-col">
                            <span
                              className="test-xs font-semibold"
                              style={{
                                color: Color.INDIGO_BLUE_08,
                                marginBottom: 16,
                              }}
                            >
                              Booking Info
                            </span>
                            <div className="space-y-4">
                              <BookingInfoElement
                                imgSrc={require("../../../public/user_grey.png")}
                                text={context.nowAddress?.customerName}
                              />
                              <BookingInfoElement
                                imgSrc={require("../../../public/phone_grey.png")}
                                text={context.nowAddress?.customerPhone}
                              />
                              <BookingInfoElement
                                imgSrc={require("../../../public/address_grey.png")}
                                text={
                                  context.nowAddress ? (
                                    <span>
                                      <span>{context.nowAddress?.street1}</span>
                                      <br />
                                      {context.nowAddress.street2 ? (
                                        <>
                                          <span>{context.nowAddress?.street2}</span>
                                          <br />
                                        </>
                                      ) : null}
                                      <span>{context.nowAddress?.city}</span>
                                      <span>, {context.nowAddress?.province}</span>
                                      <span>, {context.nowAddress?.country}</span>
                                      <br />
                                      <span>{context.nowAddress?.postal}</span>
                                    </span>
                                  ) : undefined
                                }
                                style={{
                                  alignItems: "flex-start",
                                }}
                              />
                              <BookingInfoElement
                                text={
                                  context.isReservationReady && context.assignedDateTime
                                    ? context.assignedDateTime?.format("dddd MMMM DD, YYYY")
                                    : undefined
                                }
                                imgSrc={require("../../../public/calendar_grey.png")}
                              />
                              <BookingInfoElement
                                text={
                                  context.isReservationReady && context.assignedDateTime
                                    ? context.assignedDateTime?.format("hh:mm A") +
                                      " - " +
                                      moment(context.assignedDateTime)
                                        .add(context.totalDurationByMin, "minutes")
                                        .format("hh:mm A")
                                    : undefined
                                }
                                imgSrc={require("../../../public/time_grey.png")}
                              />
                            </div>
                          </div>
                          {/* <div className="flex flex-col">
                            <span
                              className="test-xs font-semibold"
                              style={{
                                color: Color.INDIGO_BLUE_08,
                                marginBottom: 16,
                              }}
                            >
                              Technician
                            </span>
                            {context.isReservationReady &&
                            context.carServiceExtensionHook.carServicesExtensions ? (
                              <TechProfile
                                profilePictureUri={
                                  context.carServiceExtensionHook.carServicesExtensions[0]
                                    ?.technician.profilePictureUri
                                }
                                name={
                                  context.carServiceExtensionHook.carServicesExtensions[0]
                                    .technician.name
                                }
                                rateOfReviews={
                                  context.carServiceExtensionHook.carServicesExtensions[0]
                                    .technician.rating
                                }
                                numOfReviews={
                                  context.carServiceExtensionHook.carServicesExtensions[0]
                                    .technician.numOfReviews
                                }
                              />
                            ) : (
                              <span
                                className="test-xs font-semibold"
                                style={{
                                  color: Color.INDIGO_BLUE_08,
                                }}
                              >
                                -
                              </span>
                            )}
                          </div> */}
                        </div>
                        <div className="border-b border-white" />
                      </div>
                    </div>
                    <div
                      className="flex flex-col items-stretch border-t-[0.5px]"
                      style={{
                        borderColor: Color.SLIVER_GRAY,
                      }}
                      ref={context.scrollTargetMobileRef}
                    >
                      <div className="flex-row items-center justify-between p-6 pb-4">
                        <StepHeaderIndigoBlue title="Checklist" description="" />
                      </div>
                      <div className="relative flex flex-col p-6 pt-0 gap-4">
                        <BookingTireStored
                          bookingTireStoredHook={context.bookingTireStoredHook}
                          isDisabled={context.isLoadingPayment}
                        />
                        <BookingHeardFrom
                          value={context.bookingHeardFromHook.bookingHeardFrom}
                          onChange={(e: any) =>
                            context.bookingHeardFromHook.handleBookingHeardFrom(e)
                          }
                          isDisabled={context.isLoadingPayment}
                          error={context.bookingHeardFromHook.error}
                        />
                        <BookingNote
                          value={context.bookingNoteHook.bookingNote}
                          onChange={(e: any) => context.bookingNoteHook.handleBookingNote(e)}
                          isDisabled={context.isLoadingPayment}
                        />
                      </div>
                    </div>
                  </NoPaddingPaper>
                </div>
                <MobileOrderSummary
                  orderSummaryHook={context.orderSummaryHook}
                  serviceCars={context.orderSummaryHook.serviceCars}
                  disabled={context.isDisabledGoback}
                  onClick={context.handleOnClickPayNow}
                  isFetchLoading={context.orderSummaryHook.serviceCars === undefined}
                  isLoading={context.isLoadingPayment}
                  buttonTitle="Pay Now"
                  GobackButton={{
                    onClick: context.handleOnClickGoBack,
                    isLoading: context.isLoadingGoback,
                    disabled: context.isDisabledGoback,
                  }}
                  fourPage={{
                    addonCars: context.orderSummaryHook.addonCars,
                    remainingTime: Time.convertSecondToMinute(context.timerHook.nowSecond),
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const ServiceItemContent = (props: {
  quantity: number;
  serviceName: string;
  pictureUri: string;
  durationInMinutes: number;
  style?: React.CSSProperties;
}) => {
  return (
    <div className="flex flex-row items-center justify-between" style={props.style}>
      <div className="flex flex-row items-center">
        <span
          className="font-medium text-sm"
          style={{
            width: 16,
            marginRight: 12,
            color: Color.INDIGO_BLUE_06,
          }}
        >
          {`${props.quantity}x`}
        </span>
        <img
          className="w-8 h-8 rounded-md"
          src={props.pictureUri}
          style={{
            marginRight: 12,
          }}
        />
        <span
          className="font-normal text-sm"
          style={{
            color: Color.INDIGO_BLUE,
          }}
        >
          {props.serviceName}
        </span>
      </div>
    </div>
  );
};

const BookingInfoElement = (props: {
  imgSrc: any;
  text: string | ReactNode | undefined;
  style?: CSSProperties;
}) => {
  return (
    <div className="flex flex-row" style={props.style}>
      <div className="flex flex-col justify-center">
        <SizeableImage src={props.imgSrc} size={20} style={{ marginRight: 12 }} />
      </div>
      <span className="text-sm font-normal" style={{ color: Color.INDIGO_BLUE }}>
        {props.text || "-"}
      </span>
    </div>
  );
};

const TechProfile = (props: {
  profilePictureUri: string | null;
  name: string;
  rateOfReviews: number | null;
  numOfReviews: number;
}) => {
  return (
    <div className="flex flex-row">
      {props.profilePictureUri ? (
        <div className="rounded-full w-14 h-14 mr-3 overflow-hidden">
          <img src={props.profilePictureUri} />
        </div>
      ) : (
        <div className="rounded-full w-14 h-14 bg-slate-600" />
      )}

      <div className="flex flex-col justify-center gap-2">
        <span className="flex flex-row text-sm fo font-semibold">{props.name}</span>
        <div className="flex flex-row">
          <div className="flex flex-row mr-2">
            <SizeableImage
              src={require("../../../public/review_star_yellow.png")}
              size={16}
              style={{
                marginRight: 6,
              }}
            />
            <span
              className="text-xs font-normal"
              style={{
                color: Color.COD_GRAY,
              }}
            >
              {props.rateOfReviews ? props.rateOfReviews : 0.0}
            </span>
          </div>
          <span
            className="text-xs font-normal"
            style={{
              color: Color.GRAY_MID,
            }}
          >
            {`(${props.numOfReviews} Review)`}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AddonInformationContainer;
