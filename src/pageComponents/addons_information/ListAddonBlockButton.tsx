import { CarService } from "../../types/CarService";
import { Color } from "../../types/Color";

interface ListAddonBlockButtonProps {
  carService: CarService;
  isSelected: boolean;
  numOfSelected: number;
  onClick: () => void;
  style?: React.CSSProperties;
}

const ListAddonBlockButton = (props: ListAddonBlockButtonProps) => {
  return (
    <button
      className="flex flex-row justify-between rounded-lg px-4 py-2"
      style={{
        border: props.isSelected ? "none" : "1px solid #0000001A",
        backgroundColor: props.isSelected ? Color.ORANGE_PEEL : Color.WHITE,
        ...props.style,
      }}
      onClick={props.onClick}
    >
      <div className="flex flex-col items-start gap-1 mr-2">
        <span
          className="flex flex-row justify-start font-normal text-sm"
          style={{
            color: props.isSelected ? Color.WHITE : Color.EBONY_06,
          }}
        >
          {props.carService.car.make}
        </span>
        <span
          className="flex flex-row font-medium text-base text-start"
          style={{
            color: props.isSelected ? Color.ATHENS_GRAY_BOTTOM : Color.EBONY,
            lineHeight: "21.6px",
            letterSpacing: "-1.6%",
          }}
        >
          {`${props.carService.car.model} ${props.carService.car.year} (${props.carService.car.color})`}
        </span>
      </div>
      <div className="flex flex-row justify-center items-center h-full">
        <span
          className="flex flex-col justify-center font-semibold w-8 h-8 rounded-lg"
          style={{
            backgroundColor: props.isSelected
              ? Color.ORANGE_PEEL_DIRTY
              : props.numOfSelected === 0
              ? Color.BORDER_GRAY
              : Color.ORANGE_PEEL,
            color: props.isSelected
              ? Color.WHITE
              : props.numOfSelected === 0
              ? Color.BLACK
              : Color.WHITE,
          }}
        >
          {props.numOfSelected}
        </span>
      </div>
    </button>
  );
};

export default ListAddonBlockButton;
