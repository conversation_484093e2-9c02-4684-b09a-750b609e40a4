import React from "react";
import { Color } from "../../types/Color";

interface ClearAddonButtonProps {
  onClick: () => void;
  style?: React.CSSProperties;
}

const ClearAddonButton = (props: ClearAddonButtonProps) => {
  return (
    <button className="flex items-center justify-center" onClick={props.onClick}>
      <span
        className="font-semibold"
        style={{
          fontSize: 14,
          lineHeight: 1.35,
          letterSpacing: "0.02em",
          textDecorationLine: "underline",
          color: Color.INDIGO_BLUE,
          ...props.style,
        }}
      >
        Clear Add-ons
      </span>
    </button>
  );
};

export default ClearAddonButton;
