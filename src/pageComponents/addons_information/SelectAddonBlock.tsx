import React, { useContext, useState } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import { addonContext } from "../../contexts/AddonContext";
import { Addon } from "../../types/Addon";
import { BusinessRule } from "../../types/BusinessRule";
import { Color } from "../../types/Color";
import FirstRowTooltip from "../index/FirstRowTooltip";
import NotFirstRowTooltip from "../index/NotFirstRowTooltip";

interface SelectServiceBlockProps {
  addon: Addon;
  onClick: () => void;
  isSelected: boolean;
  isLeftEnd: boolean;
  isRightEnd: boolean;
  isFirstRow: boolean;
  isOverFlowX?: boolean;
  style?: React.CSSProperties;
  imageStyle?: React.CSSProperties;
}

const SelectAddonBlock = (props: SelectServiceBlockProps) => {
  const context = useContext(addonContext)!;
  const [isTooltipOpen, setIsTooltipOpen] = useState<boolean>(false);

  return (
    <button
      className="rounded-lg w-full md:w-[190px] md:h-[246px]"
      style={{
        backgroundColor: props.isSelected ? Color.ALICE_BLUE : "transparent",
        border: props.isSelected ? "1.5px solid #2E90FA" : 0,
        boxShadow: props.isSelected ? "0px 12px 24px rgba(112, 144, 176, 0.2)" : "none",
        ...props.style,
      }}
      onClick={props.onClick}
    >
      <Checkbox
        isClicked={props.isSelected}
        style={{
          top: 8,
          right: 8,
        }}
      />
      <div
        className="flex flex-col overflow-hidden rounded-lg"
        style={{
          backgroundColor: Color.MINT_CREAM,
          borderBottomRightRadius: props.isSelected ? 0 : 8,
          borderBottomLeftRadius: props.isSelected ? 0 : 8,
        }}
      >
        <img
          className="object-cover w-full h-[calc((150*(100vw-32px))/288)] xxs:max-md:h-[calc((150*((100vw-48px)/2))/166)] md:h-[150px]"
          src={props.addon.pictureUri || ""}
          style={props.imageStyle}
        />
      </div>
      <div className="flex flex-col px-2 py-3">
        <div className="flex flex-row justify-between">
          <span
            className="text-start line-clamp-2 min-h-[44px]"
            style={{
              marginBottom: 8,
              fontWeight: 500,
              fontSize: 16,
              lineHeight: 1.25,
              letterSpacing: "-0.016em",
              color: Color.EBONY,
            }}
          >
            {props.addon.name}
          </span>
          <button
            className="self-baseline relative tooltip-parent"
            onClick={(event) => {
              event.stopPropagation();
              setIsTooltipOpen(!isTooltipOpen);
            }}
          >
            <SizeableImage
              src={require("../../../public/question_grey.png")}
              size={16}
              style={{
                marginTop: 3,
              }}
            />
            <FirstRowTooltip
              targetName={props.addon.name}
              targetDescription={props.addon.description}
              isLeftEnd={props.isLeftEnd}
              isRightEnd={props.isRightEnd}
              isFirstRow={props.isFirstRow}
              isOverFlowX={props.isOverFlowX}
              isTooltipOpen={isTooltipOpen}
            />
            <NotFirstRowTooltip
              targetName={props.addon.name}
              targetDescription={props.addon.description}
              isLeftEnd={props.isLeftEnd}
              isRightEnd={props.isRightEnd}
              isFirstRow={props.isFirstRow}
              isOverFlowX={props.isOverFlowX}
              isTooltipOpen={isTooltipOpen}
            />
          </button>
        </div>
        <span
          className="text-start"
          style={{
            fontWeight: 600,
            fontSize: 16,
            lineHeight: 1.25,
            letterSpacing: "-0.016em",
            color: Color.EBONY,
          }}
        >
          {`$ ${BusinessRule.calculateAddonPrice(props.addon.cost)}`}
        </span>
      </div>
    </button>
  );
};

const Checkbox = (props: { isClicked: boolean; style?: React.CSSProperties }) => {
  return (
    <div
      className="relative flex flex-row"
      style={{
        ...props.style,
      }}
    >
      <div className="absolute top-0 right-0">
        {props.isClicked ? (
          <SizeableImage src={require("../../../public/checkbox_clicked_lg.png")} size={20} />
        ) : (
          <SizeableImage src={require("../../../public/checkbox_empty_lg.png")} size={20} />
        )}
      </div>
    </div>
  );
};

export default SelectAddonBlock;
