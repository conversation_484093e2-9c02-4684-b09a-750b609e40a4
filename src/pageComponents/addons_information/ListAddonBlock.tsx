import { useEffect, useRef, useState } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import { CarService } from "../../types/CarService";
import NoCarImage from "../index/NoCarImage";
import WhiteButtonWithIcon from "../../components/atom/Buttons/WhiteButtonWithIcon";
import { Color } from "../../types/Color";
import { useTextTruncationDetector } from "../../hooks/useTextTruncationDetector";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";

interface ListAddonBlockProps {
  isSeeMoreOn: boolean;
  carService: CarService;
  onClickEditAddon: () => void;
  onClickSeeMoreService: () => void;
  onClickFolded: () => void;
}

const ListAddonBlock = (props: ListAddonBlockProps) => {
  const COLLAPSED_COUNT = 3;

  const truncateContainerRefArr = useRef<HTMLSpanElement[] | null[]>([]);
  const textTruncationDetectorHook = useTextTruncationDetector(
    truncateContainerRefArr,
    props.carService.addons
  );

  function getCollapsedAddons(addons: CarService["addons"]) {
    let extractedAddons = [];
    if (addons.length > COLLAPSED_COUNT) {
      extractedAddons = addons.slice(0, COLLAPSED_COUNT);
      return extractedAddons;
    } else {
      return addons;
    }
  }

  function getIsAddonsCollapseable(addons: CarService["addons"]) {
    let remainingAddonsCount: number = addons.length - COLLAPSED_COUNT;
    if (remainingAddonsCount <= 0) {
      return false;
    } else {
      return true;
    }
  }

  return (
    <div
      className="flex flex-col justify-between bg-white w-full rounded-lg p-5"
      style={{
        border: "1px solid #EAECF0",
        height: props.isSeeMoreOn || props.carService.addons.length === 0 ? "auto" : 317,
      }}
    >
      <div className="flex flex-col ">
        <div className="flex items-center justify-between" style={{ marginBottom: 12.5 }}>
          <div className="flex items-center ">
            <div style={{ marginRight: 12 }}>
              {props.carService.car.pictureUri !== null ? (
                <div className="flex justify-center items-center">
                  <img
                    className="object-cover object-center w-[64px] h-[64px] rounded-lg"
                    src={props.carService.car.pictureUri}
                  />
                </div>
              ) : (
                <NoCarImage size={76} style={{ margin: -6 }} />
              )}
            </div>
            <div className="flex flex-col">
              <span
                style={{
                  fontWeight: 400,
                  fontSize: 14,
                  color: Color.EBONY_06,
                  marginBottom: 4,
                }}
              >
                {props.carService.car.make}
              </span>
              <div className="flex flex-row gap-1">
                <span
                  style={{
                    fontWeight: 600,
                    fontSize: 16,
                    color: Color.EBONY,
                  }}
                >
                  {props.carService.car.model}
                </span>
                <span
                  style={{
                    fontWeight: 600,
                    fontSize: 16,
                    color: Color.EBONY,
                  }}
                >
                  {props.carService.car.year}
                </span>
              </div>
            </div>
          </div>
          {props.carService.addons !== null && props.carService.addons.length > 0 ? (
            <DropDown onClickEditAddon={props.onClickEditAddon} />
          ) : null}
        </div>
        {props.carService.addons.length !== 0 ? (
          <div
            className="min-h-[154px]"
            style={{
              borderTop: "1px solid #EAECF0",
              height: props.isSeeMoreOn ? "auto" : 154,
            }}
          >
            <div
              className="flex flex-col"
              style={{
                paddingTop: 11.5,
                paddingBottom: 12.5,
              }}
            >
              <span
                className=""
                style={{
                  fontWeight: 600,
                  fontSize: 14,
                  lineHeight: "135%",
                  color: Color.INDIGO_BLUE_08,
                }}
              >
                Add-ons
              </span>
              <div className="flex flex-col gap-4 pt-4">
                {props.isSeeMoreOn ? (
                  props.carService.addons.map((addon, index) => (
                    <div className="flex flex-row justify-between">
                      <div className="flex flex-row">
                        <span
                          className="flex flex-col justify-start mt-1"
                          style={{
                            fontWeight: 400,
                            fontSize: 14,
                            lineHeight: 1,
                            color: Color.INDIGO_BLUE_06,
                            marginRight: 12,
                          }}
                        >
                          1x
                        </span>
                        <span
                          className=""
                          style={{
                            fontWeight: 400,
                            fontSize: 14,
                            lineHeight: "150%",
                            color: Color.INDIGO_BLUE,
                          }}
                        >
                          {addon.name}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <>
                    {getCollapsedAddons(props.carService.addons)!.map((addon, index) => (
                      <div className="flex flex-row justify-between">
                        <div className="flex flex-row">
                          <span
                            className="flex flex-col justify-center"
                            style={{
                              fontWeight: 400,
                              fontSize: 14,
                              lineHeight: 1,
                              color: Color.INDIGO_BLUE_06,
                              marginRight: 12,
                            }}
                          >
                            1x
                          </span>
                          <span
                            className="truncate w-[calc(100vw-95px-16px)] md:max-lg:w-[calc(100vw-496px-16px)] lg:max-xl:w-[calc(((100vw-448px-16px)/2)-64px)] xl:w-[calc(((100vw-464px-16px)/3)-64px)] xl:max-w-[259px]"
                            style={{
                              fontWeight: 400,
                              fontSize: 14,
                              lineHeight: "150%",
                              color: Color.INDIGO_BLUE,
                            }}
                            key={index}
                            ref={(ref) => {
                              if (index < 3) {
                                truncateContainerRefArr.current[index] = ref;
                              }
                            }}
                          >
                            {`${addon.name} `}
                          </span>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div
            className="flex flex-col pt-3"
            style={{
              borderTop: "1px solid #EAECF0",
            }}
          >
            <WhiteButtonWithIcon
              title="Add-ons"
              iconSrc={require("../../../public/heart_plus.png")}
              iconSize={20}
              onClick={props.onClickEditAddon}
              style={{
                backgroundColor: Color.ACCENT,
                borderColor: "white",
              }}
              titleStyle={{
                color: "white",
              }}
            />
          </div>
        )}
      </div>
      {props.isSeeMoreOn ? (
        <button
          className="flex flex-row justify-between py-3"
          style={{
            borderTop: "1px solid #EAECF0",
          }}
          onClick={props.onClickFolded}
        >
          <span
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: "150%",
              color: Color.ORANGE_PEEL,
            }}
          >
            Fold
          </span>
          <div>
            <SizeableImage src={require("../../../public/right_arrow_accent.png")} size={20} />
          </div>
        </button>
      ) : !getIsAddonsCollapseable(props.carService.addons) &&
        textTruncationDetectorHook.isTruncated ? (
        <button
          className="flex flex-row items-center justify-between py-3"
          onClick={props.onClickSeeMoreService}
          style={{
            borderTop: "1px solid #EAECF0",
          }}
        >
          <span
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: "150%",
              color: Color.ORANGE_PEEL,
            }}
          >
            {props.carService.addons.length === 1 ? (
              <>{`See More Detail`}</>
            ) : (
              <>{`See More Details`}</>
            )}
          </span>
          <div>
            <SizeableImage src={require("../../../public/right_arrow_accent.png")} size={20} />
          </div>
        </button>
      ) : getIsAddonsCollapseable(props.carService.addons) ? (
        <button
          className="flex flex-row items-center justify-between py-3"
          onClick={props.onClickSeeMoreService}
          style={{
            borderTop: "1px solid #EAECF0",
          }}
        >
          <span
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: "150%",
              color: Color.ORANGE_PEEL,
            }}
          >
            {getIsAddonsCollapseable(props.carService.addons) ? (
              <>{`See ${props.carService.addons.length - COLLAPSED_COUNT} More Add-on`}</>
            ) : (
              <>{`See ${props.carService.addons.length - COLLAPSED_COUNT} More Add-ons`}</>
            )}
          </span>
          <div>
            <SizeableImage src={require("../../../public/right_arrow_accent.png")} size={20} />
          </div>
        </button>
      ) : null}
    </div>
  );
};

const DropDown = (props: { onClickEditAddon: () => void }) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && isOpen && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const handleToggleOptions = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative whitespace-nowrap" ref={selectRef}>
      <button className="" onClick={() => handleToggleOptions()}>
        <SizeableImage src={require("../../../public/more_dot_vertical.png")} size={20} />
      </button>
      {isOpen && (
        <div
          className="absolute top-7 right-0 flex flex-col bg-white rounded-lg py-2"
          style={{
            border: "1px solid #F2F4F7",
            boxShadow:
              "0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03)",
          }}
        >
          <button
            className="flex flex-row py-3 px-2 pr-4 hover:bg-gray-100"
            onClick={() => {
              props.onClickEditAddon();
              setIsOpen(false);
            }}
          >
            <SizeableImage src={require("../../../public/edit_pencil_Indigo_blue.png")} size={20} />
            <span
              style={{
                fontWeight: 400,
                fontSize: 14,
                color: Color.EBONY,
                marginLeft: 8,
              }}
            >
              Edit Add-ons
            </span>
          </button>
        </div>
      )}
    </div>
  );
};

export default ListAddonBlock;
