import { useEffect, useRef, useState } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import { CarServiceWithoutAddon } from "../../hooks/useCarServices";
import { useTextTruncationDetector } from "../../hooks/useTextTruncationDetector";
import { Color } from "../../types/Color";
import NoCarImage from "./NoCarImage";

interface ListCarBlockProps {
  isSeeMoreOn: boolean;
  carService: CarServiceWithoutAddon;
  onClickEditService: () => void;
  onClickRemoveCar: () => void;
  onClickSeeMoreService: () => void;
  onClickFolded: () => void;
}

const ListCarBlock = (props: ListCarBlockProps) => {
  const truncateContainerRefArr = useRef<HTMLSpanElement[] | null[]>([]);
  const textTruncationDetectorHook = useTextTruncationDetector(
    truncateContainerRefArr,
    props.carService.services
  );

  function showingUnderThreeServices(services: CarServiceWithoutAddon["services"]) {
    let tmpArr = [];
    if (services.length > 3) {
      tmpArr = services.slice(0, 3);
      return tmpArr;
    } else {
      return services;
    }
  }

  function isServicesLenUnderThree(services: CarServiceWithoutAddon["services"]) {
    let servicesLenUnderThree: number = services.length - 3;
    if (servicesLenUnderThree <= 0) {
      return true;
    } else {
      return false;
    }
  }

  return (
    <div
      className="flex flex-col justify-between bg-white w-full rounded-lg p-5"
      style={{
        border: "1px solid #EAECF0",
        height: props.isSeeMoreOn ? "auto" : 317,
      }}
    >
      <div className="flex flex-col">
        <div className="flex items-center justify-between" style={{ marginBottom: 12.5 }}>
          <div className="flex items-center ">
            <div style={{ marginRight: 12 }}>
              {props.carService.car.pictureUri !== null ? (
                <div className="flex justify-center items-center">
                  <img
                    className="object-cover object-center w-[64px] h-[64px] rounded-lg"
                    src={props.carService.car.pictureUri}
                  />
                </div>
              ) : (
                <NoCarImage size={76} style={{ margin: -6 }} />
              )}
            </div>
            <div className="flex flex-col">
              <span
                style={{
                  fontWeight: 500,
                  fontSize: 14,
                  color: Color.EBONY_06,
                  marginBottom: 4,
                }}
              >
                {props.carService.car.make}
              </span>
              <span
                style={{
                  fontWeight: 600,
                  fontSize: 16,
                  color: Color.EBONY,
                }}
              >
                {props.carService.car.year} {props.carService.car.model} :{" "}
                <span className="font-medium">({props.carService.car.color})</span>
              </span>
            </div>
          </div>
          <DropDown
            onClickEditService={props.onClickEditService}
            onClickRemoveCar={props.onClickRemoveCar}
          />
        </div>
        <div
          className="min-h-[154px]"
          style={{
            borderTop: "1px solid #EAECF0",
            height: props.isSeeMoreOn ? "auto" : 154,
          }}
        >
          <div
            className="flex flex-col"
            style={{
              paddingTop: 11.5,
              paddingBottom: 12.5,
            }}
          >
            <span
              className=""
              style={{
                fontWeight: 600,
                fontSize: 14,
                lineHeight: "135%",
                color: Color.INDIGO_BLUE_08,
              }}
            >
              Service Items
            </span>
            <div className="flex flex-col gap-4 pt-4">
              {props.isSeeMoreOn ? (
                props.carService.services.map((service, index) => (
                  <div className="flex flex-row justify-between">
                    <div className="flex flex-row">
                      <span
                        className="flex flex-col justify-start mt-1"
                        style={{
                          fontWeight: 400,
                          fontSize: 14,
                          lineHeight: 1,
                          color: Color.INDIGO_BLUE_06,
                          marginRight: 12,
                        }}
                      >
                        1x
                      </span>
                      <span
                        className="min-w-[140px]"
                        style={{
                          fontWeight: 400,
                          fontSize: 14,
                          lineHeight: "150%",
                          color: Color.INDIGO_BLUE,
                        }}
                      >
                        {service.name}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <>
                  {showingUnderThreeServices(props.carService.services).map((service, index) => (
                    <div className="flex flex-row justify-between">
                      <div className="flex flex-row">
                        <span
                          className="flex flex-col justify-center"
                          style={{
                            fontWeight: 400,
                            fontSize: 14,
                            lineHeight: 1,
                            color: Color.INDIGO_BLUE_06,
                            marginRight: 12,
                          }}
                        >
                          1x
                        </span>
                        <span
                          className="truncate w-[calc(100vw-95px-16px)] md:max-lg:w-[calc(100vw-496px-16px)] lg:max-xl:w-[calc(((100vw-448px-16px)/2)-64px)] xl:w-[calc(((100vw-464px-16px)/3)-64px)] xl:max-w-[259px]"
                          style={{
                            fontWeight: 400,
                            fontSize: 14,
                            lineHeight: "150%",
                            color: Color.INDIGO_BLUE,
                          }}
                          key={index}
                          ref={(ref) => {
                            if (index < 3) {
                              truncateContainerRefArr.current[index] = ref;
                            }
                          }}
                        >
                          {`${service.name} `}
                        </span>
                      </div>
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      {props.isSeeMoreOn ? (
        <button
          className="flex flex-row justify-between py-3"
          style={{
            borderTop: "1px solid #EAECF0",
          }}
          onClick={props.onClickFolded}
        >
          <span
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: "150%",
              color: Color.ORANGE_PEEL,
            }}
          >
            Fold
          </span>
          <div>
            <SizeableImage src={require("../../../public/right_arrow_accent.png")} size={20} />
          </div>
        </button>
      ) : isServicesLenUnderThree(props.carService.services) &&
        textTruncationDetectorHook.isTruncated ? (
        <button
          className="flex flex-row items-center justify-between py-3"
          onClick={props.onClickSeeMoreService}
          style={{
            borderTop: "1px solid #EAECF0",
          }}
        >
          <span
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: "150%",
              color: Color.ORANGE_PEEL,
            }}
          >
            {props.carService.services.length === 1 ? (
              <>{`See More Detail`}</>
            ) : (
              <>{`See More Details`}</>
            )}
          </span>
          <div>
            <SizeableImage src={require("../../../public/right_arrow_accent.png")} size={20} />
          </div>
        </button>
      ) : !isServicesLenUnderThree(props.carService.services) ? (
        <button
          className="flex flex-row items-center justify-between py-3"
          onClick={props.onClickSeeMoreService}
          style={{
            borderTop: "1px solid #EAECF0",
          }}
        >
          <span
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: "150%",
              color: Color.ORANGE_PEEL,
            }}
          >
            {props.carService.services.length - 3 === 1 ? (
              <>{`See ${props.carService.services.length - 3} More Service`}</>
            ) : (
              <>{`See ${props.carService.services.length - 3} More Services`}</>
            )}
          </span>
          <div>
            <SizeableImage src={require("../../../public/right_arrow_accent.png")} size={20} />
          </div>
        </button>
      ) : null}
    </div>
  );
};

const DropDown = (props: { onClickEditService: () => void; onClickRemoveCar: () => void }) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && isOpen && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const handleToggleOptions = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative whitespace-nowrap" ref={selectRef}>
      <button className="" onClick={() => handleToggleOptions()}>
        <SizeableImage src={require("../../../public/more_dot_vertical.png")} size={20} />
      </button>
      {isOpen && (
        <div
          className="absolute top-7 right-0 flex flex-col bg-white rounded-lg py-2 w-[239px] md:w-auto"
          style={{
            border: "1px solid #F2F4F7",
            boxShadow:
              "0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03)",
          }}
        >
          <button
            className="flex flex-row px-[14px] py-3 md:px-2 md:pr-4 hover:bg-gray-100"
            onClick={() => {
              props.onClickEditService();
              setIsOpen(false);
            }}
          >
            <SizeableImage src={require("../../../public/edit_pencil_Indigo_blue.png")} size={20} />
            <span
              style={{
                fontWeight: 400,
                fontSize: 14,
                color: Color.EBONY,
                marginLeft: 8,
              }}
            >
              Edit Service
            </span>
          </button>
          <button
            className="flex flex-row px-[14px] py-3 md:px-2 md:pr-4 hover:bg-gray-100"
            onClick={() => {
              props.onClickRemoveCar();
              setIsOpen(false);
            }}
          >
            <SizeableImage src={require("../../../public/trash_can_red.png")} size={20} />
            <span
              className=""
              style={{
                fontWeight: 400,
                fontSize: 14,
                lineHeight: "143%",
                color: Color.ALIZARIN_CRIMSON,
                marginLeft: 8,
              }}
            >
              Remove Car
            </span>
          </button>
        </div>
      )}
    </div>
  );
};

export default ListCarBlock;
