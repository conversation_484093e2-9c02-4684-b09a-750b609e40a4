import { useState } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import { Service } from "../../types";
import { BusinessRule } from "../../types/BusinessRule";
import { Color } from "../../types/Color";
import FirstRowTooltip from "./FirstRowTooltip";
import NotFirstRowTooltip from "./NotFirstRowTooltip";

interface SelectServiceBlockProps {
  service: Service;
  onClick: () => void;
  isDisabled: boolean;
  isSelected: boolean;
  isRightEnd: boolean;
  isFirstRow: boolean;
}

const SelectServiceBlock = (props: SelectServiceBlockProps) => {
  const [isTooltipOpen, setIsTooltipOpen] = useState<boolean>(false);

  return (
    <button
      className="rounded-lg w-full md:w-[190px] md:h-[246px]"
      style={{
        backgroundColor: props.isSelected ? Color.ALICE_BLUE : "transparent",
        border: props.isSelected ? "1.5px solid #2E90FA" : 0,
        boxShadow: props.isSelected ? "0px 12px 24px rgba(112, 144, 176, 0.2)" : "none",
      }}
      onClick={props.onClick}
    >
      <Checkbox
        isClicked={props.isSelected}
        isDisabled={props.service.needContact === 1}
        style={{
          top: 8,
          right: 8,
        }}
      />
      <div
        className="flex flex-col overflow-hidden rounded-lg"
        style={{
          backgroundColor: Color.MINT_CREAM,
          borderBottomRightRadius: props.isSelected ? 0 : 8,
          borderBottomLeftRadius: props.isSelected ? 0 : 8,
        }}
      >
        <img
          className="object-cover w-full h-[calc((150*(100vw-32px))/288)] xxs:max-md:h-[calc((150*((100vw-48px)/2))/166)] md:h-[150px]"
          src={props.service.servicePictureUri}
          style={{
            opacity: props.isDisabled ? 0.65 : 1,
          }}
        />
      </div>
      <div className="flex flex-col px-2 py-3">
        <div className="flex flex-row justify-between">
          <span
            className="text-start line-clamp-2 min-h-[44px]"
            style={{
              marginBottom: 8,
              fontWeight: 500,
              fontSize: 16,
              lineHeight: 1.25,
              letterSpacing: "-0.016em",
              color: props.isDisabled ? Color.DISABLED_SHADE_GRAY : Color.EBONY,
            }}
          >
            {props.service.name}
          </span>
          <button
            className="self-baseline relative tooltip-parent"
            onClick={(event) => {
              event.stopPropagation();
              setIsTooltipOpen(!isTooltipOpen);
            }}
          >
            <SizeableImage
              src={require("../../../public/question_grey.png")}
              size={16}
              style={{
                marginTop: 3,
              }}
            />
            <FirstRowTooltip
              targetName={props.service.name}
              targetDescription={props.service.description}
              isRightEnd={props.isRightEnd}
              isFirstRow={props.isFirstRow}
              isTooltipOpen={isTooltipOpen}
              isLeftEnd={false}
            />
            <NotFirstRowTooltip
              targetName={props.service.name}
              targetDescription={props.service.description}
              isRightEnd={props.isRightEnd}
              isFirstRow={props.isFirstRow}
              isTooltipOpen={isTooltipOpen}
              isLeftEnd={false}
            />
          </button>
        </div>
        <div className="flex flex-row justify-between">
          <span
            className="text-start"
            style={{
              fontWeight: 600,
              fontSize: 16,
              lineHeight: 1.25,
              letterSpacing: "-0.016em",
              color: props.isDisabled ? Color.DISABLED_SHADE_GRAY : Color.EBONY,
            }}
          >
            {props.service.needContact === 1 ? (
              <>Please Contact</>
            ) : (
              <>{`$${BusinessRule.calculatePrice(props.service.price)}`}</>
            )}
          </span>
        </div>
      </div>
    </button>
  );
};

const Checkbox = (props: {
  isClicked: boolean;
  style?: React.CSSProperties;
  isDisabled: boolean;
}) => {
  return (
    <div
      className="relative flex flex-row z-10"
      style={{
        ...props.style,
      }}
    >
      <div className="absolute top-0 right-0">
        {props.isDisabled ? null : (
          <>
            {props.isClicked ? (
              <SizeableImage src={require("../../../public/checkbox_clicked_lg.png")} size={20} />
            ) : (
              <SizeableImage src={require("../../../public/checkbox_empty_lg.png")} size={20} />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default SelectServiceBlock;
