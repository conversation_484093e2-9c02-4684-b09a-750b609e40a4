import { useContext } from "react";
import { globalContext } from "../../contexts/GlobalContext";
import { Color } from "../../types/Color";

interface FirstRowTooltipProps {
  targetName: string;
  targetDescription: string;
  isRightEnd: boolean;
  isLeftEnd: boolean;
  isFirstRow: boolean;
  isOverFlowX?: boolean;
  isTooltipOpen: boolean;
}

const FirstRowTooltip = (props: FirstRowTooltipProps) => {
  const { getResponsiveModalTooltipWidth } = useContext(globalContext)!;

  return (
    <div
      className="absolute flex flex-col tooltip z-50"
      style={{
        alignItems: props.isRightEnd ? "end" : "center",
        transform: props.isLeftEnd
          ? "translate(calc(0% - 28px), calc(0% + 4px))"
          : props.isRightEnd
          ? "translate(calc(-100% + 11px), calc(0% + 4px))"
          : "translate(-50%, calc(0% + 4px))",
        left: props.isRightEnd ? 20 : "50%",
        visibility: props.isTooltipOpen && props.isFirstRow ? "visible" : "hidden",
      }}
    >
      <div
        className="w-[0px] h-[0px] border-t-[8px] border-t-black border-l-[8px] border-l-transparent border-r-[8px] border-r-transparent"
        style={{
          marginLeft: props.isLeftEnd ? "calc(-100% + 58px)" : 0,
          marginRight: props.isRightEnd ? 15 : 0,
          transform: "rotate(180deg)",
        }}
      />
      <div
        className="flex flex-col rounded-lg p-3 pb-4"
        style={{
          backgroundColor: Color.COD_GRAY,
          width: !props.isOverFlowX
            ? props.targetDescription.length >= 250 && props.isFirstRow
              ? getResponsiveModalTooltipWidth(340, 650)
              : getResponsiveModalTooltipWidth(320, 650)
            : 440,
        }}
      >
        <span
          className="mb-1 text-white text-[14px] text-start"
          style={{
            fontWeight: 500,
            lineHeight: "150%",
          }}
        >
          {props.targetName}
        </span>
        <span
          className="text-white text-start text-[14px]"
          style={{
            fontWeight: 400,
          }}
        >
          {props.targetDescription}
        </span>
      </div>
    </div>
  );
};

export default FirstRowTooltip;
