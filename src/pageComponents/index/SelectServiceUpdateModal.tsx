import { useContext } from "react";
import ResizingFullModal from "../../components/Modal/ResizingFullModal";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import CloseButton from "../../components/atom/Buttons/CloseButton";
import { carServiceContext } from "../../contexts/CarServiceContext";
import { globalContext } from "../../contexts/GlobalContext";
import { Color } from "../../types/Color";
import { MD, XXS } from "../../values";
import SelectServiceBlock from "./SelectServiceBlock";

const SelectServiceUpdateModal = () => {
  const context = useContext(carServiceContext);
  const services = context!.remoteServices;
  const isOn = context!.isSelectServiceUpdateModalOn;

  const deviceWidth = useContext(globalContext)?.dimensionWidth;
  const WEB_MODAL_WIDTH = 650;
  const WEB_MODAL_HEIGHT = 734;

  return (
    <ResizingFullModal
      isOn={isOn}
      onOff={() => context?.setIsSelectServiceUpdateModalOn(false)}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col bg-white rounded-lg w-full h-full">
        <div className="flex flex-col flex-1 h-full items-stretch">
          <HeadLine
            title="Update Services"
            subTitle=""
            onClickCloseButton={() => context?.setIsSelectServiceUpdateModalOn(false)}
          />
          <Content>
            {services !== undefined && services.length !== 0 ? (
              <>
                <span
                  className=""
                  style={{
                    color: Color.COD_GRAY,
                    fontWeight: 600,
                    fontSize: 20,
                    lineHeight: 1,
                    marginBottom: 16,
                  }}
                >
                  {services[0].categoryName}
                </span>
                <div className="grid grid-cols-1 xxs:max-md:grid-cols-2 md:grid-cols-3 grid-flow-row gap-4 ">
                  {services.map((service, index) => (
                    <SelectServiceBlock
                      service={service}
                      key={index.toString()}
                      onClick={() => context?.handleOnClickServiceBlock(service)}
                      isSelected={context!.getIsServiceInArray(service)}
                      isDisabled={service.needContact === 1}
                      isRightEnd={
                        deviceWidth && deviceWidth > MD
                          ? index % 3 === 2
                          : deviceWidth && deviceWidth > XXS
                          ? index % 2 === 1
                          : index % 1 === 0
                      }
                      isFirstRow={
                        deviceWidth && deviceWidth > MD
                          ? index <= 2
                          : deviceWidth && deviceWidth > XXS
                          ? index <= 1
                          : index <= 0
                      }
                    />
                  ))}
                </div>
              </>
            ) : null}
          </Content>
          {/* Web */}
          <EndLine>
            <div className="flex flex-col items-center justify-center">
              <button
                className="flex items-center justify-center"
                onClick={() => {
                  if (context && context.selectedCar) {
                    context.handleOnClickClearService(context!.selectedCar);
                  }
                }}
              >
                <span
                  className=""
                  style={{
                    fontWeight: 600,
                    fontSize: 14,
                    lineHeight: 1.35,
                    letterSpacing: "0.02em",
                    textDecorationLine: "underline",
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  Clear Services
                </span>
              </button>
            </div>
            <BackgroundSmallButton
              title="Update"
              onClick={() => {
                if (context) {
                  context.handleOnClickAddService(context!.selectedServices);
                  context.setIsSelectServiceUpdateModalOn(false);
                  context.setSelectedServices([]);
                }
              }}
              backgroundColor="BLACK_PEARL"
              style={{
                width: 135,
                borderRadius: 8,
                paddingTop: 10,
                paddingBottom: 10,
                paddingLeft: 17.5,
                paddingRight: 17.5,
              }}
              disabled={context!.selectedServices.length <= 0}
            />
          </EndLine>

          {/* Mobile */}
          <EndLineMobile>
            <div className="flex flex-col items-center justify-center flex-1">
              <button
                className="flex items-center justify-center"
                onClick={() => {
                  if (context && context.selectedCar) {
                    context.handleOnClickClearService(context!.selectedCar);
                  }
                }}
              >
                <span
                  className=""
                  style={{
                    fontWeight: 600,
                    fontSize: 14,
                    lineHeight: 1.35,
                    letterSpacing: "0.02em",
                    textDecorationLine: "underline",
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  Clear Services
                </span>
              </button>
            </div>
            <div style={{ marginRight: 16 }} />
            <div className="flex-1">
              <BackgroundSmallButton
                title="Update"
                onClick={() => {
                  if (context) {
                    context.handleOnClickAddService(context!.selectedServices);
                    context.setIsSelectServiceUpdateModalOn(false);
                    context.setSelectedServices([]);
                  }
                }}
                backgroundColor="BLACK_PEARL"
                style={{
                  width: "100%",
                  borderRadius: 8,
                  paddingTop: 10,
                  paddingBottom: 10,
                  paddingLeft: 17.5,
                  paddingRight: 17.5,
                }}
                disabled={context!.selectedServices.length <= 0}
              />
            </div>
          </EndLineMobile>
        </div>
      </div>
    </ResizingFullModal>
  );
};

const Title = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        color: Color.COD_GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const SubTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 400,
        fontSize: 16,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE_06,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const HeadLine = (props: { onClickCloseButton: () => void; title: string; subTitle: string }) => {
  return (
    <div
      className="flex justify-between items-center p-4 md:px-6 md:py-4"
      style={{
        borderBottom: "0.5px solid #E7E6EB",
      }}
    >
      <div className="flex flex-col gap-1">
        <Title>{props.title}</Title>
        <SubTitle>{props.subTitle}</SubTitle>
      </div>
      <CloseButton onClick={props.onClickCloseButton} />
    </div>
  );
};

const EndLine = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="hidden md:flex flex-row justify-between px-6 py-4"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const EndLineMobile = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex md:hidden flex-row justify-between p-4 pt-3"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const Content = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-col p-4 md:px-6 md:py-4 h-full overflow-x-hidden overflow-y-scroll"
      style={props.style}
    >
      {props.children}
    </div>
  );
};

export default SelectServiceUpdateModal;
