import SizeableImage from "../../components/atom/SizeableImage";

interface NoCarImageProps {
  size: number;
  style?: React.CSSProperties;
}

const NoCarImage = (props: NoCarImageProps) => {
  return (
    <div className="flex flex-col justify-between" style={{ ...props.style }}>
      <SizeableImage src={require("../../../public/list_no_car_image.png")} size={props.size} />
    </div>
  );
};

export default NoCarImage;
