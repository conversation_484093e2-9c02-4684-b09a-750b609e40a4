import SizeableImage from "../../components/atom/SizeableImage";
import { Car } from "../../types/Car";
import { Color } from "../../types/Color";

interface CarBlockProps {
  car: Car;
  onClick: () => void;
  onClickDelete: () => void;
  isSelected: boolean;
}

const CarBlock = (props: CarBlockProps) => {
  return (
    <button
      className="rounded-lg w-full md:w-[190px] md:h-[246px]"
      style={{
        backgroundColor: props.isSelected ? Color.ALICE_BLUE : "transparent",
        border: props.isSelected ? "1.5px solid #2E90FA" : 0,
        boxShadow: props.isSelected ? "0px 12px 24px rgba(112, 144, 176, 0.2)" : "none",
      }}
      onClick={props.onClick}
    >
      <Checkbox
        isClicked={props.isSelected}
        style={{
          top: 8,
          right: 8.75,
        }}
      />
      <div
        className="flex flex-col overflow-hidden rounded-lg"
        style={{
          backgroundColor: Color.MINT_CREAM,
          borderBottomRightRadius: props.isSelected ? 0 : 8,
          borderBottomLeftRadius: props.isSelected ? 0 : 8,
        }}
      >
        {props.car.pictureUri ? (
          <img
            className="object-cover w-full h-[calc((158*(100vw-32px))/288)] xxs:max-md:h-[calc((150*((100vw-48px)/2))/166)] md:h-[150px]"
            src={props.car.pictureUri}
          />
        ) : (
          <div className="flex flex-col items-center justify-center w-full h-[calc((158*(100vw-32px))/288)] xxs:max-md:h-[calc((150*((100vw-48px)/2))/166)] md:h-[150px]">
            <NoCarImageInModal />
          </div>
        )}
      </div>
      <div className="flex flex-col px-2 py-3">
        <span
          className="text-start min-h-[10px]"
          style={{
            marginBottom: 4,
            fontWeight: 600,
            fontSize: 14,
            lineHeight: 1,
            letterSpacing: "-0.02em",
            color: Color.INDIGO_BLUE_06,
          }}
        >
          {props.car.make}
        </span>
        <span
          className="text-start line-clamp-2 min-h-[54px]"
          style={{
            fontWeight: 600,
            fontSize: 18,
            lineHeight: "150%",
            letterSpacing: "-0.016em",
            color: Color.EBONY,
          }}
        >
          {`${props.car.model} ${props.car.year}`}
        </span>
      </div>
    </button>
  );
};

const NoCarImageInModal = (props: { style?: React.CSSProperties }) => {
  return (
    <div className="flex flex-col justify-between w-full h-[80px]" style={{ ...props.style }}>
      <div className="flex flex-row justify-center">
        <SizeableImage src={require("../../../public/no_car_Image.png")} size={64} />
      </div>
      <span
        className="text-center w-full"
        style={{
          fontWeight: 600,
          fontSize: 12,
          lineHeight: "100%",
          letterSpacing: "-0.02em",
          color: Color.FIORD,
        }}
      >
        No Car Image
      </span>
    </div>
  );
};

const Checkbox = (props: { isClicked: boolean; style?: React.CSSProperties }) => {
  return (
    <div
      className="relative flex flex-row"
      style={{
        ...props.style,
      }}
    >
      <div className="absolute top-0 right-0">
        {props.isClicked ? (
          <SizeableImage src={require("../../../public/checkbox_clicked.png")} size={20} />
        ) : (
          <SizeableImage src={require("../../../public/checkbox_empty.png")} size={20} />
        )}
      </div>
    </div>
  );
};

export default CarBlock;
