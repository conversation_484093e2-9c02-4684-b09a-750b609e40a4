import Head from "next/head";
import { useContext } from "react";
import BookingStepper from "../../components/organism/BookingStepper";
import BookingStepperMobile from "../../components/organism/BookingStepperMobile";
import { carServiceContext } from "../../contexts/CarServiceContext";
import Services from "../select_service/Services";
import AddNewCarModal from "../../components/Modal/AddNewCarModal";
import SelectServiceModal from "../../components/Modal/SelectServiceModal";
import SelectServiceUpdateModal from "./SelectServiceUpdateModal";
import RemoveWarningModal from "../../components/organism/RemoveWarningModal";

const IndexContainer = () => {
  const context = useContext(carServiceContext);
  const carServiceHook = context!.carServiceHook;
  // console.log("We're in index")

  return (
    <>
      <Head>
        <title>Select Services - Wheel Easy</title>
      </Head>
      {/* Web Stepper */}
      <div className="hidden md:block">
        <BookingStepper step={0} />
      </div>
      {/* Mobile Stepper */}
      <div className="block md:hidden">
        <BookingStepperMobile step={0} />
      </div>
      <AddNewCarModal />
      <SelectServiceModal />
      <SelectServiceUpdateModal />
      <RemoveWarningModal
        isOn={context?.removingCarServiceIndex !== -1}
        onOff={() => {
          if (context) {
            context!.setRemovingCarServiceIndex(-1);
          }
        }}
        title="Remove Car"
        description="Are you sure you want to remove this car from the list? This action cannot be undone."
        iconSrc={require("../../../public/trash_can_red_icon.png")}
        iconSize={52}
        onClickDelete={() => {
          if (context) {
            carServiceHook.deleteByIndex(context.removingCarServiceIndex);
          }
        }}
      />
      <div
        className="flex-col flex-1 bg-[#F8FAFB] flex overflow-y-scroll h-[calc(var(--vh,1vh)*100-232px)] md:max-lg:h-[calc(var(--vh,1vh)*100-110px)] lg:h-[calc(var(--vh,1vh)*100-120px)]"
        ref={context!.scrollableElementRef}
      >
        <div className="flex flex-col w-full h-full self-baseline mx-auto">
          <div className="flex flex-col md:justify-center items-stretch md:items-start md:flex-row h-full md:px-10">
            <div className="md:flex md:flex-row md:w-[1360px] md:px-auto h-full md:pb-8">
              <Services />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default IndexContainer;
