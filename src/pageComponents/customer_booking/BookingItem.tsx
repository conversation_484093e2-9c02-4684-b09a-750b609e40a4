import { useMemo, useState } from "react";
import { Booking } from "../../types/Booking";
import { useRouter } from "next/router";
import Head from "next/head";
import Paper from "../../components/atom/Paper";
import Text14 from "../../components/atom/Texts/base/Text14";
import ProfilePlaceholder from "../../components/atom/ProfilePlaceholder";
import Text16 from "../../components/atom/Texts/base/Text16";
import Rating from "../../components/atom/Rating";
import AccentTextButton from "../../components/atom/Buttons/AccentTextButton";
import { CheckmarkIcon, DownArrowIcon, PencilIcon } from "../../components/atom/Icons";
import GrayText14 from "../../components/atom/Texts/GrayText14";
import moment from "moment-timezone";
import SizeableImage from "../../components/atom/SizeableImage";
import { DURATION_UNIT } from "../../values";
import { Color } from "../../types/Color";

const BookingItem = (props: {
  data: Booking;
  onClickReview: () => void;
  onClickShowReview: () => void;
  onClickReschedule: () => void;
}) => {
  const [isBookingDetailsShown, setIsBookingDetailsShown] = useState<boolean>(false);
  const router = useRouter();

  const carServicesAndAddons = useMemo(() => {
    let cars: any[] = [];

    for (let service of props.data.services) {
      for (let carInService of service.cars) {
        const existingCar = cars.find((car) => car.id === carInService.id);

        if (existingCar) {
          existingCar.services.push(service);
        } else {
          cars.push({
            id: carInService.id,
            make: carInService.make,
            model: carInService.model,
            year: carInService.year,
            color: carInService.color,
            pictureUri: carInService.pictureUri,
            services: [service],
            addons: [],
          });
        }
      }
    }

    for (let addon of props.data.addons) {
      for (let carInAddon of addon.cars) {
        const existingCar = cars.find((car) => car.id === carInAddon.id);

        if (existingCar) {
          existingCar.addons.push(addon);
        } else {
          cars.push({
            id: carInAddon.id,
            make: carInAddon.make,
            model: carInAddon.model,
            year: carInAddon.year,
            color: carInAddon.color,
            pictureUri: carInAddon.pictureUri,
            addons: [addon],
          });
        }
      }
    }

    return cars;
  }, [props.data]);

  function onClickSeeLeaveReview() {
    if (props.data.userRating !== null) {
      props.onClickShowReview();
    } else {
      props.onClickReview();
    }
  }

  const isReschedulable = useMemo(() => {
    return new Date(props.data.startDateTime) > new Date();
  }, [props.data.startDateTime]);

  return (
    <>
      <Head>
        <title>Bookings (Customer) - Wheel Easy</title>
      </Head>
      <Paper borders={isBookingDetailsShown ? "rounded-t-xl" : "rounded-xl"}>
        <div className="flex flex-col">
          <div className="flex flex-row justify-between items-center p-5 border-b">
            <Text14 className="font-medium hidden xl:block">
              Booking Number <span className="text-[#34405499] ml-3">#{props.data.bookingRef}</span>
            </Text14>
            {/* <div className="xl:hidden flex flex-row items-center">
              <div className="w-8 h-8 mr-3 rounded-full overflow-hidden flex items-center justify-center">
                {props.data.technician.profilePictureUri ? (
                  <img src={props.data.technician.profilePictureUri} />
                ) : (
                  <ProfilePlaceholder size={32} />
                )}
              </div>
              <Text16>
                {props.data.technician.firstName + " " + props.data.technician.lastName}
              </Text16>
            </div> */}
            <span />
            <div className="hidden xl:flex xl:gap-x-3">
              {props.data.userRating !== null && (
                <div className="flex flex-row gap-x-3">
                  <Text14>Review:</Text14>
                  <Rating rating={props.data.userRating!} includeText={false} />
                </div>
              )}
              <AccentTextButton
                title={props.data.userRating !== null ? "See Review" : "Leave Review"}
                onClick={onClickSeeLeaveReview}
              />
            </div>
            <Text14
              className="ml-2"
              style={{
                color: isReschedulable ? "#F89A00" : "#4B8D68",
              }}
            >
              {isReschedulable ? "UPCOMING" : "COMPLETED"}
            </Text14>
            <button
              className="xl:hidden flex self-end"
              disabled={!isReschedulable}
              onClick={props.onClickReschedule}
            >
              {isReschedulable ? <PencilIcon color="#F89A00" /> : <CheckmarkIcon color="#4B8D68" />}
              <Text14
                className="ml-2"
                style={{
                  color: isReschedulable ? "#F89A00" : "#4B8D68",
                }}
              >
                {isReschedulable ? "RESCHEDULE" : "COMPLETED"}
              </Text14>
            </button>
            {/* <Text14>{moment(props.data.startDateTime).format("YYYY-MM-DD")}</Text14>
            <span className="text-xl font-semibold" style={{ color: Color.NORMAL_BLACK }}>
              {moment(props.data.startDateTime).format("h:mm A")}
            </span>
            <span className="text-sm text-[rgba(0,0,0,0.6)]">({props.data.bookingRef})</span> */}
          </div>
          <div className="flex flex-col">
            <div className="flex border-b">
              <div style={{ flex: 2 }}>
                <div className="flex items-center xl:mx-4 xl:mb-5 xl:mt-2">
                  {/* <div className="hidden xl:flex w-10 h-10 mr-3 rounded-full overflow-hidden items-center justify-center">
                    {props.data.technician.profilePictureUri ? (
                      <img src={props.data.technician.profilePictureUri} />
                    ) : (
                      <ProfilePlaceholder size={40} />
                    )}
                  </div> */}
                  <div className="flex flex-col w-full xl:p-2 gap-y-2">
                    <div className="flex-row items-center gap-3 hidden xl:flex">
                      {/* <Text16>
                        {props.data.technician.firstName + " " + props.data.technician.lastName}
                      </Text16> */}
                      {/* <GrayText14>Mechanical</GrayText14> */}
                    </div>
                    <div className="flex flex-col xl:flex-row w-full justify-between items-center">
                      <div className="flex flex-col w-full xl:flex-row items-center gap-x-4">
                        <div className="flex flex-row w-full xl:w-fit py-3 px-5 bg-[#FAFAFA] border-b xl:border border-[rgba(0, 0, 0, 0.1)] items-center justify-center">
                          <Text16>
                            {moment(moment(props.data.startDateTime).tz("America/Toronto")).format(
                              "YYYY-MM-DD [at] hh:mm A",
                            )}{" "}
                            -{" "}
                            {moment(moment(props.data.startDateTime).tz("America/Toronto"))
                              .add(props.data.duration * DURATION_UNIT, "minutes")
                              .format("hh:mm A")}
                          </Text16>
                        </div>
                        <div className="flex flex-row items-center gap-x-2 py-3 px-5">
                          <SizeableImage
                            src={require("../../../public/location_black.png")}
                            size={16}
                          />
                          {props.data.customer.address ? (
                            <Text16 style={{ flex: 1, fontWeight: 400 }}>
                              {props.data.customer.address?.street1 +
                                " " +
                                (props.data.customer.address.street2
                                  ? props.data.customer.address?.street2 + " "
                                  : "") +
                                props.data.customer.address.city +
                                ", " +
                                props.data.customer.address.province +
                                ", " +
                                props.data.customer.address.country +
                                " " +
                                props.data.customer.address.postal}
                            </Text16>
                          ) : null}
                        </div>
                      </div>
                      <button
                        className="hidden xl:flex"
                        disabled={!isReschedulable}
                        onClick={props.onClickReschedule}
                      >
                        {isReschedulable ? (
                          <PencilIcon color="#F89A00" />
                        ) : (
                          <CheckmarkIcon color="#4B8D68" />
                        )}
                        <Text14
                          className={`${
                            isReschedulable ? "text-[#F89A00]" : "text-[#4B8D68]"
                          } ml-2 font-medium`}
                        >
                          {isReschedulable ? "RESCHEDULE" : "COMPLETED"}
                        </Text14>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center">
              <div style={{ flex: 3.5 }}>
                <div className="flex items-center justify-between mx-4 my-2 p-2">
                  <div
                    className="flex flex-row cursor-pointer items-center"
                    onClick={() => setIsBookingDetailsShown((currentState) => !currentState)}
                  >
                    <Text16>Booking Details</Text16>
                    <div className={`${isBookingDetailsShown ? "rotate-180" : ""} ml-1`}>
                      <DownArrowIcon />
                    </div>
                  </div>
                  <div className="flex items-center justify-between gap-x-5">
                    <div className="hidden xl:flex">
                      <AccentTextButton
                        title={
                          props.data.receiptUrl !== null
                            ? "See Receipt"
                            : "See Receipt (Please contact)"
                        }
                        onClick={() => router.push(props.data.receiptUrl!)}
                        disabled={props.data.receiptUrl === null}
                      />
                    </div>
                    <Text16>${(props.data.amountTotal / 100).toFixed(2)}</Text16>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Paper>
      {isBookingDetailsShown && (
        <Paper borders="rounded-b-xl drop-shadow-none" background={"bg-[#FAFAFA]"}>
          <Text14 className="font-medium xl:hidden bg-white p-4 flex justify-between">
            Booking Number <span className="text-[#34405499] ml-3">#{props.data.bookingRef}</span>
          </Text14>
          <div style={{ flex: 2 }}>
            <div className="flex flex-col mx-4">
              {carServicesAndAddons.map((item: any, index: number) => (
                <div className="flex flex-col " key={index}>
                  <div
                    className="flex flex-col p-4 -mx-4"
                    key={index}
                    style={{
                      backgroundColor: index % 2 !== 0 ? Color.WHITE : undefined,
                    }}
                  >
                    <div className="flex items-center">
                      <div className="hidden xl:flex w-[60px] h-[60px] xl:items-center xl:justify-center rounded-md mr-4 border overflow-hidden">
                        {item.pictureUri ? (
                          <img className="w-[60px] h-[60px] object-cover" src={item.pictureUri} />
                        ) : (
                          <SizeableImage
                            size={32}
                            src={require("../../../public/car_placeholder.png")}
                            style={{ opacity: 0.5 }}
                          />
                        )}
                      </div>
                      <div>
                        <div className="flex flex-row items-center text-[#34405499] mb-3 xl:mb-0">
                          <div className="xl:hidden mr-3 w-[32px] h-[32px] flex items-center justify-center rounded-md border overflow-hidden">
                            {item.pictureUri ? (
                              <img className="w-[32px] h-[32px] " src={item.pictureUri} />
                            ) : (
                              <SizeableImage
                                size={20}
                                src={require("../../../public/car_placeholder.png")}
                                style={{ opacity: 0.5 }}
                              />
                            )}
                          </div>
                          <span className="text-sm mr-1">{item.year}</span>
                          <div className="flex items-center space-x-1">
                            <span className="text-sm">{item.make}</span>
                            <span className="text-sm">{item.model}</span>
                            <span className="text-sm">({item.color})</span>
                          </div>
                        </div>
                        <span
                          className="font-medium mb-2"
                          style={{
                            fontSize: 15,
                          }}
                        >
                          {item.services.map(
                            (service: any, serviceIndex: number) =>
                              service.name +
                              (serviceIndex !== item.services.length - 1 ? ", " : ""),
                          )}
                        </span>
                        <div className="flex flex-row">
                          {item.addons.length > 0 ? (
                            <span className="text-sm text-[rgba(0,0,0,0.6)] mr-1">
                              Add-ons:{" "}
                              {item.addons.map(
                                (addon: any, addonIndex: number) =>
                                  addon.name + (addonIndex !== item.addons.length - 1 ? ", " : ""),
                              )}
                            </span>
                          ) : (
                            <span className="text-sm text-[rgba(0,0,0,0.6)]">No Addons</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="xl:hidden w-full flex justify-center bg-white py-4 rounded-b-xl border-t border-t-gray">
            <AccentTextButton
              title={
                props.data.receiptUrl !== null ? "See Receipt" : "See Receipt (Please contact)"
              }
              onClick={() => router.push(props.data.receiptUrl!)}
              disabled={props.data.receiptUrl === null}
            />
          </div>
        </Paper>
      )}
      <div className="xl:hidden mt-2 w-full flex justify-between">
        {props.data.userRating !== null && (
          <div className="flex flex-row gap-x-3">
            <Text14>Review:</Text14>
            <Rating rating={props.data.userRating!} includeText={false} />
          </div>
        )}
        <AccentTextButton
          title={props.data.userRating === null ? "Leave Review" : "See Review"}
          onClick={onClickSeeLeaveReview}
        />
      </div>
    </>
  );
};

export default BookingItem;
