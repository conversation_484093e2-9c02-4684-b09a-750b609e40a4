import ResizingModal from "../../components/Modal/ResizingModal";
import CloseButton from "../../components/atom/Buttons/CloseButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import SuccessIcon from "../../components/atom/Icons/SuccessIcon";
import WarningIcon from "../../components/atom/Icons/WarningIcon";
import { Color } from "../../types/Color";

interface AlertModalProps {
  isOn: boolean;
  onOff: () => void;
  title: string;
  description: React.ReactNode;
  isButton?: {
    buttonTitle: string;
    onClickButton: () => void;
    isLoading?: boolean;
  };
  modalSize?: {
    mobile: {
      width: number;
      height: number;
    };
    web: {
      width: number;
      height: number;
    };
  };
  type?: "SUCCESS" | "WARNING";
}

const AlertModal = (props: AlertModalProps) => {
  function handleCloseModal() {
    props.onOff();
  }

  return (
    <ResizingModal
      isOn={props.isOn}
      onOff={handleCloseModal}
      modalSize={{
        mobile: {
          width: props.modalSize ? props.modalSize.mobile.width : 375,
          height: props.modalSize ? props.modalSize.mobile.height : 274,
        },
        web: {
          width: props.modalSize ? props.modalSize.web.width : 375,
          height: props.modalSize ? props.modalSize.web.height : 274,
        },
      }}
    >
      <div className="relative flex flex-col justify-between bg-white rounded-lg h-full w-full max-w-[375px] pl-6 py-6 pr-2 overflow-y-scroll">
        <div className="absolute top-5 right-4">
          <CloseButton onClick={handleCloseModal} />
        </div>
        <div className="flex flex-col flex-1 items-stretch">
          {props.type === "SUCCESS" ? <SuccessIcon /> : <WarningIcon />}
          <div
            className="flex flex-col pt-5"
            style={{
              paddingBottom: props.isButton ? 32 : 16,
            }}
          >
            <span
              className=""
              style={{
                marginBottom: 8,
                fontWeight: 500,
                fontSize: 18,
                lineHeight: "156%",
                color: Color.EBONY,
              }}
            >
              {props.title}
            </span>
            <span
              className=""
              style={{
                fontWeight: 400,
                fontSize: 14,
                lineHeight: "20px",
                color: Color.PALE_SKY,
              }}
            >
              {props.description}
            </span>
          </div>
        </div>
        <div className="flex flex-row">
          {props.isButton ? (
            <WhiteButton
              title={props.isButton.buttonTitle}
              onClick={props.isButton.onClickButton}
              style={{
                flex: 1,
                justifyContent: "center",
                height: 40,
                paddingTop: 10,
                paddingBottom: 10,
              }}
              isLoading={props.isButton.isLoading}
            />
          ) : null}
        </div>
      </div>
    </ResizingModal>
  );
};

export default AlertModal;
