interface EmbedTutorialVideoProps {}

const EmbedTutorialVideoWeb = (props: EmbedTutorialVideoProps) => {
  return (
    <div
      style={{
        position: "relative",
        paddingBottom: "56.25%",
        height: 0,
      }}
    >
      <iframe
        src="https://www.loom.com/embed/1c715359094e4fa2b5525d931e1bb35f?sid=db884911-0ddc-4e36-bdbf-0c3c97282798"
        webkitAllowFullScreen
        mozallowfullscreen
        allowFullScreen
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          border: "none",
          borderRadius: "1rem",
        }}
      ></iframe>
    </div>
  );
};

export default EmbedTutorialVideoWeb;
