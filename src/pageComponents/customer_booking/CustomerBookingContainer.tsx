import { useContext, useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import SelectDateTechnicianModal from "../../components/Modal/SelectDateTechnicianModal";
import Spinner from "../../components/atom/Spinner";
import CustomerLayout from "../../components/organism/CustomerLayout";
import CustomerReviewModal from "../../components/organism/CustomerReviewModal";
import PreviewReviewModal from "../../components/organism/PreviewReviewModal";
import { customerBookingContext } from "../../contexts/CustomerBookingContext";
import { apiGetBookingsCount, apiListBookings } from "../../functions/api/booking";
import BookingItem from "../../pageComponents/customer_booking/BookingItem";
import { Booking } from "../../types/Booking";
import { Color } from "../../types/Color";
import AlertModal from "./AlertModal";
import ErrorAlertModal from "../booking_date/ErrorAlertModal";
import EmbedTutorialVideoBlock from "./EmbedTutorialVideoBlock";

const selectStyles = {
  paddingRight: 30,
  appearance: "none",
  WebkitAppearance: "none",
  MozAppearance: "none",
  backgroundImage:
    "url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23000000%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E')",
  backgroundRepeat: "no-repeat, repeat",
  backgroundPosition: "right .8em top 50%, 0 0",
  backgroundSize: ".65em auto, 100%",
};

const CustomerBookingContainer = () => {
  const context = useContext(customerBookingContext)!;

  const user = useSelector((state: RootState) => state.auth.user);
  const [leaveReviewModalIndex, setLeaveReviewModalIndex] = useState<number>(-1);
  const [showReviewModalIndex, setShowReviewModalIndex] = useState<number>(-1);

  const [isFetchLoading, setIsFetchLoading] = useState<boolean>(true);
  const [order, setOrder] = useState<"ASC" | "DESC">("DESC");
  const [sortBy, setSortBy] = useState<"APPOINTMENT_TIME" | "CREATED_AT">("CREATED_AT");
  const [viewType, setViewType] = useState<"UPCOMING" | "PAST">("UPCOMING");
  const [isBookingsEmpty, setIsBookingsEmpty] = useState<boolean>(false);

  useEffect(() => {
    if (context.shouldFetchBookings && !context.alertModalHook.isOn) {
      fetchBookings();
    }
  }, [context.shouldFetchBookings, context.alertModalHook.isOn]);

  useEffect(() => {
    fetchBookings();
  }, [order, sortBy, viewType, context.paginationHook.currentPage]);

  useEffect(() => {
    fetchBookingsCount();
  }, []);

  async function fetchBookingsCount() {
    const bookingsCount: number = await apiGetBookingsCount();

    if (bookingsCount > 0) {
      setIsBookingsEmpty(false);
    } else {
      setIsBookingsEmpty(true);
    }

    console.log("fetchBookingsCount = " + bookingsCount);
  }

  async function fetchBookings() {
    setIsFetchLoading(true);
    const result = await apiListBookings(
      context.paginationHook.currentPage,
      context.paginationHook.numOfRowsPerPage,
      sortBy,
      order,
      viewType
    );
    context.setBookings(result);
    if (result[0]) {
      context.paginationHook.setTotalCount(result[0].pagination.totalCount);
    } else {
      context.paginationHook.resetTotalCount();
    }

    console.log("context.paginationHook.totalCount = " + context.paginationHook.totalCount);
    context.setShouldFetchBookings(false);
    setIsFetchLoading(false);
  }

  const timeText = useMemo(() => {
    const curHr = new Date().getHours();
    if (curHr < 12) {
      return "Good morning";
    } else if (curHr < 18) {
      return "Good afternoon";
    } else {
      return "Good evening";
    }
  }, []);

  function handleSortOption(order: "ASC" | "DESC", sortBy: "APPOINTMENT_TIME" | "CREATED_AT") {
    setOrder(order);
    setSortBy(sortBy);
    context.paginationHook.resetCurrentPage();
  }

  function handleViewType(viewType: "UPCOMING" | "PAST") {
    setViewType(viewType);
  }

  const setSort = (value: string) => {
    if (value === "1") {
      handleSortOption("DESC", "CREATED_AT");
    } else if (value === "2") {
      handleSortOption("ASC", "CREATED_AT");
    } else if (value === "3") {
      handleSortOption("DESC", "APPOINTMENT_TIME");
    } else if (value === "4") {
      handleSortOption("ASC", "APPOINTMENT_TIME");
    }
  };

  return (
    <CustomerLayout>
      <CustomerReviewModal
        isOn={leaveReviewModalIndex !== -1}
        onOff={() => setLeaveReviewModalIndex(-1)}
        bookingId={context.bookings[leaveReviewModalIndex]?.id}
        serviceNames={context.bookings[leaveReviewModalIndex]?.services.map((item) => item.name)}
        fetchBooking={() => fetchBookings()}
      />
      <PreviewReviewModal
        isOn={showReviewModalIndex !== -1}
        onOff={() => setShowReviewModalIndex(-1)}
        selectedTechnician={
          showReviewModalIndex === -1
            ? null
            : (context.bookings[showReviewModalIndex].technician as any)
        }
      />
      <SelectDateTechnicianModal
        isOn={context.isSelctDateTechnicianModalOn}
        onOff={context.handleOnOffSelectDateTechnicianModal}
        showFirstStep={context.showFirstStep}
        showSecondStep={context.showSecondStep}
        modalTitle={context.selectDateTechnicianModalTitle}
        modalButtonText={context.selectDateTechnicianModalButtonText}
        selectedMonth={context.selectedMonth}
        selectedDate={context.selectedDate}
        selectedTime={context.selectedTime}
        getIsDateAvailable={context.getIsDateAvailable}
        onClickDate={context.onClickDate}
        onClickTime={context.onClickTime}
        isWideModal={context.isWideModal}
        isLoading={context.isLoading}
        isConfirmButtonLoading={context.isConfirmButtonLoading}
        isConfirmButtonDisabled={context.isConfirmButtonDisabled}
        onClickConfirmButton={context.onClickConfirmButton}
        showBackButton={context.showBackButton}
        onClickBackButton={context.onClickBackButton}
        showCalendarPicker={context.showCalendarPicker}
        weekDays={context.weekDays}
        generatedWeeks={context.generatedWeeks}
        isPreviousCalendarMonthDisabled={context.isPreviousCalendarMonthDisabled}
        addMonthToSelectedTime={context.addMonthToSelectedTime}
        minusMonthToSelectedTime={context.minusMonthToSelectedTime}
        availableHours={context.availableHours}
      />
      <AlertModal
        isOn={context.isAlertModalOn}
        onOff={
          context.alertModalHook.type === "WARNING"
            ? () => context.setIsAlertModalOn(false)
            : () => {
                context.alertModalHook.setIsOn(false);
                context.alertModalHook.handleSetType();
              }
        }
        title={context.alertModalHook.title}
        description={context.alertModalHook.description}
        isButton={
          context.alertModalHook.isButton
            ? {
                buttonTitle: context.alertModalHook.buttonTitle,
                onClickButton:
                  context.alertModalHook.type === "WARNING"
                    ? () => {
                        context.setBookingToEdit(
                          context.bookings as unknown as Booking[],
                          context.selectedBookingIndex
                        );
                        context.setIsAlertModalOn(false);
                      }
                    : () => {},
              }
            : undefined
        }
        type={context.alertModalHook.type}
        modalSize={{
          mobile: {
            width: 375,
            height: context.alertModalHook.type === "SUCCESS" ? 196 : 360,
          },
          web: {
            width: 375,
            height: context.alertModalHook.type === "SUCCESS" ? 196 : 360,
          },
        }}
      />
      <ErrorAlertModal
        isOn={context.errorMessageFromServer !== null}
        onOff={context.handleOnOffErrorAlertModal}
        title="Server Error"
        description={context.errorMessageFromServer!}
      />
      <div className="flex flex-col items-center flex-1 pt-12 w-full overflow-y-scroll h-[calc(var(--vh,1vh)*100-54px)] lg:h-[calc(var(--vh,1vh)*100-64px)] pb-14">
        <div className="w-full">
          <h6
            className="text-sm mb-1 text-start px-4"
            style={{
              color: Color.BLACK_06,
            }}
          >
            {new Date().toLocaleDateString("en-us", {
              month: "long",
              day: "numeric",
              weekday: "long",
              hour: "2-digit",
              minute: "2-digit",
            })}
          </h6>
          <div className="w-full flex flex-col xl:flex-row justify-between mt-5 border-b xl:border-none mb-4 xl:mb-0">
            <h2 className="text-3xl font-semibold mb-8 px-4">
              {timeText}, {user?.firstName}
            </h2>
            <select
              style={selectStyles as React.CSSProperties}
              onChange={(e) => {
                setSort(e.target.value);
              }}
              className="border-[0.5px] border-black opacity-60 mx-4 mb-4 xl:mb-0 rounded-full h-fit w-fit text-sm py-2 pl-3 pr-6 font-medium"
              disabled={isBookingsEmpty}
            >
              <option value={"1"}>Sort by: Booking Created - New to Old</option>
              <option value={"2"}>Sort by: Booking Created - Old to New</option>
              <option value={"3"}>Sort by: Appointment Time - New to Old</option>
              <option value={"4"}>Sort by: Appointment Time - Old to New</option>
            </select>
          </div>
          <div className="flex items-center px-4">
            <button
              style={{
                paddingBottom: 20,
                marginRight: 20,
                color: viewType === "UPCOMING" ? Color.ACCENT : undefined,
                fontWeight: viewType === "UPCOMING" ? "600" : undefined,
              }}
              className="text-sm"
              onClick={() => handleViewType("UPCOMING")}
              disabled={isBookingsEmpty}
            >
              Upcoming Appointments
            </button>
            <button
              style={{
                paddingBottom: 20,
                color: viewType === "PAST" ? Color.ACCENT : undefined,
                fontWeight: viewType === "PAST" ? "600" : undefined,
              }}
              className="text-sm"
              onClick={() => handleViewType("PAST")}
              disabled={isBookingsEmpty}
            >
              Past Appointments
            </button>
          </div>
          <div className="w-full px-4">
            {isFetchLoading ? (
              <div className="w-full h-full flex flex-col justify-center items-center min-h-[200px] md:min-h-[300px]">
                <div className="flex flex-col items-center">
                  <Spinner />
                </div>
              </div>
            ) : (
              <div className="flex flex-col justify-between">
                {isBookingsEmpty ? (
                  <>
                    {/* web */}
                    <div className="hidden md:flex w-full">
                      <EmbedTutorialVideoBlock
                        isLoading={context.isGoBookingLoading}
                        onClickStart={async () => {
                          context.setIsGoBookingLoading(true);
                          await context.router.push("/");
                          context.setIsGoBookingLoading(false);
                        }}
                        responsive="WEB"
                      />
                    </div>
                    {/* mobile */}
                    <div className="flex md:hidden justify-center w-full h-full">
                      <EmbedTutorialVideoBlock
                        isLoading={context.isGoBookingLoading}
                        onClickStart={async () => {
                          context.setIsGoBookingLoading(true);
                          await context.router.push("/");
                          context.setIsGoBookingLoading(false);
                        }}
                        responsive="MOBILE"
                      />
                    </div>
                  </>
                ) : (
                  <>
                    {context.bookings
                      // .filter((booking) => new Date(booking.startDateTime) > new Date())
                      .map((item, index) => (
                        <div key={index} className="w-full mb-6">
                          <BookingItem
                            data={item as unknown as Booking}
                            onClickReview={() => setLeaveReviewModalIndex(index)}
                            onClickShowReview={() => setShowReviewModalIndex(index)}
                            onClickReschedule={() => {
                              context.setSelectedBookingIndex(index);
                              context.alertModalHook.setIsOn(true);
                              context.initAlertModal(context.alertModalHook.type);
                            }}
                          />
                        </div>
                      ))}
                    {context.paginationHook.totalCount === null ? null : (
                      <div className="flex flex-row justify-end gap-4 items-center">
                        <button
                          className="rounded-lg py-2 px-2"
                          style={{
                            backgroundColor: context.paginationHook.getIsPrevPageDisabled()
                              ? Color.DISABLED_GRAY
                              : Color.MINT_CREAM,
                            color: context.paginationHook.getIsPrevPageDisabled()
                              ? Color.EBONY_04
                              : Color.PALE_SKY,
                          }}
                          disabled={context.paginationHook.getIsPrevPageDisabled()}
                          onClick={context.paginationHook.onClickPrevPage}
                        >
                          prev
                        </button>
                        <span>{`${context.paginationHook.currentPage} / ${context.paginationHook.totalCount}`}</span>
                        <button
                          className="rounded-lg py-2 px-2"
                          style={{
                            backgroundColor: context.paginationHook.getIsNextPageDisabled()
                              ? Color.DISABLED_GRAY
                              : Color.MINT_CREAM,
                            color: context.paginationHook.getIsNextPageDisabled()
                              ? Color.EBONY_04
                              : Color.PALE_SKY,
                          }}
                          disabled={context.paginationHook.getIsNextPageDisabled()}
                          onClick={context.paginationHook.onClickNextPage}
                        >
                          next
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}

            {/* <Title18 style={{ paddingBottom: 20 }}>Past Appointments</Title18>
          {bookings
            .filter((booking) => new Date(booking.startDateTime) <= new Date())
            .map((item, index) => (
              <div key={index} className="w-full mb-6">
                <BookingItem
                  data={item as unknown as Booking}
                  onClickReview={() => setLeaveReviewModalIndex(index)}
                  onClickShowReview={() => setShowReviewModalIndex(index)}
                />
              </div>
            ))} */}
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CustomerBookingContainer;
