import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import { Color } from "../../types/Color";
import EmbedTutorialVideoMobile from "./EmbedTutorialVideoMobile";
import EmbedTutorialVideoWeb from "./EmbedTutorialVideoWeb";

interface EmbedTutorialVideoBlockProps {
  isLoading: boolean;
  onClickStart: () => void;
  responsive: "WEB" | "MOBILE";
}

const EmbedTutorialVideoBlock = (props: EmbedTutorialVideoBlockProps) => {
  return (
    <div className="flex flex-col p-4 md:p-6 border rounded-lg w-full bg-white gap-5 md:gap-6 xl:gap-8">
      <div className="flex flex-col gap-4">
        <span
          className="text-xl md:text-2xl font-semibold"
          style={{
            color: Color.INDIGO_BLUE,
          }}
        >
          You haven't made a booking yet.
        </span>
        <span
          className="text-base md:text-lg font-medium"
          style={{
            color: Color.GRAY_MID,
          }}
        >
          Booking for the first time? <br />
          Don't worry! The tutorial video below will help you.
        </span>
      </div>
      <div className="border-b" />
      <div className="flex flex-col gap-4 xl:px-[calc((100vw-250px-32px-16px-48px)/8)]">
        <span
          className="text-lg md:text-xl font-medium"
          style={{
            color: Color.INDIGO_BLUE,
          }}
        >
          Book Appointment Tutorial
        </span>
        {props.responsive === "WEB" ? (
          <EmbedTutorialVideoWeb />
        ) : props.responsive === "MOBILE" ? (
          <EmbedTutorialVideoMobile />
        ) : null}
      </div>
      <div className="flex flex-row justify-center mb-4">
        {/* web */}
        <div className="hidden md:flex">
          <BackgroundSmallButton
            backgroundColor="ACCENT"
            title="Start Booking"
            onClick={props.onClickStart}
            style={{
              width: 180,
              height: 45,
              borderRadius: "0.5rem",
              fontSize: 16,
            }}
            isLoading={props.isLoading}
          />
        </div>
        {/* mobile */}
        <div className="flex md:hidden w-full">
          <BackgroundSmallButton
            backgroundColor="ACCENT"
            title="Start Booking"
            onClick={props.onClickStart}
            style={{
              width: "100%",
              height: 45,
              borderRadius: "0.5rem",
              fontSize: 16,
            }}
            isLoading={props.isLoading}
          />
        </div>
      </div>
    </div>
  );
};

export default EmbedTutorialVideoBlock;
