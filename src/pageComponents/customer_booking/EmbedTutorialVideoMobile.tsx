import { useRef } from "react";

interface EmbedTutorialVideoMobileProps {}

const EmbedTutorialVideoMobile = (props: EmbedTutorialVideoMobileProps) => {
  const ref = useRef<HTMLIFrameElement>(null);
  return (
    <div className="flex justify-center border border-gray-200 rounded-2xl overflow-hidden">
      <iframe
        src="https://app.screencast.com/5yEiSHYTaWc7k/e"
        allowFullScreen
        style={{
          width: "100%",
          height:
            ref && ref.current ? `calc(((1080 * ${ref.current!.clientWidth}px) / 582))` : "100%",
          border: "none",
          overflow: "hidden",
        }}
        ref={ref}
      ></iframe>
    </div>
  );
};

export default EmbedTutorialVideoMobile;
