import { useState } from "react";
import ResizingModal from "../../components/Modal/ResizingModal";
import AuthInput from "../../components/atom/AuthInput";
import CloseButton from "../../components/atom/Buttons/CloseButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import WarningIcon from "../../components/atom/Icons/WarningIcon";
import { Color } from "../../types/Color";

interface CaptchaFailedModalProps {
  isOn: boolean;
  onOff: () => void;
  succeeded: () => void;
}

const CaptchaFailedModal = (props: CaptchaFailedModalProps) => {
  const [code, setCode] = useState<string>("");
  const [isError, setIsError] = useState<boolean>(false);
  function handleCloseModal() {
    props.onOff();
  }

  function handleSubmit() {
    if (code.trim() == "092134") {
      props.succeeded();
    } else {
      setIsError(true);
    }
  }

  return (
    <ResizingModal
      isOn={props.isOn}
      onOff={handleCloseModal}
      modalSize={{
        mobile: {
          width: 400,
          height: 312,
        },
        web: {
          width: 400,
          height: 312,
        },
      }}
    >
      <div className="relative flex flex-col bg-white rounded-lg h-full w-full p-6 pr-2 overflow-y-scroll">
        <div className="absolute top-5 right-4">
          <CloseButton onClick={handleCloseModal} />
        </div>
        <div className="flex flex-col flex-1 h-full items-stretch">
          <WarningIcon />
          <div className="flex flex-col justify-between h-full">
            <div className="flex flex-col pt-5 gap-5">
              <span
                className=""
                style={{
                  fontWeight: 500,
                  fontSize: 18,
                  lineHeight: "156%",
                  color: Color.EBONY,
                }}
              >
                Please type the follow code: 092134
              </span>
              <AuthInput
                placeholder="Input Provided Code..."
                value={code}
                onChange={(event) => setCode(event.target.value)}
                error={isError}
                errorMessage="Please Make Sure Code Matches"
              />
              <span
                className="flex flex-col justify-between gap-2.5"
                style={{
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "20px",
                  color: Color.PALE_SKY,
                }}
              ></span>
              <WhiteButton
                title={"Submit"}
                onClick={handleSubmit}
                style={{
                  flex: 1,
                  justifyContent: "center",
                  height: 40,
                  paddingTop: 10,
                  paddingBottom: 10,
                }}
                isLoading={false}
              />
            </div>
            <div className="flex flex-row"></div>
          </div>
        </div>
      </div>
    </ResizingModal>
  );
};

export default CaptchaFailedModal;
