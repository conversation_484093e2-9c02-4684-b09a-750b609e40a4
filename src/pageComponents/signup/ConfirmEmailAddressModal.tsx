import ResizingModal from "../../components/Modal/ResizingModal";
import CloseButton from "../../components/atom/Buttons/CloseButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import WarningIcon from "../../components/atom/Icons/WarningIcon";
import { Color } from "../../types/Color";

interface ConfirmEmailAddressModalProps {
  isOn: boolean;
  onOff: () => void;
  title: string;
  emailAddress: string;
  buttonTitle: string;
  onClickButton: () => void;
  isLoading?: boolean;
}

const ConfirmEmailAddressModal = (props: ConfirmEmailAddressModalProps) => {
  function handleCloseModal() {
    props.onOff();
  }

  return (
    <ResizingModal
      isOn={props.isOn}
      onOff={handleCloseModal}
      modalSize={{
        mobile: {
          width: 400,
          height: 312,
        },
        web: {
          width: 400,
          height: 312,
        },
      }}
    >
      <div className="relative flex flex-col bg-white rounded-lg h-full w-full p-6 pr-2 overflow-y-scroll">
        <div className="absolute top-5 right-4">
          <CloseButton onClick={handleCloseModal} />
        </div>
        <div className="flex flex-col flex-1 h-full items-stretch">
          <WarningIcon />
          <div className="flex flex-col justify-between h-full">
            <div className="flex flex-col pt-5 gap-5">
              <span
                className=""
                style={{
                  fontWeight: 500,
                  fontSize: 18,
                  lineHeight: "156%",
                  color: Color.EBONY,
                }}
              >
                {props.title}
              </span>
              <span
                className="flex flex-col justify-between gap-2.5"
                style={{
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "20px",
                  color: Color.PALE_SKY,
                }}
              >
                <span
                  className="border-2 rounded-lg px-4 py-2 outline outline-2 outline-red-200"
                  style={{
                    borderColor: "#FEE4E2",
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  {props.emailAddress}
                </span>
                Please confirm that the email address above is correct.
              </span>
            </div>
            <div className="flex flex-row">
              <WhiteButton
                title={props.buttonTitle}
                onClick={props.onClickButton}
                style={{
                  flex: 1,
                  justifyContent: "center",
                  height: 40,
                  paddingTop: 10,
                  paddingBottom: 10,
                }}
                isLoading={props.isLoading}
              />
            </div>
          </div>
        </div>
      </div>
    </ResizingModal>
  );
};

export default ConfirmEmailAddressModal;
