import SizeableImage from "../../components/atom/SizeableImage";
import { SearchedTechnician } from "../../hooks/useTechnicianSearch";
import { Color } from "../../types/Color";

interface TechnicianItemProps {
  data: SearchedTechnician;
  isSelected?: boolean;
  onClick?: () => void;
  onClickReview?: () => void;
}

const TechnicianItem = (props: TechnicianItemProps) => {
  if (props.data === undefined) return <span></span>;
  return (
    <div
      className="flex flex-col py-2 px-3 border rounded-lg"
      style={{
        minWidth: 326,
        backgroundColor: props.isSelected ? Color.SOAP_STONE : Color.NORMAL_WHITE,
        borderColor: props.isSelected ? Color.ACCENT : Color.NORMAL_WHITE,
        marginBottom: props.onClick !== undefined ? 20 : 0,
      }}
    >
      <button onClick={props.onClick} className="flex items-center justify-between">
        <div className="flex items-center">
          {props.data.profilePictureUri ? (
            <img
              src={props.data?.profilePictureUri}
              style={{
                width: 56,
                height: 56,
                marginRight: 16,
              }}
              className="rounded-full"
            />
          ) : (
            <div
              className="flex items-center justify-center rounded-md"
              style={{
                width: 56,
                height: 56,
                marginRight: 16,
                backgroundColor: Color.GRAY,
              }}
            />
          )}
          <div className="flex flex-col text-left">
            <dt style={{ fontSize: 14, fontWeight: "500" }}>{props.data.name}</dt>
            <div className="flex items-center">
              {props.data.rating !== null ? (
                <>
                  <SizeableImage
                    size={16}
                    src={require("../../../public/rating_star.png")}
                    style={{ marginRight: 6 }}
                  />
                  <span
                    style={{
                      fontSize: 13,
                      marginRight: 8,
                    }}
                  >
                    {props.data.rating}
                  </span>
                </>
              ) : null}
              <button
                onClick={(event) => {
                  event.stopPropagation();
                  if (props.onClickReview !== undefined) props.onClickReview();
                }}
                style={{
                  color: Color.GRAY,
                  fontSize: 12,
                }}
              >
                ({props.data.numOfReviews} Review{props.data.numOfReviews > 1 ? "s" : ""})
              </button>
            </div>
          </div>
        </div>
        {props.onClick !== undefined ? (
          <div
            className="flex items-center justify-center rounded-full"
            style={{
              boxShadow: props.isSelected
                ? "0px 0px 0px 1px " + Color.ACCENT
                : "0px 0px 0px 1px #d4d4d4",
              width: 20,
              height: 20,
              backgroundColor: props.isSelected ? Color.MAGNOLIA : Color.WHITE,
            }}
          >
            {props.isSelected ? (
              <div
                className="rounded-full"
                style={{ width: 8, height: 8, backgroundColor: Color.ACCENT }}
              />
            ) : null}
          </div>
        ) : null}
      </button>
    </div>
  );
};

export default TechnicianItem;
