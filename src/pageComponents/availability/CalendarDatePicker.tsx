import { Moment } from "moment";
import { useContext } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import Text16 from "../../components/atom/Texts/base/Text16";
import Title18 from "../../components/atom/Texts/base/Title18";
import { calendarContext } from "../../contexts/CalendarContext";
import { globalContext } from "../../contexts/GlobalContext";
import { Color } from "../../types/Color";

type CalendarDatePickerProps = {
  showFirstStep: boolean;
  showSecondStep: boolean;
  getIsDateAvailable: (day: Moment) => boolean;
  getDateBackgroundColor: (day: Moment | null) => string;
  getDateFontColor: (day: Moment | null) => string;
  selectedMonth: Moment | null;
  isPreviousCalendarMonthDisabled: boolean;
  minusMonthToSelectedTime: () => void;
  addMonthToSelectedTime: () => void;
  weekDays: string[];
  generatedWeeks: (Moment | null)[][];
  availableHours: string[];
  selectedTime: Moment | null;
  selectedDate: Moment | null;
  onClickDate: (date: number) => void;
  onClickTime: (timeOption: string) => void;
};

const CalendarDatePicker = ({
  showFirstStep,
  showSecondStep,
  getIsDateAvailable,
  getDateBackgroundColor,
  getDateFontColor,
  selectedMonth,
  isPreviousCalendarMonthDisabled,
  minusMonthToSelectedTime,
  addMonthToSelectedTime,
  weekDays,
  generatedWeeks,
  availableHours,
  selectedTime,
  selectedDate,
  onClickDate,
  onClickTime,
}: CalendarDatePickerProps) => {
  const gContext = useContext(globalContext);
  const deviceWidth = gContext?.dimensionWidth;
  const isMobile = gContext?.isMobile;

  return (
    <div className="flex flex-col flex-1 items-center h-full">
      <div className="flex flex-1 justify-between overflow-hidden w-full">
        {showFirstStep ? (
          <div className="flex flex-col">
            <div
              className="flex justify-between items-center p-4 md:px-6 pb-0"
              style={{
                paddingTop: isMobile ? "calc(20*(100vw - 32px)/400)" : 24,
                paddingBottom: isMobile ? "calc(20*(100vw - 32px)/400)" : 24,
              }}
            >
              <Title18
                style={{
                  fontSize: isMobile ? "calc(18*(100vw - 32px)/400)" : 18,
                }}
              >
                {selectedMonth?.format("MMMM YYYY")}
              </Title18>
              <div className="flex">
                <button
                  disabled={isPreviousCalendarMonthDisabled}
                  onClick={minusMonthToSelectedTime}
                  className="flex flex-col items-center justify-center"
                  style={{
                    width: isMobile ? "calc(32*(100vw - 32px)/400)" : 32,
                    height: isMobile ? "calc(32*(100vw - 32px)/400)" : 32,
                  }}
                >
                  <SizeableImage
                    size={isMobile ? "calc(24*(100vw - 32px)/400)" : 24}
                    src={
                      isPreviousCalendarMonthDisabled
                        ? require("../../../public/calendar_left_disabled.png")
                        : require("../../../public/calendar_left_enabled.png")
                    }
                  />
                </button>
                <button
                  onClick={addMonthToSelectedTime}
                  className="flex flex-col items-center justify-center"
                  style={{
                    width: isMobile ? "calc(32*(100vw - 32px)/400)" : 32,
                    height: isMobile ? "calc(32*(100vw - 32px)/400)" : 32,
                  }}
                >
                  <SizeableImage
                    size={isMobile ? "calc(24*(100vw - 32px)/400)" : 24}
                    src={require("../../../public/calendar_right_enabled.png")}
                  />
                </button>
              </div>
            </div>
            <table
              className="flex flex-col items-center px-4 pt-0 md:p-6 md:pt-0 overflow-y-scroll"
              style={{
                paddingBottom: isMobile ? "calc(24*(100vw - 32px)/400)" : 24,
              }}
            >
              <thead
                className=""
                style={{
                  marginBottom: isMobile ? "calc(12*(100vw - 32px)/400)" : 12,
                }}
              >
                <tr className="flex space-x-[calc(20*(100vw-32px)/400)] md:space-x-5">
                  {weekDays.map((weekday, index) => (
                    <th
                      className=""
                      key={weekday}
                      style={{
                        width: isMobile ? "calc(40*(100vw - 32px)/400)" : 40,
                        height: isMobile ? "calc(20*(100vw - 32px)/400)" : 20,
                        fontSize: isMobile ? "calc(12*((40*(100vw - 32px)/400))/40)" : 12,
                        fontWeight: "400",
                        color: "gray",
                      }}
                    >
                      {weekday.toUpperCase()}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {generatedWeeks.map((week, index) => (
                  <tr
                    key={index}
                    className="flex flex-1 space-x-[calc(20*(100vw-32px)/400)] md:space-x-5"
                    style={{
                      marginBottom:
                        index === generatedWeeks.length - 1
                          ? 0
                          : isMobile
                          ? "calc(16*(100vw - 32px)/400)"
                          : 16,
                    }}
                  >
                    {week.map((day, index) => (
                      <button
                        disabled={!day || !getIsDateAvailable(day)}
                        key={index}
                        className="flex rounded-full items-center justify-center"
                        style={{
                          width: isMobile ? "calc(40*(100vw - 32px)/400)" : 40,
                          height: isMobile ? "calc(40*(100vw - 32px)/400)" : 40,
                          fontSize: isMobile ? "calc(14*((40*(100vw - 32px)/400))/40)" : 14,
                          fontWeight: "500",
                          color: getDateFontColor(day),
                          backgroundColor: getDateBackgroundColor(day),
                        }}
                        onClick={() => {
                          onClickDate(day!.date());
                        }}
                      >
                        {day?.date()}
                      </button>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : null}
        {showSecondStep ? (
          <div
            className="flex flex-col items-start px-6 py-4 w-full"
            style={{
              minWidth: isMobile ? deviceWidth : 200,
            }}
          >
            {isMobile ? (
              <div className="flex flex-col gap-2 mb-6 w-full">
                <span
                  className="flex flex-row justify-center font-semibold text-lg"
                  style={{
                    color: Color.BLACK_PEARL,
                  }}
                >
                  {selectedDate?.format("dddd")}
                </span>
                <span className="flex flex-row justify-center text-base font-normal">
                  {selectedDate?.format("MMMM D, YYYY")}
                </span>
              </div>
            ) : (
              <div className="flex flex-col mb-3">
                <Text16>{selectedDate?.format("dddd, MMMM D")}</Text16>
              </div>
            )}
            <div className="flex flex-col items-start overflow-y-scroll w-full">
              <div className="space-y-3 w-full">
                {availableHours.map((time, index) => (
                  <button
                    className="w-full py-3 rounded-md"
                    style={{
                      backgroundColor:
                        selectedTime?.format("h:mm A") === time
                          ? Color.ACCENT
                          : Color.BACKGROUND_ACCENT,
                      color: selectedTime?.format("h:mm A") === time ? "white" : Color.ACCENT,
                    }}
                    onClick={() => {
                      onClickTime(time);
                    }}
                  >
                    {time}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
};

export default CalendarDatePicker;
