import React, { useContext } from "react";
import Text16 from "../../components/atom/Texts/base/Text16";
import { calendarContext } from "../../contexts/CalendarContext";
import TechnicianItem from "./TechnicianItem";

interface TechnicianListProps {
  onClickReviewButton: (index: number) => void;
}

const TechnicianList = (props: TechnicianListProps) => {
  const context = useContext(calendarContext)!;

  return (
    <div
      className="flex flex-col flex-1 justify-between"
      style={{
        width: "100%",
        height: "100%",
      }}
    >
      <div className="flex flex-col flex-1 p-4 overflow-y-scroll">
        <Text16 style={{ fontWeight: "600", marginBottom: 12 }}>Available Technicians</Text16>
        <div className="flex flex-col self-stretch" style={{ height: 320 }}>
          {context.technicianSearchHook.technicians?.map((item, index) => (
            <TechnicianItem
              data={item}
              isSelected={context.technicianSearchHook.selectedTechIndex === index}
              onClick={() => context.technicianSearchHook.setSelectedTechIndex(index)}
              onClickReview={() => props.onClickReviewButton(index)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default TechnicianList;
