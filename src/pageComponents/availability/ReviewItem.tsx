import moment from "moment";
import Rating from "../../components/atom/Rating";
import GrayText14 from "../../components/atom/Texts/GrayText14";
import Text14 from "../../components/atom/Texts/base/Text14";
import { Review } from "../../types/Review";
import { Color } from "../../types/Color";

interface ReviewItemProps {
  item: Review;
}

const ReviewItem = ({ item }: ReviewItemProps) => {
  return (
    <>
      <div className="flex flex-col py-6">
        <div className="flex flex-row justify-between mb-5">
          <div className="flex flex-row ">
            <div className="flex flex-col items-center justify-center mr-3">
              {item.customerInfo.profilePictureUri ? (
                <img
                  src={item.customerInfo.profilePictureUri}
                  style={{
                    width: 40,
                    height: 40,
                  }}
                  className="rounded-full"
                />
              ) : (
                <div
                  className="flex items-center justify-center rounded-full"
                  style={{
                    width: 40,
                    height: 40,
                    backgroundColor: Color.BLACK_02,
                  }}
                />
              )}
            </div>
            <div className="flex flex-col justify-center">
              <Text14 style={{ marginBottom: 2, fontWeight: 500 }}>
                {item.customerInfo.firstName + " " + item.customerInfo.lastName}
              </Text14>
              <GrayText14 style={{ fontWeight: 400 }}>
                {skipItemThenCreateMore(item.serviceNames)}
              </GrayText14>
            </div>
          </div>
          <div className="flex flex-col justify-center">
            <div
              className="flex items-center space-x-1 justify-end"
              style={{
                marginBottom: 6,
              }}
            >
              <Rating rating={item.rating} includeText={false} />
            </div>
            <GrayText14 style={{ fontWeight: 400, textAlign: "right" }}>
              {changeDateForm(item.createdDateTime)}
            </GrayText14>
          </div>
        </div>
        <Text14 style={{ fontWeight: 400 }}>{item.description}</Text14>
      </div>
      <div className="border-b" />
    </>
  );
};

function changeDateForm(date: string) {
  let dateForm = moment(date);

  return dateForm.format("MMM DD, YYYY [at] hh:mm A");
}

function skipItemThenCreateMore(target: string) {
  let newArr: string[] = target.split(", ");
  if (newArr.length <= 1) {
    return target;
  } else {
    return `${newArr[0]} and ${newArr.length - 1} more services`;
  }
}

export default ReviewItem;
