import { Color } from "../../types/Color";

interface ContentLabelProps {
  children: React.ReactNode;
  isContentEssential?: boolean;
  style?: React.CSSProperties;
}

const ContentLabel = (props: ContentLabelProps) => {
  return (
    <span
      className="flex flex-row justify-start"
      style={{
        fontWeight: 500,
        fontSize: 14,
        lineHeight: "143%",
        color: Color.BLACK_PEARL,
        ...props.style,
      }}
    >
      {props.children}
      {props.isContentEssential ? (
        <span
          style={{
            color: Color.CORAL_RED,
          }}
        >
          *
        </span>
      ) : null}
    </span>
  );
};

export default ContentLabel;
