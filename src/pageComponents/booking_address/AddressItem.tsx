import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import NoBorderButtonWithIcon from "../../components/atom/Buttons/NoBorderButtonWithIcon";
import SizeableImage from "../../components/atom/SizeableImage";
import IndigoBlueText16 from "../../components/atom/Texts/IndigoBlueText16";
import { NewAddress } from "../../services/AddressService";
import { Address } from "../../types/Address";
import { Color } from "../../types/Color";
import { PhoneNumber } from "../../types/PhoneNumber";
import MarkedSelection from "./MarkedSelection";

interface AddressItemProps {
  isSelected: boolean;
  title: string;
  name: string;
  address: NewAddress;
  phone: string;
  onClickChangeAddress: () => void;
  onClickRemoveAddress: () => void;
  onClickUseAddress: () => void;
}

const AddressItem = (props: AddressItemProps) => {
  return (
    <div
      className="rounded-lg p-4"
      style={{
        border: props.isSelected ? "1px solid #FCA311" : "1px solid #E6E6E8",
        backgroundColor: props.isSelected ? Color.SOAP_STONE : Color.WHITE,
      }}
    >
      <div className="flex flex-col">
        <div className="flex flex-row justify-between mb-6">
          <div className="flex">
            <div className="flex flex-col justify-center">
              <span
                className="mr-3"
                style={{
                  color: Color.INDIGO_BLUE_08,
                  fontWeight: 600,
                  fontSize: 16,
                  lineHeight: 1,
                }}
              >
                {Address.getPresentedTitle(props.title)}
              </span>
            </div>
            <MarkedSelection
              title="Primary"
              style={{
                visibility: props.isSelected ? "visible" : "hidden",
              }}
            />
          </div>
          <div className="hidden lg:flex">
            <NoBorderButtonWithIcon
              title="Change Address"
              iconSize={18}
              iconSrc={require("../../../public/pencil_indigo_blue.png")}
              onClick={() => props.onClickChangeAddress()}
              style={{ paddingRight: 16, borderRightWidth: 1, borderRightColor: Color.SLIVER_GRAY }}
            />
            <NoBorderButtonWithIcon
              title="Remove Address"
              iconSize={18}
              iconSrc={require("../../../public/garbage_red.png")}
              onClick={props.onClickRemoveAddress}
              isTextRed={true}
              style={{ marginLeft: 16 }}
            />
          </div>
          {/* Icons without text */}
          <div className="flex lg:hidden">
            <button
              onClick={props.onClickChangeAddress}
              style={{ paddingRight: 16, borderRightWidth: 1, borderRightColor: Color.SLIVER_GRAY }}
            >
              <SizeableImage src={require("../../../public/pencil_indigo_blue.png")} size={18} />
            </button>
            <button onClick={props.onClickRemoveAddress} style={{ marginLeft: 16 }}>
              <SizeableImage
                src={require("../../../public/trash_can_alizarin_crimson.png")}
                size={18}
              />
            </button>
          </div>
        </div>
        <div className="flex flex-col space-y-6 justify-between lg:flex-row lg:space-y-0">
          <div className="flex flex-col items-start">
            <span
              style={{
                color: Color.BLACK_PEARL,
                fontWeight: 600,
                fontSize: 16,
                lineHeight: 1,
                marginBottom: 16,
              }}
            >
              {props.name}
            </span>
            <IndigoBlueText16>
              {PhoneNumber.getPhoneNumberFromAPI(props.phone).getFormattedFullPhoneNumber()}
            </IndigoBlueText16>
            <div className="flex flex-col space-y-2 mt-4">
              <IndigoBlueText16>{`${props.address.street1}`}</IndigoBlueText16>
              {props.address.street2 ? (
                <IndigoBlueText16>{props.address.street2}</IndigoBlueText16>
              ) : null}
              <IndigoBlueText16>{`${props.address.city}, ${props.address.province}, ${props.address.country}`}</IndigoBlueText16>
              <IndigoBlueText16>{props.address.postal}</IndigoBlueText16>
            </div>
          </div>
          <div className="flex flex-col justify-center">
            {props.isSelected ? (
              <SizeableImage size={24} src={require("../../../public/check_orange.png")} />
            ) : (
              <BackgroundSmallButton
                title="Use This Address"
                onClick={() => props.onClickUseAddress()}
                backgroundColor="ACCENT"
                isLoading={false}
                style={{
                  borderRadius: 8,
                  paddingLeft: 20,
                  paddingRight: 20,
                  paddingTop: 8,
                  paddingBottom: 8,
                  fontWeight: 500,
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressItem;
