import InputForm from "./InputForm";
import SelectForm from "./SelectForm";

interface ClassifyContentProps {
  value: string;
  options: string[];
  placeholder: string;
  type: "INPUT" | "SELECT";
  onChange: (target: string) => void;
  disabled?: boolean;
  style?: React.CSSProperties;
}

const ClassifyContent = (props: ClassifyContentProps) => {
  return (
    <>
      {props.type === "INPUT" ? (
        <InputForm
          placeholder={props.placeholder}
          style={props.style}
          onChange={(target) => props.onChange(target)}
          disabled={props.disabled}
          value={props.value}
        />
      ) : props.type === "SELECT" ? (
        <SelectForm
          placeholder={props.placeholder}
          style={props.style}
          onChange={(target) => props.onChange(target)}
          value={props.value}
          options={props.options}
        />
      ) : null}
    </>
  );
};

export default ClassifyContent;
