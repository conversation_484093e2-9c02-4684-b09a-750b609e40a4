import { useRouter } from "next/router";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import SizeableImage from "../../components/atom/SizeableImage";
import ClassifyContent from "./ClassifyContent";
import ContentLabel from "./ContentLabel";
import FormOfContent from "./FormOfContent";
import PhoneSelect from "./PhoneSelect";
import { Color } from "../../types/Color";

interface AddressInputBlockProps {
  addressFormHook: any;
  onCreate: () => void;
  isMobile?: boolean;
}

const AddressInputBlock = (props: AddressInputBlockProps) => {
  const router = useRouter();

  function handleOnClickGoBack() {
    router.push("/");
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-row ">
        <FormOfContent>
          <ContentLabel isContentEssential>Customer Name</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.name}
            options={[]}
            placeholder="Input your name here..."
            type="INPUT"
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setName(target);
              }
            }}
          />
        </FormOfContent>
        <div style={{ width: 16 }} />
        <FormOfContent>
          <ContentLabel isContentEssential>Phone Number</ContentLabel>
          <div className="flex flex-row">
            <PhoneSelect
              value={props.addressFormHook.phoneRegion}
              imageSrc={require("../../../public/flag_US.png")}
              regionCode="1"
              style={{
                flex: "1 1 0%",
              }}
              onChange={(target) => {
                if (target !== " ") {
                  props.addressFormHook.setPhoneRegion(target);
                }
              }}
            />
            <div style={{ width: 6 }} />
            <ClassifyContent
              value={props.addressFormHook.phone}
              options={[]}
              placeholder="Your phone number..."
              type="INPUT"
              style={{
                flex: "1 1 0%",
              }}
              onChange={(target) => {
                if (target !== " ") {
                  props.addressFormHook.setPhone(target);
                }
              }}
            />
          </div>
        </FormOfContent>
      </div>
      <div>
        <FormOfContent>
          <ContentLabel isContentEssential>Address Line 1</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.street1}
            options={[]}
            placeholder="Input your address line 1 here..."
            type="INPUT"
            style={{
              flex: "1 1 0%",
            }}
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setStreet1(target);
              }
            }}
          />
          {/* Auto Complete Address */}
          {/* <AddressAutocomplete
            isOn={true}
            initialValue={props.addressFormHook.street1}
            populateForms={props.addressFormHook.populateForms}
          /> */}
        </FormOfContent>
      </div>
      <div>
        <FormOfContent>
          <ContentLabel>Address Line 2</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.street2}
            options={[]}
            placeholder="Input your address line 2 here..."
            type="INPUT"
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setStreet2(target);
              }
            }}
          />
        </FormOfContent>
      </div>
      <div className="flex flex-row gap-6">
        <FormOfContent>
          <ContentLabel isContentEssential>City</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.city}
            options={[]}
            placeholder="Input your state"
            type="INPUT"
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setCity(target);
              }
            }}
          />
        </FormOfContent>
        <FormOfContent>
          <ContentLabel isContentEssential>Province</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.province}
            options={[]}
            placeholder="Input your state"
            type="INPUT"
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setProvince(target);
              }
            }}
          />
        </FormOfContent>
        <FormOfContent>
          <ContentLabel isContentEssential>Country</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.country}
            options={[]}
            placeholder="Input your state"
            type="INPUT"
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setCountry(target);
              }
            }}
          />
        </FormOfContent>
        <FormOfContent style={{ width: 150, flex: "none" }}>
          <ContentLabel isContentEssential>Postal Code</ContentLabel>
          <ClassifyContent
            value={props.addressFormHook.postal}
            options={[]}
            placeholder="0"
            type="INPUT"
            onChange={(target) => {
              if (target !== " ") {
                props.addressFormHook.setPostal(target);
              }
            }}
          />
        </FormOfContent>
      </div>
      {!props.isMobile ? (
        <div className="relative pt-6">
          <div
            className="absolute top-0 -left-6 w-[1012px]"
            style={{
              border: "0.5px solid #E6E6E8",
              borderTop: 0,
              borderLeft: 0,
              borderRight: 0,
            }}
          />
          <EndLine
            onClickGoBack={handleOnClickGoBack}
            onClickSaveAddress={async () => {
              await props.addressFormHook.SaveAddress();
              await props.onCreate();
            }}
            isDisabledContinue={!props.addressFormHook.isContinuable}
          />
        </div>
      ) : null}
    </div>
  );
};

const EndLine = (props: {
  onClickGoBack: () => void;
  onClickSaveAddress: () => void;
  isDisabledContinue: boolean;
}) => {
  return (
    <div className="flex justify-between">
      <div className="flex flex-row items-center">
        <div className="flex rounded-full bg-[#F5F8FF] mr-2 w-9 h-9">
          <div className="flex flex-col justify-center p-2">
            <SizeableImage size={20} src={require("../../../public/clock.png")} />
          </div>
        </div>
        <span
          className="flex flex-col justify-center"
          style={{
            fontWeight: 600,
            fontSize: 14,
            lineHeight: "143%",
            color: Color.INDIGO_BLUE,
          }}
        >
          Please wait around 30 minutes for your technician to arrive at the service location
        </span>
      </div>
      <div className="flex flex-row">
        <WhiteButton
          title="Go Back"
          onClick={props.onClickGoBack}
          style={{
            borderRadius: 8,
            paddingTop: 10,
            paddingBottom: 10,
            paddingLeft: 40,
            paddingRight: 40,
          }}
        />
        <div style={{ marginRight: 16 }} />
        <BackgroundSmallButton
          title="Save Address"
          onClick={props.onClickSaveAddress}
          backgroundColor="DARK_BLUE"
          style={{
            borderRadius: 8,
            paddingTop: 10,
            paddingBottom: 10,
            paddingLeft: 40,
            paddingRight: 40,
          }}
          disabled={props.isDisabledContinue}
        />
      </div>
    </div>
  );
};

export default AddressInputBlock;
