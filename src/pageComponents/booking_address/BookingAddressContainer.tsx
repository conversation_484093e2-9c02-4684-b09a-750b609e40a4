import Head from "next/head";
import { useContext } from "react";
import AddressModalGroup from "../../components/AddressModalGroup";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import WhiteButtonWithIcon from "../../components/atom/Buttons/WhiteButtonWithIcon";
import NoPaddingPaper from "../../components/atom/NoPaddingPaper";
import Spinner from "../../components/atom/Spinner";
import StepHeaderIndigoBlue from "../../components/molecule/StepHeaderIndigoBlue";
import BookingStepper from "../../components/organism/BookingStepper";
import BookingStepperMobile from "../../components/organism/BookingStepperMobile";
import MobileOrderSummary from "../../components/organism/MobileOrderSummary";
import OrderSummary from "../../components/organism/OrderSummary";
import { addressContext } from "../../contexts/AddressContext";
import { globalContext } from "../../contexts/GlobalContext";
import { getOrdinalNumber } from "../../functions/util";
import { useOrderSummarySticky } from "../../hooks/useOrderSummarySticky";
import { LG } from "../../values";
import AddressItem from "./AddressItem";
import AddressItemMobile from "./AddressItemMobile";
import EmptyAddressItem from "./EmptyAddressItem";
import AlertModal from "../customer_booking/AlertModal";

const BookingAddressContainer = () => {
  const context = useContext(addressContext)!;
  const orderSummaryHook = context!.orderSummaryHook;
  const orderSummaryStickyHook = useOrderSummarySticky(
    context!.scrollableElementRef,
    orderSummaryHook.dynamicHeightRef
  );
  const gContext = useContext(globalContext)!;
  const deviceWidth = gContext.dimensionWidth;
  return (
    <>
      <AddressModalGroup user={context.user} addressHook={context.addressHook} />
      <AlertModal
        isOn={context.alertModalHook.isOn}
        onOff={() => context.alertModalHook.setIsOn(false)}
        title={context.alertModalHook.title}
        description={context.alertModalHook.description}
        modalSize={{
          mobile: {
            width: 375,
            height: 258,
          },
          web: {
            width: 375,
            height: 258,
          },
        }}
        type={context.alertModalHook.type}
      />
      <Head>
        <title>Customer Information - Wheel Easy</title>
      </Head>
      {/* Web Stepper */}
      <div className="hidden md:block">
        <BookingStepper step={1} />
      </div>
      {/* Mobile Stepper */}
      <div className="block md:hidden">
        <BookingStepperMobile step={1} />
      </div>

      <div
        className="flex flex-col bg-[#F8FAFB] overflow-y-scroll h-[calc(var(--vh,1vh)*100-232px)] md:max-lg:h-[calc(var(--vh,1vh)*100-110px)] lg:h-[calc(var(--vh,1vh)*100-120px)]"
        ref={context!.scrollableElementRef}
      >
        {/* Web */}
        <div className="hidden flex-row items-center justify-center md:flex px-10 h-full">
          <div className="flex flex-row pt-10 h-full w-[1360px]">
            <div className="flex flex-col pb-10 mr-6 h-full flex-1">
              <NoPaddingPaper borders="rounded-br-none md:rounded-t-xl rounded-t-none rounded-bl-none md:rounded-bl-xl">
                <div className="flex flex-col">
                  <div className="flex-row items-center hidden md:flex justify-between px-6 pt-6">
                    <div className="mr-8">
                      <StepHeaderIndigoBlue title="Service Location" description="" />
                    </div>
                    <WhiteButtonWithIcon
                      title="Add New Address"
                      iconSrc={require("../../../public/vector_indigo_blue.png")}
                      iconSize={20}
                      onClick={() => {
                        if (context) {
                          context.openAddModal();
                        }
                      }}
                    />
                  </div>
                  <div
                    className="relative flex flex-col p-6 pt-4 border-b border-b-gray gap-4"
                    style={{
                      paddingBottom: 24,
                    }}
                  >
                    {!context.isAddressFetchLoading ? (
                      <>
                        {context.isAddressExisted ? (
                          context.addresses?.map((address, index) => (
                            <AddressItem
                              isSelected={context.isAddressItemSelected(index)}
                              title={getOrdinalNumber(index + 1)}
                              name={address.customerName || "N/A"}
                              address={address}
                              phone={address.customerPhone || "N/A"}
                              onClickChangeAddress={() => {
                                if (context) {
                                  context.openEditModal(index);
                                }
                              }}
                              onClickRemoveAddress={() => {
                                if (context) {
                                  context.openDeleteModal(index);
                                }
                              }}
                              onClickUseAddress={() => {
                                if (context) {
                                  context.selectAddress(index);
                                }
                              }}
                            />
                          ))
                        ) : (
                          <EmptyAddressItem />
                        )}
                      </>
                    ) : (
                      <div className="w-full h-full flex flex-col justify-center items-center bg-[#ffffff] min-h-[400px]">
                        <div className="flex flex-col items-center">
                          <Spinner />
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="p-6">
                    <EndLine
                      onClickGoBack={context.goBackPage}
                      onClickContinue={context.proceedToNextPage}
                      isDisabledContinue={!context.isUsingAddress}
                      isLoadingContinue={context.isProceedingToNextPage}
                      isLoadingGoBack={context.isLoadingGoback}
                      deviceWidth={deviceWidth}
                    />
                  </div>
                </div>
              </NoPaddingPaper>
              <div style={{ paddingBottom: 40 }} />
            </div>
            <div
              className=""
              style={{
                position: orderSummaryStickyHook.getPosition(),
                top: orderSummaryStickyHook.getTop(),
                right: orderSummaryStickyHook.getRight(),
              }}
            >
              <OrderSummary
                orderSummaryHook={context.orderSummaryHook}
                serviceCars={context.orderSummaryHook.serviceCars}
                isFetchLoading={context.isFetchLoadingOrderSummary}
              />
            </div>
            <div
              className="min-w-[280px]"
              style={{
                display: orderSummaryStickyHook.getDisplay(),
              }}
            />
          </div>
        </div>

        {/* Mobile */}
        <div className="flex flex-col w-full h-full md:hidden justify-between">
          <div className="flex flex-col pb-14">
            <NoPaddingPaper borders="rounded-br-none md:rounded-t-xl rounded-t-none rounded-bl-none md:rounded-bl-xl xl:w-[1014px] flex flex-col">
              <div className="flex-row items-center justify-between px-5 pt-5">
                <StepHeaderIndigoBlue title="Service Location" description="" />
              </div>
              <div
                className="flex flex-col p-5 gap-4"
                style={{
                  paddingBottom: context.isAddressExisted ? 24 : 16,
                }}
              >
                {!context.isAddressFetchLoading ? (
                  <>
                    {context.isAddressExisted ? (
                      context.addresses?.map((address, index) => (
                        <AddressItemMobile
                          isSelected={context.isAddressItemSelected(index)}
                          title={getOrdinalNumber(index + 1)}
                          name={address.customerName || "N/A"}
                          address={address}
                          phone={address.customerPhone || "N/A"}
                          onClickChangeAddress={() => {
                            if (context) {
                              context.openEditModal(index);
                            }
                          }}
                          onClickRemoveAddress={() => {
                            if (context) {
                              context.openDeleteModal(index);
                            }
                          }}
                          onClickUseAddress={() => {
                            if (context) {
                              context.selectAddress(index);
                              console.log(context.addressHook.selectedAddressIndex);
                            }
                          }}
                        />
                      ))
                    ) : (
                      <EmptyAddressItem />
                    )}
                  </>
                ) : (
                  <div className="w-full h-full flex flex-col justify-center items-center bg-[#ffffff] min-h-[200px]">
                    <div className="flex flex-col items-center">
                      <Spinner />
                    </div>
                  </div>
                )}
                <WhiteButtonWithIcon
                  title="Add New Address"
                  iconSrc={require("../../../public/vector_indigo_blue.png")}
                  iconSize={20}
                  onClick={() => {
                    if (context) {
                      context.openAddModal();
                    }
                  }}
                  style={{
                    justifyContent: "center",
                    width: "100%",
                  }}
                />
              </div>
            </NoPaddingPaper>
          </div>
          <MobileOrderSummary
            orderSummaryHook={context.orderSummaryHook}
            serviceCars={context.orderSummaryHook.serviceCars}
            isFetchLoading={context.addressHook.isFetchLoading}
            onClick={context.proceedToNextPage}
            isLoading={context.isProceedingToNextPage}
            disabled={!context.isUsingAddress}
            buttonTitle="Continue"
            GobackButton={{
              onClick: context.goBackPage,
              isLoading: context.isLoadingGoback,
              disabled: context.isProceedingToNextPage,
            }}
          />
        </div>
      </div>
    </>
  );
};

const EndLine = (props: {
  onClickGoBack: () => void;
  onClickContinue: () => void;
  isDisabledContinue: boolean;
  isLoadingContinue: boolean;
  isLoadingGoBack: boolean;
  deviceWidth: any;
}) => {
  return (
    <div className="flex flex-col justify-center">
      <div className="flex flex-row justify-end items-center">
        <div className="flex flex-1 lg:flex-initial">
          <WhiteButton
            title="Go Back"
            onClick={props.onClickGoBack}
            style={{
              justifyContent: "center",
              width: props.deviceWidth >= LG ? 120 : "100%",
              height: 44,
              borderRadius: 8,
              marginRight: 16,
            }}
            isLoading={props.isLoadingGoBack}
          />
        </div>
        <div className="flex flex-1 lg:flex-initial">
          <BackgroundSmallButton
            title="Continue"
            onClick={props.onClickContinue}
            backgroundColor="DARK_BLUE"
            style={{
              justifyContent: "center",
              width: props.deviceWidth >= LG ? 120 : "100%",
              height: 44,
            }}
            disabled={props.isDisabledContinue}
            isLoading={props.isLoadingContinue}
          />
        </div>
      </div>
    </div>
  );
};

export default BookingAddressContainer;
