import { Color } from "../../types/Color";

interface MarkedSelectionProps {
  title: string;
  style?: React.CSSProperties;
}

const MarkedSelection = (props: MarkedSelectionProps) => {
  return (
    <div
      className="flex flex-col justify-center rounded px-4 py-1"
      style={{
        background: Color.MINT_CREAM_MID,
        ...props.style,
      }}
    >
      <span
        style={{
          color: Color.PALE_SKY_MID,
          fontWeight: 600,
          fontSize: 12,
          lineHeight: "150%",
        }}
      >
        {props.title}
      </span>
    </div>
  );
};

export default MarkedSelection;
