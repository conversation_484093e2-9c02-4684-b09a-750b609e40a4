import React from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import IndigoBlueText16 from "../../components/atom/Texts/IndigoBlueText16";
import { Color } from "../../types/Color";

interface EmptyAddressItemProps {}

const EmptyAddressItem = ({}: EmptyAddressItemProps) => {
  return (
    <div
      className="flex flex-col md:flex-row items-center justify-center md:justify-start rounded-lg p-4 space-y-4 md:space-x-4 md:space-y-0"
      style={{
        border: "1px solid #E6E6E8",
        backgroundColor: Color.WHITE,
      }}
    >
      <SizeableImage size={72} src={require("../../../public/address_empty.png")} />
      <IndigoBlueText16
        className="text-center md:text-start"
        style={{ fontWeight: 600, lineHeight: 1.5, flex: 1 }}
      >
        You have no available addresses. Please add a service location to continue to the next page.
      </IndigoBlueText16>
    </div>
  );
};

export default EmptyAddressItem;
