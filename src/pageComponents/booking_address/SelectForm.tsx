import { useEffect, useRef, useState } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface SelectFormProps {
  options: string[];
  value: string;
  placeholder: string;
  onChange: (target: string) => void;
  style?: React.CSSProperties;
}

const SelectForm = (props: SelectFormProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<string>(props.value);
  const [isSelected, setIsSelected] = useState<boolean>(false);
  const selectRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    setSelectedOption(props.value);
  }, [props.value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && isOpen && !selectRef.current.contains(event.target as Node)) {
        setIsSelected(false);
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const handleToggleOptions = () => {
    setIsOpen(!isOpen);
    setIsSelected(!isSelected);
  };

  const handleSelectOption = (option: string) => {
    setSelectedOption(option);
    props.onChange(selectedOption);
    setIsSelected(false);
    setIsOpen(false);
  };

  return (
    <div className="relative inline-block" ref={selectRef}>
      <span className="rounded-lg shadow-sm">
        <button
          value={props.value}
          className="flex flex-row justify-between rounded-lg w-full px-4 py-3"
          style={{
            border: isSelected ? "1px solid #475467" : "1px solid #D0D5DD",
            boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
            color: isSelected || selectedOption ? Color.INDIGO_BLUE : Color.PALE_SKY,
            fontWeight: 400,
            fontSize: 16,
            lineHeight: 1,
          }}
          onClick={() => {
            handleToggleOptions();
          }}
        >
          <div className="flex flex-row items-start" style={{ paddingTop: 2, paddingBottom: 2 }}>
            {isSelected || selectedOption ? (
              <SizeableImage
                src={require("../../../public/location_selected.png")}
                size={16}
                style={{ marginRight: 8 }}
              />
            ) : (
              <SizeableImage
                src={require("../../../public/location_grey.png")}
                size={16}
                style={{ marginRight: 8 }}
              />
            )}
            {selectedOption || props.placeholder}
          </div>
          {isSelected || selectedOption ? (
            <SizeableImage src={require("../../../public/check_indigo_blue.png")} size={20} />
          ) : (
            <SizeableImage src={require("../../../public/select_folded_grey.png")} size={20} />
          )}
        </button>
      </span>

      {isOpen && (
        <div className="absolute left-0 top-10 w-full mt-2 rounded-md shadow-lg z-20">
          <ul className="py-1 overflow-auto bg-white rounded-md">
            {props.options.map((option, index) => (
              <li
                key={index}
                className="px-4 py-2 cursor-pointer hover:bg-gray-300"
                onClick={() => handleSelectOption(option)}
              >
                {option}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default SelectForm;
