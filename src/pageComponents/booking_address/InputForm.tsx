import { Color } from "../../types/Color";

interface InputFormProps {
  value: string;
  placeholder: string;
  onChange: (target: string) => void;
  disabled?: boolean;
  style?: React.CSSProperties;
}

const InputForm = (props: InputFormProps) => {
  return (
    <input
      className="rounded-lg px-[14px] py-[10px] custom-input"
      style={{
        background: "#FFFFFF",
        border: "1px solid #D0D5DD",
        boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
        ...props.style,
      }}
      placeholder={props.placeholder}
      onChange={(e) => props.onChange(e.target.value)}
      value={props.value}
      disabled={props.disabled}
    />
  );
};

export default InputForm;
