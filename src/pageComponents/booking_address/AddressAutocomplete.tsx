import usePlacesAutocomplete, { getGeocode, getLatLng } from "use-places-autocomplete";
import useOnClickOutside from "react-cool-onclickoutside";
import { MutableRefObject, useEffect, useRef, useState } from "react";
import Image from "next/image";
import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

//= TODO
//= 1. 함수는 function으로 통일
//= 2. 리액트는 데이터 코드 정형화가 안되기 때문에 가독성 때문에 내부 함수는 이왕이면 순수하게 만드는게 나음.
//= renderSuggestion의 data같은건 파라미터로 받아서 처리되게.
//= 3. 필요없는 로그 삭제하고, dev환경에서만 로그될 수 있게 해서 result[0] 로그는 그대로 유지
//= 4. result[0]라는건 선택한 result가 여러개일수도 있다는건지?
//= 5. googlePlaceHook으로 명명하고 쓰는 곳에서는 googlePlaceHook으로 쓰는게 좋을듯. 우리쪽 state인지
//= 라이브러리에서 제공하는 기능인지 구분이 어려움.
//= 6. 전부 반영 후 주석삭제.

interface AddressAutocompleteProps {
  isOn: boolean;
  initialValue: string;
  onValueChange: (value: string) => void;
  populateForms: (
    addressLine1: string,
    city: string,
    province: string,
    postal: string,
    country: string
  ) => void;
  onClick: () => void;
  disabled?: boolean;
  style?: React.CSSProperties;
}

const AddressAutocomplete = (props: AddressAutocompleteProps) => {
  const [isFirstCalled, setIsFirstCalled] = useState<boolean>(true);
  const [isInputClicked, setIsInputClicked] = useState<boolean>(false);

  useEffect(() => {
    if (props.initialValue && props.isOn) {
      setValue(props.initialValue, false);
      setIsFirstCalled(true);
    } else {
      setValue("", false);
      setIsFirstCalled(false);
    }
  }, [props.isOn, props.initialValue]);

  useEffect(() => {
    setIsInputClicked(false);
  }, [props.isOn]);

  const {
    ready,
    value,
    suggestions: { status, data },
    setValue,
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      types: ["address"],
      componentRestrictions: {
        country: "ca",
      },
      /* Define search scope here */
    },
    debounce: 300,
  });

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsFirstCalled(false);
    setValue(e.target.value);

    // to keep track of the input text in case the user wants to input manually
    props.onValueChange(e.target.value);
  };

  const handleSelect =
    ({ description }: { description: string }) =>
    () => {
      clearSuggestions();

      getGeocode({ address: description })
        .then((results) => {
          console.log(results[0]);

          let streetAddress = "";
          let city = "";
          let province = "";
          let postal = "";
          let country = "";

          for (let i = 0; i < results[0].address_components.length; i++) {
            const types = results[0].address_components[i].types;
            const longName = results[0].address_components[i].long_name;

            if (types.includes("street_number")) {
              streetAddress += longName;
            }

            if (types.includes("route")) {
              streetAddress += " " + longName;
            }

            if (types.includes("locality")) {
              city = longName;
            }

            if (types.includes("administrative_area_level_1")) {
              province = longName;
            }

            if (types.includes("postal_code")) {
              postal = longName;
            }

            if (types.includes("country")) {
              country = longName;
            }
          }

          console.log("streetAddress = " + streetAddress);
          setValue(streetAddress, false);
          props.populateForms(streetAddress, city, province, postal, country);
          setIsInputClicked(false);
        })
        .catch((error) => {
          console.log("😱 Error: ", error);
        });
    };

  const renderSuggestions = () =>
    data.map((suggestion) => {
      const {
        place_id,
        structured_formatting: { main_text, secondary_text },
      } = suggestion;

      const completeText = main_text + " " + secondary_text;
      const matchingLastIndex = completeText.toLowerCase().indexOf(value.toLowerCase());

      let matchingText = "";
      let remainingText = completeText;

      if (matchingLastIndex === 0) {
        matchingText = completeText.substring(0, value.length);
        remainingText = completeText.substring(value.length);
      }

      return (
        <li
          className="flex flex-row cursor-pointer hover:bg-gray-100"
          key={place_id}
          onClick={handleSelect(suggestion)}
        >
          <div className="flex flex-col justify-center items-center">
            <SizeableImage
              src={require("../../../public/location_black.png")}
              size={20}
              style={{
                marginRight: 8,
              }}
            />
          </div>
          <span
            style={{
              fontWeight: 500,
              fontSize: 16,
              lineHeight: "150%",
              color: Color.EBONY,
            }}
          >
            {matchingText}
            <span
              style={{
                fontWeight: 500,
                fontSize: 16,
                lineHeight: "150%",
                color: Color.EBONY,
              }}
            >
              {remainingText}
            </span>
          </span>
        </li>
      );
    });

  return (
    <div style={props.style}>
      <div
        className="flex flex-row justify-between rounded-lg"
        style={{
          border: "1px solid #D0D5DD",
          boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
        }}
      >
        <input
          className="relative custom-input bg-white rounded-lg focus:ring-orange-400 focus:border-orange-400 block w-full px-[14px] py-[10px] transition-all"
          value={isFirstCalled ? props.initialValue : value}
          onChange={handleInput}
          disabled={!ready || props.disabled}
          placeholder="Input your street address here..."
          onFocus={() => setIsInputClicked(true)}
        />
        {props.disabled === true ? (
          <button
            className="flex flex-col justify-center items-center mr-2"
            onClick={props.onClick}
          >
            <SizeableImage src={require("../../../public/trash_can_red_icon.png")} size={36} />
          </button>
        ) : null}
      </div>

      {/* We can use the "status" to decide whether we should display the dropdown or not */}
      {isInputClicked ? (
        <div
          className="absolute mt-1 bg-white rounded-lg w-full outline-none transition-all z-20"
          style={{
            border: "1px solid #F2F4F7",
            boxShadow:
              "0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03)",
          }}
        >
          {status === "OK" ? (
            <ul className="flex flex-col gap-y-5 py-[14px] px-4 ">{renderSuggestions()}</ul>
          ) : (
            <div
              className="p-4"
              style={{
                fontWeight: 500,
                fontSize: 16,
                lineHeight: "150%",
                color: Color.GRAY,
              }}
            >
              Please enter an address.
            </div>
          )}
          <div className="flex flex-row justify-end h-10 px-[14px] py-1">
            <span
              className="flex flex-col justify-end pb-1 mr-2"
              style={{
                fontWeight: 400,
                fontSize: 16,
                lineHeight: 1,
                color: Color.PALE_SKY,
              }}
            >
              powered by
            </span>
            <div className="flex flex-col justify-end w-[102px] h-8">
              <Image src={require("../../../public/logo_google.png")} />
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default AddressAutocomplete;
