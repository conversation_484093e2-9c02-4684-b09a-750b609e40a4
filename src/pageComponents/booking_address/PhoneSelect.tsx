import { useEffect, useRef, useState } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import { Color } from "../../types/Color";

interface PhoneSelectProps {
  imageSrc: any;
  regionCode: string;
  value: string;
  onChange: (selectedOption: string) => void;
  style?: React.CSSProperties;
}

const PhoneSelect = (props: PhoneSelectProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState<string>("+1");
  const selectRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    props.onChange(selectedOption);
  }, [selectedOption]);

  useEffect(() => {
    if (props.value === "") {
      setSelectedOption("");
    }
  }, [props.value]);

  const handleToggleOptions = () => {
    setIsOpen(!isOpen);
  };

  const handleSelectOption = (option: string) => {
    setSelectedOption(option);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && isOpen && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative inline-block" ref={selectRef}>
      <div>
        <span className="rounded-lg shadow-sm">
          <button
            type="button"
            className="flex flex-row items-center justify-evenly rounded-lg w-[120px] py-[10px]"
            style={{
              border: "1px solid #D0D5DD",
              boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
              color: Color.PALE_SKY,
              fontWeight: 400,
              fontSize: 16,
              lineHeight: 1,
              ...props.style,
            }}
            onClick={handleToggleOptions}
          >
            <div
              className="flex flex-row items-start mr-1"
              style={{ paddingTop: 2, paddingBottom: 2 }}
            >
              <SizeableImage src={props.imageSrc} size={20} style={{ marginRight: 8 }} />
              <span className="flex flex-col justify-center items-center" style={{ height: 20 }}>
                {selectedOption || `+${props.regionCode}`}
              </span>
            </div>
            <div
              className="flex flex-col justify-center items-center"
              style={{ paddingTop: 2, paddingBottom: 2 }}
            >
              <SizeableImage src={require("../../../public/select_folded_grey.png")} size={20} />
            </div>
          </button>
        </span>
      </div>
      {isOpen && (
        <div className="absolute left-0 top-10 w-full mt-2 rounded-md shadow-lg z-20">
          <ul className="py-1 overflow-auto bg-white rounded-md">
            <li
              className="px-4 py-2 text-sm text-gray-700 cursor-pointer hover:bg-gray-100"
              onClick={() => handleSelectOption("+1")}
            >
              CA +1
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default PhoneSelect;
