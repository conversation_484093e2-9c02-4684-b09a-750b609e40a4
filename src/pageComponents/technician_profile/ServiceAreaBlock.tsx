import { useState } from "react";
import ConfirmCancelModal from "../../components/Modal/ConfirmCancelModal";
import { GarbageIcon, LocationIcon, PencilIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import TextInput from "../../components/atom/TextInput";
import Text14 from "../../components/atom/Texts/base/Text14";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";

interface ServiceAreaBlockProps {}

const ServiceAreaBlock = (props: ServiceAreaBlockProps) => {
  const [isServiceAreaModalOn, setIsServiceAreaModalOn] = useState<boolean>(false);
  const [serviceArea, setServiceArea] = useState<string>("London, Ontario Canada");

  const handleAddArea = () => {
    setIsServiceAreaModalOn(false);
  };

  return (
    <div className="my-5">
      <Paper>
        <ConfirmCancelModal
          isOn={isServiceAreaModalOn}
          onOff={() => setIsServiceAreaModalOn(false)}
          title="Edit Service Area"
          description=""
          webHeight={250}
          content={
            <>
              <Text14 className="font-medium mb-2">Location</Text14>
              <TextInput
                value={serviceArea}
                onChange={(val) => setServiceArea(val)}
                placeholder="Service Area"
              />
            </>
          }
          confirmButtonTitle="Confirm"
          cancelButtonTitle="Cancel"
          onClickConfirmButton={() => handleAddArea}
          onClickCancelButton={() => setIsServiceAreaModalOn(false)}
          isConfirmDisabled={serviceArea === ""}
          isLoading={false}
        />
        <ProfileBlockHeader
          title="Service Area"
          onClick={() => setIsServiceAreaModalOn(true)}
          buttonIcon={<PencilIcon />}
          buttonText="Edit Service Area"
        />
        <div className="border-t-[0.5px] border-[rgba(0,0,0,0.2)]" />
        <div className="w-full">
          <div className="flex items-center justify-between p-6 border-b-[rgba(0,0,0,0.2)]">
            <div className="flex flex-row items-center gap-4">
              <div className="flex flex-row gap-3">
                <LocationIcon />
                <span className="text-lg font-medium">{serviceArea}</span>
              </div>
            </div>
            <div className="flex items-center ">
              <button className="bg-[#FEE9E9] py-3 px-4 rounded-sm">
                <GarbageIcon />
              </button>
            </div>
          </div>
        </div>
      </Paper>
    </div>
  );
};

export default ServiceAreaBlock;
