import { useState } from "react";
import ConfirmCancelModal from "../../components/Modal/ConfirmCancelModal";
import { GarbageIcon, PencilIcon, PlusIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import TextInput from "../../components/atom/TextInput";
import Text14 from "../../components/atom/Texts/base/Text14";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";

interface CategoryBlockProps {}

interface CategoryProps {
  title: string;
  subtitle?: string;
}

const CategoryBlock = (props: CategoryBlockProps) => {
  const [isCategoryModalOn, setIsCategoryModalOn] = useState<boolean>(false);
  const [editingCategory, setEditingCategory] = useState<boolean>(false);
  const [skillInput, setSkillInput] = useState<string>("");
  const [supportingSkillInput, setSupportingSkillInput] = useState<string>("");

  const [categories, setCategories] = useState([
    { title: "Mechanical", subtitle: "Includes: Tire Swap, Oil Change" },
    { title: "Dent Repair" },
  ]);

  const handleAddCategory = () => {
    if (editingCategory) {
      // TO DO: update user with new category
    } else {
      // TO DO: update existing category
    }
    setSkillInput("");
    setSupportingSkillInput("");
    setIsCategoryModalOn(false);
    setEditingCategory(false);
  };

  const handleOpenModal = (category?: CategoryProps | undefined) => {
    if (category) {
      setSkillInput(category.title);
      setSupportingSkillInput(category.subtitle ? category.subtitle : "");
      setIsCategoryModalOn(true);
      setEditingCategory(true);
    } else {
      setSkillInput("");
      setSupportingSkillInput("");
      setEditingCategory(false);
      setIsCategoryModalOn(true);
    }
  };

  return (
    <div className="my-5">
      <Paper>
        <ConfirmCancelModal
          isOn={isCategoryModalOn}
          onOff={() => setIsCategoryModalOn(false)}
          title={`${editingCategory ? "Edit" : "Add"} Supporting Category`}
          description=""
          webHeight={330}
          content={
            <>
              <Text14 className="font-medium mb-2">Support Skill</Text14>
              <TextInput
                value={skillInput}
                onChange={(val) => setSkillInput(val)}
                placeholder="Support Skill"
                style={{ marginBottom: 12 }}
              />
              <Text14 className="font-medium mb-2">Other Skill</Text14>
              <TextInput
                value={supportingSkillInput}
                onChange={(val) => setSupportingSkillInput(val)}
                placeholder="Other Skill"
              />
            </>
          }
          confirmButtonTitle="Confirm"
          cancelButtonTitle="Cancel"
          onClickConfirmButton={() => handleAddCategory}
          onClickCancelButton={() => setIsCategoryModalOn(false)}
          isConfirmDisabled={skillInput === "" || supportingSkillInput === ""}
          isLoading={false}
        />
        <ProfileBlockHeader
          title="Supporting Categories"
          onClick={() => handleOpenModal()}
          buttonIcon={<PlusIcon />}
          buttonText="Add Supporting Categories"
        />
        <div className="border-t-[0.5px] border-[rgba(0,0,0,0.2)]" />
        <div className="w-full">
          {categories.map((category, index) => {
            return (
              <div
                className={`flex items-center justify-between ${
                  categories.length === index + 1 ? "" : "border-b-[0.5px]"
                } p-6 border-b-[rgba(0,0,0,0.2)]`}
                key={index}
              >
                <div className="flex flex-row items-center gap-4">
                  <div className="flex flex-col ">
                    <span className="text-lg font-medium">{category.title}</span>
                    <Text14 className="font-light text-gray-400">{category.subtitle}</Text14>
                  </div>
                </div>
                <div className="flex items-center ">
                  <button
                    className="mr-2 bg-[#EBF2FF] py-3 px-4 rounded-sm"
                    onClick={() => handleOpenModal(category)}
                  >
                    <PencilIcon />
                  </button>
                  <button className="bg-[#FEE9E9] py-3 px-4 rounded-sm">
                    <GarbageIcon />
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </Paper>
    </div>
  );
};

export default CategoryBlock;
