import Image from "next/image";
import Paper from "../../components/atom/Paper";
import Text14 from "../../components/atom/Texts/base/Text14";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";

interface CertificationBlockProps {}

const CertificationBlock = (props: CertificationBlockProps) => {
  return (
    <div className="my-5">
      <Paper>
        <ProfileBlockHeader title="Certification" />
        <div className="border-t-[0.5px] border-[rgba(0,0,0,0.2)]" />
        <div className="w-full">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between p-6 border-b-[rgba(0,0,0,0.2)]">
            <div className="flex flex-row items-center gap-7">
              <div
                className="rounded-lg bg-gray-300 p-4 flex items-center justify-center relative overflow-h"
                style={{
                  width: 60,
                  height: 60,
                }}
              >
                <Image src={require("../../../public/certification_placeholder.png")} />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-medium">Mastering Car Service 2.0</span>
                <Text14 className="font-light">Sponsored by Hyundai</Text14>
                <Text14 className="font-light text-gray-400">Issued Aug 2022</Text14>
              </div>
            </div>
            <div className="flex items-center md:mt-0 mt-5 md:w-1/4 w-full">
              <button className="w-full border-black border-[1.5px] font-medium py-3 px-4 text-sm rounded-sm">
                Show License
              </button>
            </div>
          </div>
        </div>
      </Paper>
    </div>
  );
};

export default CertificationBlock;
