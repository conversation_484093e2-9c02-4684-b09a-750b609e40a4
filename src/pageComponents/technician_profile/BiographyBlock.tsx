import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../@redux";
import { thunkUpdateUserBiography } from "../../@redux/modules/auth/thunks";
import ConfirmCancelModal from "../../components/Modal/ConfirmCancelModal";
import { PencilIcon } from "../../components/atom/Icons";
import Paper from "../../components/atom/Paper";
import Text14 from "../../components/atom/Texts/base/Text14";
import ProfileBlockHeader from "../../components/molecule/ProfileBlockHeader";

interface BiographyBlockProps {}

const BiographyBlock = (props: BiographyBlockProps) => {
  const dispatch = useDispatch<any>();
  const isLoading = useSelector((state: RootState) => state.auth.isLoading);
  const userBiography = useSelector((state: RootState) => state.auth.user?.biography);

  const [biography, setBiography] = useState<string>(userBiography || "");
  const [isBiographyModalOn, setIsBiographyModalOn] = useState<boolean>(false);
  const isFirstCalled = useRef<boolean>(true);

  useEffect(() => {
    if (isFirstCalled.current) {
      isFirstCalled.current = false;
    } else if (!isLoading) {
      setIsBiographyModalOn(false);
    }
  }, [isLoading]);

  useEffect(() => {
    if (isBiographyModalOn) {
      setupBiography();
    }
  }, [isBiographyModalOn]);

  function setupBiography() {
    if (userBiography) {
      setBiography(userBiography);
    }
  }

  const handleEditBiography = async () => {
    try {
      await dispatch(
        thunkUpdateUserBiography({
          biography: biography,
        })
      );
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="my-5">
      <Paper>
        <ConfirmCancelModal
          isOn={isBiographyModalOn}
          onOff={() => setIsBiographyModalOn(false)}
          title="Edit Biography"
          description=""
          webHeight={270}
          content={
            <>
              <Text14 className="font-medium">Biography</Text14>
              <div className="flex relative mt-2">
                <textarea
                  className="bg-white border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-orange-400 focus:border-orange-400 block w-full p-2.5 outline-none pl-3 transition-all"
                  placeholder={"Enter your biography"}
                  value={biography}
                  onChange={(event) => {
                    setBiography(event.target.value.trimStart());
                  }}
                />
              </div>
            </>
          }
          confirmButtonTitle="Confirm"
          cancelButtonTitle="Cancel"
          onClickConfirmButton={handleEditBiography}
          onClickCancelButton={() => setIsBiographyModalOn(false)}
          isConfirmDisabled={biography === ""}
          isLoading={isLoading}
        />

        <ProfileBlockHeader
          title="Biography"
          onClick={() => setIsBiographyModalOn(true)}
          buttonIcon={<PencilIcon />}
          buttonText="Edit Biography"
        />
        <div className="border-t-[0.5px] border-[rgba(0,0,0,0.2)]" />
        <div className="whitespace-normal w-full p-6">
          <p>{userBiography}</p>
        </div>
      </Paper>
    </div>
  );
};

export default BiographyBlock;
