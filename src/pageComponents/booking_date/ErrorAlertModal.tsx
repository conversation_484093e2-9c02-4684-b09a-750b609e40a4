import ResizingModal from "../../components/Modal/ResizingModal";
import CloseButton from "../../components/atom/Buttons/CloseButton";
import WarningIcon from "../../components/atom/Icons/WarningIcon";
import { Color } from "../../types/Color";

interface ErrorAlertModalProps {
  isOn: boolean;
  onOff: () => void;
  title: string;
  description: string;
  isLoading?: boolean;
}
const ErrorAlertModal = (props: ErrorAlertModalProps) => {
  function handleCloseModal() {
    props.onOff();
  }

  return (
    <ResizingModal
      isOn={props.isOn}
      onOff={handleCloseModal}
      modalSize={{
        mobile: {
          width: 375,
          height: 212,
        },
        web: {
          width: 375,
          height: 212,
        },
      }}
    >
      <div className="relative flex flex-col justify-between bg-white rounded-lg h-full w-full max-w-[375px] p-6 pr-2 overflow-y-scroll">
        <div className="absolute top-5 right-4">
          <CloseButton onClick={handleCloseModal} />
        </div>
        <div className="flex flex-col items-stretch">
          <WarningIcon />
          <div className="flex flex-col">
            <span
              className="pt-5"
              style={{
                marginBottom: 8,
                fontWeight: 500,
                fontSize: 18,
                lineHeight: "156%",
                color: Color.EBONY,
              }}
            >
              {props.title}
            </span>
            <span
              className="pb-3"
              style={{
                fontWeight: 400,
                fontSize: 14,
                lineHeight: "20px",
                color: Color.PALE_SKY,
              }}
            >
              {props.description}
            </span>
          </div>
        </div>
      </div>
    </ResizingModal>
  );
};

export default ErrorAlertModal;
