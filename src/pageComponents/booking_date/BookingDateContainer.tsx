import moment from "moment";
import Head from "next/head";
import { CSSProperties, ReactNode, useContext } from "react";
import SelectDateTechnicianModal from "../../components/Modal/SelectDateTechnicianModal";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import WhiteButton from "../../components/atom/Buttons/WhiteButton";
import WhiteButtonWithIcon from "../../components/atom/Buttons/WhiteButtonWithIcon";
import NoPaddingPaper from "../../components/atom/NoPaddingPaper";
import SizeableImage from "../../components/atom/SizeableImage";
import Spinner from "../../components/atom/Spinner";
import StepHeaderIndigoBlue from "../../components/molecule/StepHeaderIndigoBlue";
import BookingStepper from "../../components/organism/BookingStepper";
import BookingStepperMobile from "../../components/organism/BookingStepperMobile";
import MobileOrderSummary from "../../components/organism/MobileOrderSummary";
import OrderSummary from "../../components/organism/OrderSummary";
import TechnicianReviewModal from "../../components/organism/TechnicianReviewModal";
import { calendarContext } from "../../contexts/CalendarContext";
import { globalContext } from "../../contexts/GlobalContext";
import { useOrderSummarySticky } from "../../hooks/useOrderSummarySticky";
import { Color } from "../../types/Color";
import { PhoneNumber } from "../../types/PhoneNumber";
import { LG } from "../../values";
import ErrorAlertModal from "./ErrorAlertModal";

const BookingDateContainer = () => {
  const gContext = useContext(globalContext);
  const context = useContext(calendarContext)!;
  const orderSummaryStickyHook = useOrderSummarySticky(
    context!.scrollableElementRef,
    context!.orderSummaryHook.dynamicHeightRef,
  );
  const deviceWidth = gContext?.dimensionWidth;

  return (
    <>
      <Head>
        <title>Booking Date - Wheel Easy</title>
      </Head>
      <div className="hidden md:block">
        <BookingStepper step={2} />
      </div>
      {/* Mobile Stepper */}
      <div className="block md:hidden">
        <BookingStepperMobile step={2} />
      </div>
      {context.technicianSearchHook.technicians ? (
        <TechnicianReviewModal
          isOn={context.reviewTechIndex !== -1}
          onOff={() => context.setReviewTechIndex(-1)}
          technicianId={
            context.reviewTechIndex !== -1
              ? context.technicianSearchHook.technicians[context.reviewTechIndex].technicianId
              : -1
          }
          selectedTechnicians={context.technicianSearchHook.technicians[context.reviewTechIndex]}
        />
      ) : null}
      <SelectDateTechnicianModal
        isOn={
          context.categorySessionHook.selectedCategoryIndex !== -1 && context.reviewTechIndex === -1
        }
        onOff={context.handleOnOffSelectDateTechnicianModal}
        showFirstStep={context.showFirstStep}
        showSecondStep={context.showSecondStep}
        modalTitle={context.selectDateTechnicianModalTitle}
        modalButtonText={context.selectDateTechnicianModalButtonText}
        selectedMonth={context.technicianSearchHook.selectedMonth}
        selectedDate={context.technicianSearchHook.selectedDate}
        selectedTime={context.technicianSearchHook.selectedTime}
        getIsDateAvailable={context.technicianSearchHook.getIsDateAvailable}
        isWideModal={context.technicianSearchHook.selectedDate !== null}
        isLoading={context.technicianSearchHook.isLoading}
        onClickDate={(date: number) => context.technicianSearchHook.setSelectedDate(date)}
        onClickTime={(timeOption: string) => {
          context.technicianSearchHook.setSelectedTimeOption(timeOption);
          context.technicianSearchHook.setSelectedTechIndex(0);
        }}
        isConfirmButtonDisabled={context.isConfirmButtonDisabled}
        isConfirmButtonLoading={context.isProceedingToNextPage}
        onClickConfirmButton={context.onClickConfirmButton}
        onClickBackButton={context.onClickBackButton}
        showBackButton={context.showBackButton}
        showCalendarPicker={!gContext?.isMobile || context.step === 0 || context.step === 1}
        weekDays={context.weekDays}
        generatedWeeks={context.generateWeeks()}
        isPreviousCalendarMonthDisabled={context.isPreviousCalendarMonthDisabled}
        addMonthToSelectedTime={context.technicianSearchHook.addMonthToSelectedTime}
        minusMonthToSelectedTime={context.technicianSearchHook.minusMonthToSelectedTime}
        availableHours={context.technicianSearchHook.availableHours}
      />
      <ErrorAlertModal
        isOn={context.errorMessageFromServer !== null}
        onOff={context.handleOnOffErrorAlertModal}
        title="Server Error"
        description={context.errorMessageFromServer!}
      />
      <div
        className="flex-col flex-1 bg-[#F8FAFB] flex overflow-y-scroll min-w-[320px] h-[calc(var(--vh,1vh)*100-232px)] md:max-lg:h-[calc(var(--vh,1vh)*100-110px)] lg:h-[calc(var(--vh,1vh)*100-120px)]"
        ref={context.scrollableElementRef}
      >
        <div className="flex flex-col h-full md:px-10">
          {/* Web */}
          <div className="hidden md:flex flex-row h-full justify-center">
            <div className="flex h-full justify-center w-[1360px]">
              <div className="flex flex-row justify-center w-full h-full pt-10">
                <div className="flex flex-col items-stretch w-full mr-6">
                  <NoPaddingPaper borders="rounded-br-none md:rounded-t-xl rounded-t-none rounded-bl-none md:rounded-bl-xl">
                    <div className="flex flex-col items-stretch">
                      <div className="flex-row items-center hidden md:flex justify-between p-6 pb-0">
                        <StepHeaderIndigoBlue title="Configure Booking" description="" />
                      </div>
                      {context.isFetchLoading ? (
                        <div className="w-full h-full flex flex-col justify-center items-center rounded-xl bg-white min-h-[400px]">
                          <div className="flex flex-col items-center">
                            <Spinner />
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="relative flex flex-col p-6 pt-4 border-b border-b-gray gap-4">
                            <div className="flex flex-col border border-neutral-200 bg-white rounded-lg px-6 py-5 gap-4">
                              <div className="flex flex-row justify-between md:max-lg:flex-col md:max-lg:gap-4">
                                <div className="flex flex-col justify-center">
                                  <span className="text-xl font-semibold text-neutral-900">
                                    Mechanical Service
                                  </span>
                                </div>
                                {context !== null &&
                                context.categorySessionHook.categorySessionItems.length > 0
                                  ? context.categorySessionHook.categorySessionItems.map(
                                      (category, index) => (
                                        <WhiteButtonWithIcon
                                          title="Select Date and Time"
                                          iconSrc={require("../../../public/calendar_plus_indigo_blue.png")}
                                          iconSize={20}
                                          onClick={() =>
                                            context.categorySessionHook.setSelectedCategoryIndex(
                                              index,
                                            )
                                          }
                                          style={{
                                            color: Color.INDIGO_BLUE,
                                            border: "1px solid rgba(208, 213, 221, 1)",
                                            justifyContent: "center",
                                          }}
                                        />
                                      ),
                                    )
                                  : null}
                              </div>
                              <div className="border-b" />
                              <div className="flex flex-col lg:flex-row justify-between space-y-3 lg:space-y-0">
                                <div className="flex flex-[2] flex-col">
                                  <span
                                    className="test-xs font-semibold"
                                    style={{
                                      color: Color.INDIGO_BLUE_08,
                                      marginBottom: 16,
                                    }}
                                  >
                                    Service Items
                                  </span>
                                  <div className="flex flex-col space-y-4">
                                    {context.categorySessionHook.categorySessionItems.map(
                                      (item, index) => (
                                        <>
                                          {item.services.map((service, index) => (
                                            <ServiceItemContent
                                              pictureUri={service.pictureUri}
                                              quantity={service.cars.length}
                                              serviceName={service.name}
                                              durationInMinutes={service.duration}
                                            />
                                          ))}
                                        </>
                                      ),
                                    )}
                                  </div>
                                </div>
                                <div className="hidden flex-[0.1] lg:flex" />
                                <div className="flex flex-1 flex-col gap-6">
                                  <div className="flex flex-col">
                                    <span
                                      className="test-xs font-semibold"
                                      style={{
                                        color: Color.INDIGO_BLUE_08,
                                        marginBottom: 16,
                                      }}
                                    >
                                      Booking Info
                                    </span>
                                    <div className="space-y-4">
                                      <BookingInfoElement
                                        imgSrc={require("../../../public/user_grey.png")}
                                        text={context.nowAddress?.customerName}
                                      />
                                      <BookingInfoElement
                                        imgSrc={require("../../../public/phone_grey.png")}
                                        text={
                                          context.nowAddress
                                            ? PhoneNumber.getPhoneNumberFromAPI(
                                                context.nowAddress?.customerPhone,
                                              ).getFormattedFullPhoneNumber()
                                            : null
                                        }
                                      />
                                      <BookingInfoElement
                                        imgSrc={require("../../../public/address_grey.png")}
                                        text={
                                          context.nowAddress ? (
                                            <span>
                                              <span>{context.nowAddress?.street1}</span>
                                              <br />
                                              {context.nowAddress.street2 ? (
                                                <>
                                                  <span>{context.nowAddress?.street2}</span>
                                                  <br />
                                                </>
                                              ) : null}
                                              <span>{context.nowAddress?.city}</span>
                                              <span>, {context.nowAddress?.province}</span>
                                              <span>, {context.nowAddress?.country}</span>
                                              <br />
                                              <span>{context.nowAddress?.postal}</span>
                                            </span>
                                          ) : undefined
                                        }
                                        style={{
                                          alignItems: "flex-start",
                                        }}
                                      />
                                      <BookingInfoElement
                                        text={
                                          context.isReservationReady && context.assignedDateTime
                                            ? context.assignedDateTime?.format("dddd MMMM DD, YYYY")
                                            : undefined
                                        }
                                        imgSrc={require("../../../public/calendar_grey.png")}
                                      />
                                      <BookingInfoElement
                                        text={
                                          context.isReservationReady && context.assignedDateTime
                                            ? context.assignedDateTime?.format("hh:mm A") +
                                              " - " +
                                              moment(context.assignedDateTime)
                                                .add(context.totalDurationByMin, "minutes")
                                                .format("hh:mm A")
                                            : undefined
                                        }
                                        imgSrc={require("../../../public/time_grey.png")}
                                      />
                                    </div>
                                  </div>
                                  {/* <div className="flex flex-col">
                                    <span
                                      className="test-xs font-semibold"
                                      style={{
                                        color: Color.INDIGO_BLUE_08,
                                        marginBottom: 16,
                                      }}
                                    >
                                      Technician
                                    </span>
                                    {context.isReservationReady && context.assignedTech ? (
                                      <TechProfile
                                        profilePictureUri={context.assignedTech?.profilePictureUri}
                                        name={context.assignedTech.name}
                                        rateOfReviews={context.assignedTech.rating}
                                        numOfReviews={context.assignedTech.numOfReviews}
                                      />
                                    ) : (
                                      <span
                                        className="test-xs font-semibold"
                                        style={{
                                          color: Color.INDIGO_BLUE_08,
                                        }}
                                      >
                                        -
                                      </span>
                                    )}
                                  </div> */}
                                </div>
                              </div>
                              <div className="border-b border-white" />
                            </div>
                          </div>
                          <div className="relative p-6">
                            <EndLine
                              onClickGoBack={context.goBackPage}
                              onClickContinue={context.proceedToNextPage}
                              isDisabledContinue={!context.isReservationReady}
                              isLoadingContinue={context.isProceedingToNextPage}
                              isLoadingGoBack={context.isLoadingGoback}
                              deviceWidth={deviceWidth}
                            />
                          </div>
                        </>
                      )}
                    </div>
                  </NoPaddingPaper>
                  <div style={{ paddingBottom: 40 }} />
                </div>
                <div
                  className=""
                  style={{
                    position: orderSummaryStickyHook.getPosition(),
                    top: orderSummaryStickyHook.getTop(),
                    right: orderSummaryStickyHook.getRight(),
                  }}
                >
                  <OrderSummary
                    orderSummaryHook={context.orderSummaryHook}
                    serviceCars={context.orderSummaryHook.serviceCars}
                    isFetchLoading={context.isFetchLoading}
                  />
                </div>
                <div
                  className="min-w-[280px]"
                  style={{
                    display: orderSummaryStickyHook.getDisplay(),
                  }}
                />
              </div>
            </div>
          </div>

          {/* mobile */}
          <div className="flex flex-col w-full h-full justify-between md:hidden">
            <div className="flex flex-col pb-14">
              <NoPaddingPaper borders="rounded-br-none rounded-t-none rounded-bl-none">
                <div className="flex flex-col">
                  <div className="flex-row items-center justify-between p-4 pb-0">
                    <StepHeaderIndigoBlue title="Configure Booking" description="" />
                  </div>
                </div>
                {context.isFetchLoading ? (
                  <div className="w-full h-full flex flex-col justify-center items-center rounded-xl bg-white min-h-[400px]">
                    <div className="flex flex-col items-center">
                      <Spinner />
                    </div>
                  </div>
                ) : (
                  <div className="p-4">
                    <div className="flex flex-col border p-4 rounded-lg">
                      <span className="text-xl font-semibold text-neutral-900">
                        Mechanical Service
                      </span>

                      <div style={{ marginBottom: 16 }} />
                      {context !== null &&
                      context.categorySessionHook.categorySessionItems.length > 0
                        ? context.categorySessionHook.categorySessionItems.map(
                            (category, index) => (
                              <WhiteButtonWithIcon
                                title="Select Date and Time"
                                iconSrc={require("../../../public/calendar_plus_indigo_blue.png")}
                                iconSize={20}
                                onClick={() =>
                                  context.categorySessionHook.setSelectedCategoryIndex(index)
                                }
                                style={{
                                  color: Color.INDIGO_BLUE,
                                  border: "1px solid rgba(208, 213, 221, 1)",
                                  justifyContent: "center",
                                }}
                              />
                            ),
                          )
                        : null}
                      <div className="border-b" style={{ marginTop: 16, marginBottom: 16 }} />
                      <div className="flex-1 flex flex-col">
                        <span
                          className="test-xs font-semibold"
                          style={{
                            color: Color.INDIGO_BLUE_08,
                            marginBottom: 16,
                          }}
                        >
                          Service Items
                        </span>
                        <div className="flex flex-col space-y-4 mb-6">
                          {context.categorySessionHook.categorySessionItems.map((item, index) => (
                            <>
                              {item.services.map((service, index) => (
                                <ServiceItemContentMobile
                                  pictureUri={service.pictureUri}
                                  quantity={service.cars.length}
                                  serviceName={service.name}
                                  durationInMinutes={service.duration}
                                />
                              ))}
                            </>
                          ))}
                        </div>
                        <div className="flex flex-col min-w-[266px] gap-6">
                          <div className="flex flex-col">
                            <span
                              className="test-xs font-semibold"
                              style={{
                                color: Color.INDIGO_BLUE_08,
                                marginBottom: 16,
                              }}
                            >
                              Booking Info
                            </span>
                            <div className="space-y-4">
                              <BookingInfoElement
                                imgSrc={require("../../../public/user_grey.png")}
                                text={context.nowAddress?.customerName}
                              />
                              <BookingInfoElement
                                imgSrc={require("../../../public/phone_grey.png")}
                                text={context.nowAddress?.customerPhone}
                              />
                              <BookingInfoElement
                                imgSrc={require("../../../public/address_grey.png")}
                                text={
                                  context.nowAddress ? (
                                    <span>
                                      <span>{context.nowAddress?.street1}</span>
                                      <br />
                                      {context.nowAddress.street2 ? (
                                        <>
                                          <span>{context.nowAddress?.street2}</span>
                                          <br />
                                        </>
                                      ) : null}
                                      <span>{context.nowAddress?.city}</span>
                                      <span>, {context.nowAddress?.province}</span>
                                      <span>, {context.nowAddress?.country}</span>
                                      <br />
                                      <span>{context.nowAddress?.postal}</span>
                                    </span>
                                  ) : undefined
                                }
                                style={{
                                  alignItems: "flex-start",
                                }}
                              />
                              <BookingInfoElement
                                text={
                                  context.isReservationReady && context.assignedDateTime
                                    ? context.assignedDateTime?.format("dddd MMMM DD, YYYY")
                                    : undefined
                                }
                                imgSrc={require("../../../public/calendar_grey.png")}
                              />
                              <BookingInfoElement
                                text={
                                  context.isReservationReady && context.assignedDateTime
                                    ? context.assignedDateTime?.format("hh:mm A") +
                                      " - " +
                                      moment(context.assignedDateTime)
                                        .add(context.totalDurationByMin, "minutes")
                                        .format("hh:mm A")
                                    : undefined
                                }
                                imgSrc={require("../../../public/time_grey.png")}
                              />
                            </div>
                          </div>
                          {/* <div className="flex flex-col">
                            <span
                              className="test-xs font-semibold"
                              style={{
                                color: Color.INDIGO_BLUE_08,
                                marginBottom: 16,
                              }}
                            >
                              Technician
                            </span>
                            {context.isReservationReady && context.assignedTech ? (
                              <TechProfile
                                profilePictureUri={context.assignedTech?.profilePictureUri}
                                name={context.assignedTech.name}
                                rateOfReviews={context.assignedTech.rating}
                                numOfReviews={context.assignedTech.numOfReviews}
                              />
                            ) : (
                              <span
                                className="test-xs font-semibold"
                                style={{
                                  color: Color.INDIGO_BLUE_08,
                                }}
                              >
                                -
                              </span>
                            )}
                          </div> */}
                        </div>
                        <div style={{ marginTop: 16 }} />
                      </div>
                    </div>
                  </div>
                )}
              </NoPaddingPaper>
            </div>
            <MobileOrderSummary
              orderSummaryHook={context.orderSummaryHook}
              serviceCars={context.orderSummaryHook.serviceCars}
              isFetchLoading={context.isFetchLoading}
              disabled={context.isReservationNotMade}
              buttonTitle="Continue"
              onClick={context.proceedToNextPage}
              GobackButton={{
                onClick: context.goBackPage,
                isLoading: context.isLoadingGoback,
                disabled: context.isProceedingToNextPage,
              }}
              isLoading={context.isProceedingToNextPage}
            />
          </div>
        </div>
      </div>
    </>
  );
};

const EndLine = (props: {
  onClickGoBack: () => void;
  onClickContinue: () => void;
  isDisabledContinue: boolean;
  isLoadingContinue: boolean;
  isLoadingGoBack: boolean;
  deviceWidth: any;
}) => {
  return (
    <div className="flex flex-row gap-4 justify-end">
      <div className="flex flex-1 lg:flex-initial">
        <WhiteButton
          title="Go Back"
          onClick={props.onClickGoBack}
          style={{
            width: props.deviceWidth >= LG ? "auto" : "100%",
            borderRadius: 8,
            paddingTop: 10,
            paddingBottom: 10,
            paddingLeft: 40,
            paddingRight: 40,
            justifyContent: "center",
          }}
          isLoading={props.isLoadingGoBack}
        />
      </div>
      <div className="flex flex-1 lg:flex-initial">
        <BackgroundSmallButton
          title="Confirm"
          onClick={props.onClickContinue}
          backgroundColor="DARK_BLUE"
          style={{
            width: props.deviceWidth >= LG ? "auto" : "100%",
            borderRadius: 8,
            paddingTop: 10,
            paddingBottom: 10,
            paddingLeft: 40,
            paddingRight: 40,
          }}
          disabled={props.isDisabledContinue}
          isLoading={props.isLoadingContinue}
        />
      </div>
    </div>
  );
};

const ServiceItemContent = (props: {
  quantity: number;
  serviceName: string;
  pictureUri: string;
  durationInMinutes: number;
  style?: React.CSSProperties;
}) => {
  return (
    <div className="flex flex-row items-center justify-between" style={props.style}>
      <div className="flex flex-row items-center">
        <span
          className="font-medium text-sm"
          style={{
            width: 16,
            marginRight: 12,
            color: Color.INDIGO_BLUE_06,
          }}
        >
          {`${props.quantity}x`}
        </span>
        <img
          className="w-8 h-8 rounded-md"
          src={props.pictureUri}
          style={{
            marginRight: 12,
          }}
        />
        <span
          className="font-normal text-sm"
          style={{
            color: Color.INDIGO_BLUE,
          }}
        >
          {props.serviceName}
        </span>
      </div>
    </div>
  );
};

const ServiceItemContentMobile = (props: {
  quantity: number;
  serviceName: string;
  pictureUri: string;
  durationInMinutes: number;
  style?: React.CSSProperties;
}) => {
  return (
    <div className="flex flex-row items-center justify-between" style={props.style}>
      <div className="flex flex-row items-center">
        <span
          className="font-medium text-sm"
          style={{
            width: 16,
            marginRight: 12,
            color: "rgba(52, 64, 84, 0.6)",
          }}
        >
          {`${props.quantity}x`}
        </span>
        <img
          className="w-8 h-8 rounded-md"
          src={props.pictureUri}
          style={{
            marginRight: 12,
          }}
        />
        <span
          className="line-clamp-2 font-normal text-sm mr-4"
          style={{
            color: Color.INDIGO_BLUE,
          }}
        >
          {props.serviceName}
        </span>
      </div>
    </div>
  );
};

const BookingInfoElement = (props: {
  imgSrc: any;
  text: string | ReactNode | undefined;
  style?: CSSProperties;
}) => {
  return (
    <div className="flex flex-row" style={props.style}>
      <div className="flex flex-col justify-center">
        <SizeableImage src={props.imgSrc} size={20} style={{ marginRight: 12 }} />
      </div>
      <span className="text-sm font-normal" style={{ color: Color.INDIGO_BLUE }}>
        {props.text || "-"}
      </span>
    </div>
  );
};

const TechProfile = (props: {
  profilePictureUri: string | null;
  name: string;
  rateOfReviews: number | null;
  numOfReviews: number;
}) => {
  return (
    <div className="flex flex-row">
      {props.profilePictureUri ? (
        <div className="rounded-full w-14 h-14 mr-3 overflow-hidden">
          <img src={props.profilePictureUri} />
        </div>
      ) : (
        <div className="rounded-full w-14 h-14 bg-slate-600" />
      )}

      <div className="flex flex-col justify-center gap-2">
        <span className="flex flex-row text-sm fo font-semibold">{props.name}</span>
        <div className="flex flex-row">
          <div className="flex flex-row mr-2">
            <SizeableImage
              src={require("../../../public/review_star_yellow.png")}
              size={16}
              style={{
                marginRight: 6,
              }}
            />
            <span
              className="text-xs font-normal"
              style={{
                color: Color.COD_GRAY,
              }}
            >
              {props.rateOfReviews ? props.rateOfReviews : 0.0}
            </span>
          </div>
          <span
            className="text-xs font-normal"
            style={{
              color: Color.GRAY_MID,
            }}
          >
            {`(${props.numOfReviews} Review)`}
          </span>
        </div>
      </div>
    </div>
  );
};

export default BookingDateContainer;
