import { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import BorderButton from "../../components/atom/Buttons/BorderButton";
import { EmailIcon, PhoneIcon } from "../../components/atom/Icons";
import GrayText14 from "../../components/atom/Texts/GrayText14";
import Text14 from "../../components/atom/Texts/base/Text14";
import Text16 from "../../components/atom/Texts/base/Text16";
import TitleWithIcon from "../../components/atom/TitleWithIcon";
import StepHeader from "../../components/molecule/StepHeader";
import { useAddresses } from "../../hooks/useAddresses";
import EditProfileModal from "../../components/Modal/EditProfileModal";

interface CustomerInfoProps {
  addressHook: ReturnType<typeof useAddresses>;
}

const CustomerInfo = (props: CustomerInfoProps) => {
  const user = useSelector((state: RootState) => state.auth.user);

  const [isEditProfileModalOpen, setIsEditProfileModalOpen] = useState(false);

  const address = useMemo(() => {
    if (props.addressHook.addresses === undefined) {
      return undefined;
    }

    return props.addressHook.addresses[0];
  }, [props.addressHook.addresses]);

  useEffect(() => {
    console.log(isEditProfileModalOpen);
  }, [isEditProfileModalOpen]);

  return (
    <div className="flex flex-col mt-6 xl:mt-5">
      <EditProfileModal
        isOn={isEditProfileModalOpen}
        onOff={() => {
          setIsEditProfileModalOpen(false);
        }}
        profilePictureUri={user?.profilePictureUri || ""}
        firstName={user?.firstName || ""}
        lastName={user?.lastName || ""}
        phone={user?.phone || ""}
      />
      <div className="bg-white xl:rounded-xl border border-x-0 flex flex-col xl:shadow-[0_5px_15px_rgba(0,0,0,0.03)] xl:border-x">
        <div className="hidden flex flex-col items-stretch p-6 border-b xl:block">
          <StepHeader
            title="Customer Information"
            description="Review and edit your information if needed. Address cannot be modified at this stage."
          />
        </div>
        <div className="flex flex-col justify-between items-start xl:flex-row">
          <div className="flex flex-col flex-1 p-6 space-y-6 w-full">
            <TitleWithIcon
              title="Info"
              iconSrc={require("../../../public/title_icon_profile.png")}
            />
            <div className="flex flex-col items-start justify-between xl:flex-row xl:space-x-3">
              <img
                src={user?.profilePictureUri || undefined}
                className="rounded-full mb-4 xl:mb-0 object-cover"
                style={{
                  width: 60,
                  height: 60,
                }}
              />
              <div className="flex flex-1 flex-col space-y-2 items-start w-full">
                <div className="pb-3 xl:pb-0">
                  <Text16>
                    {user?.firstName} {user?.lastName}
                  </Text16>
                </div>
                <div className="flex flex-col font-light mb-3 xl:flex-row xl:space-x-4 ">
                  <div className="flex flex-row gap-3 w-full pb-3 xl:pb-0">
                    <EmailIcon />
                    <Text14>{user?.email}</Text14>
                  </div>
                  <div className="hidden flex flex-row gap-3 xl:flex">
                    <PhoneIcon />
                    <Text14>{user?.phone}</Text14>
                  </div>
                  <div className="flex flex-row gap-3 xl:hidden">
                    <PhoneIcon />
                    <Text14>{user?.phone}</Text14>
                  </div>
                </div>
              </div>
            </div>
            <BorderButton
              title="Edit"
              onClick={() => {
                setIsEditProfileModalOpen(true);
              }}
            />
          </div>
          <div className="flex flex-col flex-1 space-y-6 p-6 pt-0 w-full xl:pt-6">
            <TitleWithIcon
              title="Address"
              iconSrc={require("../../../public/title_icon_home.png")}
            />
            {address ? (
              <div className="flex flex-col space-y-2 xl:space-y-0">
                <Text14>{address.street1}</Text14>
                <Text14>{address.street2}</Text14>
                <Text14>
                  {address.city}, {address.province}, {address.country}
                </Text14>
                <Text14>{address.postal}</Text14>
              </div>
            ) : (
              <Text14>No Address</Text14>
            )}
            <GrayText14>
              Address cannot be modified at this stage since your technician is based on your
              location.
            </GrayText14>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerInfo;
