import { useMemo } from "react";
import SizeableImage from "../../components/atom/SizeableImage";
import GrayText14 from "../../components/atom/Texts/GrayText14";
import Text14 from "../../components/atom/Texts/base/Text14";
import Text16 from "../../components/atom/Texts/base/Text16";
import { Color } from "../../types/Color";
import { CarService } from "../../types/CarService";

interface AddonVehicleItemProps {
  carService: CarService;
  onClickAddon: () => void;
}

export const AddonVehicleItem = (props: AddonVehicleItemProps) => {
  const carText = useMemo(() => {
    return (
      props.carService.car.make +
      " " +
      props.carService.car.model +
      " " +
      props.carService.car.year +
      " " +
      (props.carService.car.color ? `(${props.carService.car.color})` : "")
    );
  }, [props.carService.car]);

  const servicesText = useMemo(() => {
    if (props.carService.services.length === 0) {
      return "No services selected";
    } else {
      return props.carService.services.map((service) => service.name).join(", ");
    }
  }, [props.carService.services]);

  const addonsText = useMemo(() => {
    if (!props.carService.addons || props.carService.addons.length === 0) {
      return "No addons selected";
    } else {
      return props.carService.addons.map((item) => item.name).join(", ");
    }
  }, [props.carService.addons]);

  return (
    <div className="flex justify-between items-center px-6 least:max-tablet_booking_summary:flex-col">
      <div className="flex flex-1 flex-row items-center w-full least:max-tablet_booking_summary:mb-5">
        <div className="flex flex-col items-center">
          <div className="w-[60px] h-[60px] sm:w-[60px] relative sm:h-[60px] rounded-lg bg-gray-300 flex items-center overflow-hidden justify-center">
            {props.carService.car.pictureUri ? (
              <img
                className="object-cover"
                src={props.carService.car.pictureUri}
                style={{ width: 60, height: 60 }}
              />
            ) : (
              <SizeableImage
                size={40}
                src={require("../../../public/car_placeholder.png")}
                style={{ opacity: 0.5, position: "relative", zIndex: 1 }}
              />
            )}
          </div>
        </div>
        <div className="flex flex-1 flex-col ml-4 least:max-tablet_booking_summary:space-y-1">
          <GrayText14>{carText}</GrayText14>
          <Text16>{servicesText}</Text16>
          <Text14>{addonsText}</Text14>
        </div>
      </div>
      <button
        className="flex space-x-2 items-center px-4 py-2 ml-5 rounded-md least:max-tablet_booking_summary:w-full least:max-tablet_booking_summary:m-0 least:max-tablet_booking_summary:py-3"
        style={{ backgroundColor: Color.ACCENT }}
        onClick={props.onClickAddon}
      >
        <div className="flex flex-row least:max-tablet_booking_summary:w-full items-center justify-center">
          <SizeableImage
            size={20}
            style={{ marginRight: 12 }}
            src={require("../../../public/addon.png")}
          />
          <Text14 className="font-medium" style={{ color: Color.WHITE }}>
            Add-ons
          </Text14>
        </div>
      </button>
    </div>
  );
};
