import axios from "axios";
import moment from "moment";
import { useRouter } from "next/router";
import { event } from "nextjs-google-analytics";
import { useMemo, useState } from "react";
import BackgroundMiddleButton from "../../components/atom/Buttons/BackgroundMiddleButton";
import SizeableImage from "../../components/atom/SizeableImage";
import GrayParagraph from "../../components/atom/Texts/GrayParagraph";
import GrayText14 from "../../components/atom/Texts/GrayText14";
import Text14 from "../../components/atom/Texts/base/Text14";
import Text16 from "../../components/atom/Texts/base/Text16";
import { getTokenContainedHeader } from "../../functions/cognito/util";
import useTimer from "../../hooks/useTimer";
import { SessionService } from "../../services/SessionService";
import { Color } from "../../types/Color";
import { CarServiceExtension } from "../../types/CarServiceExtension";
import { API_URL, TRAVEL_FEE } from "../../values";

const PaymentBlock = ({
  carServiceExtensions,
  timerHook,
  isBottomDividedInResponsive,
}: {
  carServiceExtensions: CarServiceExtension[];
  timerHook: ReturnType<typeof useTimer>;
  isBottomDividedInResponsive?: boolean;
}) => {
  const [isPaying, setIsPaying] = useState<boolean>(false);
  const router = useRouter();

  const servicesTotal = useMemo(() => {
    let result = 0;
    let isFirstService = true;

    for (let carServiceExtension of carServiceExtensions) {
      if (carServiceExtension.cars) {
        for (let carService of carServiceExtension.cars) {
          if (carService.services) {
            for (let i = 0; i < carService.services.length; i++) {
              if (isFirstService) {
                result += carService.services[i].price;
                isFirstService = false;
              } else {
                result += carService.services[i].price - TRAVEL_FEE;
              }
            }
          }
        }
      }
    }

    return result / 100;
  }, [carServiceExtensions]);

  const addonsTotal = useMemo(() => {
    let result = 0;

    for (let carServiceExtension of carServiceExtensions) {
      if (carServiceExtension.cars) {
        for (let carService of carServiceExtension.cars) {
          if (carService.addons) {
            for (let addon of carService.addons) {
              result += addon.cost;
            }
          }
        }
      }
    }

    return result / 100;
  }, [carServiceExtensions]);

  const subTotal = useMemo(() => {
    return servicesTotal + addonsTotal;
  }, [servicesTotal, addonsTotal]);

  const tax = useMemo(() => {
    return subTotal * 0.13;
  }, [subTotal]);

  const shouldTimerTextTurnRed = useMemo(() => {
    if (timerHook.nowSecond <= 60) {
      return true;
    }

    return false;
  }, [timerHook.nowSecond]);

  function formatCarServiceExentionsToCarServiceAndAddons(
    carServiceExtensions: CarServiceExtension[]
  ) {
    let carServicesAndAddons = [];

    for (let i = 0; i < carServiceExtensions.length; i++) {
      if (carServiceExtensions[i].cars) {
        for (let carService of carServiceExtensions[i].cars!) {
          carServicesAndAddons.push({
            carId: carService.car.id,
            serviceIds: carService.services ? carService.services.map((item) => item.id) : [],
            addonsWithCount: carService.addons
              ? carService.addons.map((item) => ({
                  addonId: item.id,
                  count: 1,
                }))
              : [],
          });
        }
      }
    }

    return carServicesAndAddons;
  }

  async function onClickPaymentButton() {
    try {
      setIsPaying(true);
      const carServicesAndAddons =
        formatCarServiceExentionsToCarServiceAndAddons(carServiceExtensions);
      console.log(carServicesAndAddons);
      await SessionService.createSessionAddons(carServicesAndAddons);
      event("payment", {
        category: "Payment",
        label: "Payment is clicked.",
      });
      router.push((await apiCreateBooking()).checkoutUrl);
    } catch (error) {
      console.log(error);
    } finally {
      setIsPaying(false);
    }
  }

  async function apiCreateBooking() {
    const headers = await getTokenContainedHeader();
    const createdResult = (
      await axios.post(
        API_URL + "/bookings",
        {},
        {
          headers,
        }
      )
    ).data as {
      checkoutUrl: string;
    };
    return createdResult;
  }

  function convertSecondToMinute(second: number) {
    return moment.utc(second * 1000).format("m:ss");
  }

  return !isBottomDividedInResponsive ? (
    <div className="block flex-col items-center space-y-8 xl:w-[328px] w-full min-w-[320px]">
      <div className="bg-white border border-x-0 flex flex-col xl:rounded-xl xl:shadow-[0_5px_15px_rgba(0,0,0,0.03)] xl:border-x">
        <div className="flex items-center justify-between p-5 -mt-1 xl:border-b xl:border-b-gray">
          <div className="flex space-x-2 items-center">
            <SizeableImage size={16} src={require("../../../public/hourglass.png")} />
            <GrayText14>Complete your checkout in</GrayText14>
          </div>
          <div
            className="flex items-center justify-center border border-gray rounded-full py-2"
            style={{ width: 60 }}
          >
            <Text14 style={{ color: shouldTimerTextTurnRed ? "red" : "black" }}>
              {convertSecondToMinute(timerHook.nowSecond)}
            </Text14>
          </div>
        </div>
        <div className="hidden flex-col p-5 xl:flex">
          <h6 className="text-xl font-semibold mb-3">Payment</h6>
          <GrayParagraph style={{ marginBottom: 16 }}>
            Your booking times are reserved for 5 minutes. Other customers will not be able to book
            an appointment for the same times.
          </GrayParagraph>
          <div className="border-t mb-4" />
          <div className="flex items-center justify-between mb-1">
            <Text14>Services</Text14>
            <Text14>${servicesTotal.toFixed(2)}</Text14>
          </div>
          <div className="flex items-center justify-between mb-1">
            <Text14>Add-ons</Text14>
            <Text14>${addonsTotal.toFixed(2)}</Text14>
          </div>
          <div className="flex items-center justify-between mb-1">
            <Text14>Subtotal</Text14>
            <Text14>${subTotal.toFixed(2)}</Text14>
          </div>
          <div className="flex items-center justify-between mb-4">
            <Text14>Tax</Text14>
            <Text14>${(subTotal * 0.13).toFixed(2)}</Text14>
          </div>
          <div className="border-t mb-4" />
          <div className="flex items-center justify-between mb-5">
            <Text16>Total</Text16>
            <Text16
              style={{
                color: Color.ACCENT,
              }}
            >
              ${(subTotal + tax).toFixed(2)}
            </Text16>
          </div>
          <div className="flex flex-row items-stretch">
            <BackgroundMiddleButton
              title={`Pay Now`}
              isLoading={isPaying}
              onClick={onClickPaymentButton}
              backgroundColor="DARK_BLUE"
              style={{
                width: "100%",
              }}
            />
          </div>
        </div>
      </div>
      <div className="hidden flex-col items-center space-y-3 xl:flex">
        <div className="flex flex-row items-center justify-center mb-3">
          <SizeableImage size={32} src={require("../../../public/green_shield.png")} />
        </div>
        <span
          className="flex flex-row justify-center items-center text-sm px-5 text-center break-words"
          style={{
            color: Color.GRAY,
          }}
        >
          We use Stripe for secure checkout. You will be redirected to a checkout page where you can
          enter your payment method.
        </span>
      </div>
    </div>
  ) : (
    <div className="flex-col items-center space-y-8 xl:w-[328px] w-full min-w-[320px] xl:hidden">
      <div className="bg-white border border-x-0 flex flex-col">
        <div className="flex flex-col p-6 border-0">
          <div className="flex items-center justify-between mb-2">
            <Text14>Services</Text14>
            <Text14>${servicesTotal.toFixed(2)}</Text14>
          </div>
          <div className="flex items-center justify-between mb-2">
            <Text14>Add-ons</Text14>
            <Text14>${addonsTotal.toFixed(2)}</Text14>
          </div>
          <div className="flex items-center justify-between mb-2">
            <Text14>Subtotal</Text14>
            <Text14>${subTotal.toFixed(2)}</Text14>
          </div>
          <div className="flex items-center justify-between mb-5">
            <Text14>Tax</Text14>
            <Text14>${(subTotal * 0.13).toFixed(2)}</Text14>
          </div>
          <div className="border-t mb-3" />
          <div className="flex items-center justify-between mb-6">
            <Text16>Total</Text16>
            <Text16
              style={{
                color: Color.ACCENT,
              }}
            >
              ${(subTotal + tax).toFixed(2)}
            </Text16>
          </div>
          <div className="flex flex-row items-stretch">
            <BackgroundMiddleButton
              title={`Pay Now`}
              isLoading={isPaying}
              onClick={onClickPaymentButton}
              backgroundColor="DARK_BLUE"
              style={{
                width: "100%",
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center space-y-3">
        <div className="flex flex-row items-center justify-center mb-3">
          <SizeableImage size={32} src={require("../../../public/green_shield.png")} />
        </div>
        <span
          className="flex flex-row justify-center items-center text-sm px-5 text-center break-words"
          style={{
            color: Color.GRAY,
          }}
        >
          We use Stripe for secure checkout. You will be redirected to a checkout page where you can
          enter your payment method.
        </span>
        <div style={{ height: 32 }} />
      </div>
    </div>
  );
};

export default PaymentBlock;
