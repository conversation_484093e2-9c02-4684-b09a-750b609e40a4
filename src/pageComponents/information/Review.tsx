import { useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { addAddon, deleteAddon } from "../../@redux/modules/summary";
import BookingDateDisplay from "../../components/atom/BookingDateDisplay";
import BookingDateDisplayMobile from "../../components/atom/BookingDateDisplayMobile";
import Rating from "../../components/atom/Rating";
import SizeableImage from "../../components/atom/SizeableImage";
import Text16 from "../../components/atom/Texts/base/Text16";
import TitleWithIcon from "../../components/atom/TitleWithIcon";
import StepHeader from "../../components/molecule/StepHeader";
import { convertCentToDollar } from "../../functions/local";
import { useRemoteAddon } from "../../hooks/useRemoteAddon";
import { CarServiceExtension } from "../../types/CarServiceExtension";
import { TRAVEL_FEE } from "../../values";
import AddonSelectionModal from "../../components/Modal/AddonSelectionModal";
import { AddonVehicleItem } from "./AddonVehicleItem";

interface ReviewProps {
  carServiceExtensions: CarServiceExtension[];
}

const Review = (props: ReviewProps) => {
  const dispatch = useDispatch();

  const [selectedCarServiceIndex, setSelectedCarServiceIndex] = useState<number>(-1);

  const addonHook = useRemoteAddon();

  const enableAddonSelectionModal = useMemo(() => {
    return selectedCarServiceIndex != -1;
  }, [selectedCarServiceIndex]);

  function getIndividualCarServiceExtensionTotal(carServiceExtensionId: number) {
    let total = 0;
    let isFirstService = true;

    const carServiceExtension = props.carServiceExtensions.find(
      (item) => item.id == carServiceExtensionId
    );

    if (carServiceExtension) {
      if (carServiceExtension.cars) {
        for (let car of carServiceExtension.cars) {
          if (car.services) {
            for (let service of car.services) {
              if (isFirstService) {
                total += service.price;
                isFirstService = false;
              } else {
                total += service.price - TRAVEL_FEE;
              }
            }
          }

          if (car.addons) {
            for (let addon of car.addons) {
              total += addon.cost;
            }
          }
        }
      }
    }

    return total;
  }

  return (
    <div className="bg-white xl:rounded-xl border border-x-0 border-t-0 flex flex-col xl:shadow-[0_5px_15px_rgba(0,0,0,0.03)] xl:border-x xl:border-t">
      <div className="hidden flex-col items-stretch p-6 border-b xl:flex">
        <StepHeader
          title="Review"
          description="Review your booking detailes and make a payment. You can include add-ons if needed."
        />
        {}
      </div>
      {props.carServiceExtensions.map((carServiceExtension) => (
        <div className="w-full h-full">
          {enableAddonSelectionModal && carServiceExtension.cars ? (
            <AddonSelectionModal
              isOn={addonHook.isAddonModalOn}
              onOff={() => {
                addonHook.onOffAddonModal();
                setSelectedCarServiceIndex(-1);
              }}
              addons={addonHook.remoteAddons}
              carService={carServiceExtension.cars[selectedCarServiceIndex]}
              onClickAddAddon={(addon) => {
                dispatch(
                  addAddon({
                    carServiceExtensionId: carServiceExtension.id,
                    carId: carServiceExtension.cars![selectedCarServiceIndex].car.id,
                    addon,
                  })
                );
              }}
              onClickDeleteAddon={(addon) => {
                dispatch(
                  deleteAddon({
                    carServiceExtensionId: carServiceExtension.id,
                    carId: carServiceExtension.cars![selectedCarServiceIndex].car.id,
                    addon,
                  })
                );
              }}
            />
          ) : null}
          <div className="flex flex-col items-stretch p-6 space-y-5">
            <TitleWithIcon
              title={carServiceExtension.name}
              iconSrc={require("../../../public/title_icon_tool.png")}
            />
            <div className="flex flex-col justify-between items-start xl:flex-row xl:items-center">
              <div className="flex flex-row items-center space-x-4 mb-6 xl:mb-0">
                {carServiceExtension.technician?.profilePictureUri ? (
                  <div style={{ width: 60, height: 60, borderRadius: 30, overflow: "hidden" }}>
                    <img src={carServiceExtension.technician.profilePictureUri} />
                  </div>
                ) : (
                  <div
                    className="rounded-full"
                    style={{ width: 60, height: 60, backgroundColor: "gray" }}
                  />
                )}
                <div className="flex flex-col">
                  <Text16 style={{ marginBottom: 8 }}>
                    {carServiceExtension.technician?.name}
                  </Text16>
                  <Rating rating={carServiceExtension.technician?.rating} />
                </div>
              </div>
              <div className="hidden xl:block">
                <BookingDateDisplay
                  startDateTime={carServiceExtension.timeslot?.startDateTime}
                  endDateTime={carServiceExtension.timeslot?.endDateTime}
                />
              </div>
              <div className="block w-full xl:hidden">
                <BookingDateDisplayMobile
                  startDateTime={carServiceExtension.timeslot?.startDateTime}
                  endDateTime={carServiceExtension.timeslot?.endDateTime}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col items-stretch p-6 border-t border-b">
            <div className="flex justify-between items-center">
              <div className="flex space-x-3 items-center">
                <Text16>Booking Details</Text16>
                <SizeableImage size={20} src={require("../../../public/hide.png")} />
              </div>
              <Text16>
                $
                {Number(
                  convertCentToDollar(getIndividualCarServiceExtensionTotal(carServiceExtension.id))
                ).toFixed(2)}
              </Text16>
            </div>
          </div>
          <div className="flex flex-col py-6 space-y-6 xl:space-y-5 xl:py-5">
            {carServiceExtension.cars?.map((item, index) => (
              <AddonVehicleItem
                carService={item}
                onClickAddon={() => {
                  setSelectedCarServiceIndex(index);
                  addonHook.setIsAddonModalOn(true);
                }}
              />
            ))}{" "}
          </div>
        </div>
      ))}
    </div>
  );
};

export default Review;
