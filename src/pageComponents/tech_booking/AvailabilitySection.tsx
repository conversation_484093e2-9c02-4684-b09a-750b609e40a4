import { Availability } from "../../@redux/modules/techBooking";
import Paper from "../../components/atom/Paper";
import Title18 from "../../components/atom/Texts/base/Title18";
import { Color } from "../../types/Color";
import AvailabilityBlock from "./AvailabilityBlock";

interface AvailabilitySectionProps {
  availabilities: Availability[] | undefined;
  onClick: () => void;
}

const AvailabilitySection = (props: AvailabilitySectionProps) => {
  return (
    <Paper style={{ marginBottom: 8, padding: 24 }}>
      <div className="flex justify-between mb-3">
        <Title18>Availabilities</Title18>
        <button
          title="Setting"
          onClick={props.onClick}
          style={{
            border: `1px solid ${Color.ACCENT}`,
            background: Color.ACCENT + "10",
            color: Color.ACCENT,
            paddingTop: 10,
            paddingBottom: 10,
          }}
          className="px-6 rounded-lg text-sm"
        >
          Availability Setting
        </button>
        {/* <button
                  className="flex items-center justify-center"
                  onClick={() => {}}
                  style={{
                    marginRight: 10,
                  }}
                >
                  <Image src={require("../../../public/arrow_left.png")} />
                </button>
                <button className="flex items-center justify-center" onClick={() => {}}>
                  <Image src={require("../../../public/arrow_right.png")} />
                </button> */}
      </div>
      <div className="flex items-baseline space-x-4">
        {props.availabilities?.map((item, index) => (
          <AvailabilityBlock data={item} key={index} />
        ))}
      </div>
    </Paper>
  );
};

export default AvailabilitySection;
