import moment from "moment";
import React from "react";
import { Availability } from "../../@redux/modules/techBooking";
import { Color } from "../../types/Color";

interface AvailabilityBlockProps {
  data: Availability;
}

const AvailabilityBlock = ({ data }: AvailabilityBlockProps) => {
  return (
    <button
      className="p-5 flex flex-col"
      style={{
        width: 160,
        backgroundColor: data.dayOfWeek === "Tuesday" ? Color.OASIS : Color.BLACK_004,
        border: data.dayOfWeek === "Tuesday" ? "1px solid #FCA311" : "1px solid rgba(0,0,0,0.1)",
      }}
    >
      <span
        className="font-medium mb-2"
        style={{
          fontSize: 15,
        }}
      >
        {data.dayOfWeek}
      </span>
      <div className="flex flex-col space-y-[2px]">
        {data.slots.map((item) => (
          <span className="text-sm text-[rgba(0,0,0,0.6)]">
            {moment(item.startTime).format("HH:mm")} - {moment(item.endTime).format("HH:mm")}
          </span>
        ))}
      </div>
    </button>
  );
};

export default AvailabilityBlock;
