import Head from "next/head";
import { useContext } from "react";
import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import NoPaddingPaper from "../../components/atom/NoPaddingPaper";
import SelectDateTechButton from "../../components/atom/SelectDateTechButton";
import TitleWithIcon from "../../components/atom/TitleWithIcon";
import SelectedTechInfoTab from "../../components/molecule/SelectedTechInfoTab";
import ServiceCarItem from "../../components/molecule/ServiceCarItem";
import StepHeader from "../../components/molecule/StepHeader";
import AddressSelector from "../../components/organism/AddressSelector";
import BookingStepper from "../../components/organism/BookingStepper";
import { calendarContext } from "../../contexts/CalendarContext";
import { useAddresses } from "../../hooks/useAddresses";
import SelectDateTechnicianModal from "../../components/Modal/SelectDateTechnicianModal";

const BookingCalendarContainer = () => {
  const context = useContext(calendarContext)!;
  const formAddressHook = useAddresses();

  return (
    <>
      <Head>
        <title>Select Date and Time - Wheel Easy</title>
      </Head>
      <BookingStepper step={2} />
      <div className="flex w-full flex-1 bg-white md:bg-[#F8FAFB] overflow-y-scroll h-[calc(100vh-123px)] md:min-w-[1024px] overflow-x-scroll">
        <div className="flex flex-col w-full h-full self-baseline mx-auto md:px:auto">
          <div className="flex flex-col md:justify-center items-stretch md:items-start md:flex-row h-full md:px-10">
            <div className="md:flex md:flex-row md:px-auto md:w-[1360px] h-full">
              <div className="flex flex-col md:px-10 xl:mx-auto xl:w-[1280px]">
                <div className="flex flex-col md:flex-row xl:justify-center">
                  <div className="flex flex-col items-stretch md:ml-4 mt-2 mb-8 w-full">
                    {/* <SelectDateTechnicianModal /> */}
                    <NoPaddingPaper
                      borders="rounded-br-none md:rounded-t-xl rounded-t-none rounded-bl-none md:rounded-bl-xl xl:max-w-[987px] md:mt-3"
                      style={{
                        marginBottom: 24,
                        overflow: "hidden",
                      }}
                    >
                      <div className="flex flex-col items-stretch">
                        <div
                          className="flex-row items-center hidden md:flex justify-between border-b border-b-gray p-8"
                          style={{
                            paddingBottom: 28,
                          }}
                        >
                          <StepHeader
                            title="Select Date and Time"
                            description="Select the date and technician for your services."
                          />
                          <BackgroundSmallButton
                            disabled={!context.isReservationReady}
                            isLoading={context.isProceedingToNextPage}
                            backgroundColor="DARK_BLUE"
                            title="Continue"
                            onClick={context.proceedToNextPage}
                            style={{
                              height: 40,
                            }}
                          />
                        </div>
                        <AddressSelector
                          addressHook={formAddressHook}
                          style={{
                            marginTop: 24,
                            marginBottom: 24,
                          }}
                        />
                        <div className="relative px-4 pb-8 xl:px-8">
                          {false ? (
                            <div className="absolute w-full h-full bg-[rgba(255,255,255,0.8)] z-[5]" />
                          ) : null}
                          {context.categorySessionHook.categorySessionItems.length > 0
                            ? context.categorySessionHook.categorySessionItems.map(
                                (category, index) => (
                                  <div className="flex flex-col items-stretch">
                                    <TitleWithIcon
                                      title={category.name + " Services"}
                                      iconSrc={require("../../../public/title_icon_tool.png")}
                                      style={{
                                        marginBottom: 20,
                                      }}
                                    />
                                    {category.services.map((service, index) => (
                                      <ServiceCarItem
                                        service={service}
                                        index={index}
                                        serviceLength={category.services.length}
                                      />
                                    ))}

                                    {context.isReservationNotMade ? (
                                      <SelectDateTechButton
                                        onClick={() =>
                                          context.categorySessionHook.setSelectedCategoryIndex(
                                            index,
                                          )
                                        }
                                        style={{
                                          width: "100%",
                                        }}
                                      />
                                    ) : (
                                      <SelectedTechInfoTab
                                        onClickEdit={() =>
                                          context.categorySessionHook.setSelectedCategoryIndex(
                                            index,
                                          )
                                        }
                                      />
                                    )}
                                  </div>
                                ),
                              )
                            : null}
                        </div>
                      </div>
                    </NoPaddingPaper>
                  </div>
                </div>
              </div>
              <div className="absolute left-0 bottom-0 md:hidden pt-3 w-full">
                <BackgroundSmallButton
                  disabled={!context.isReservationReady}
                  backgroundColor="ACCENT"
                  title="Continue"
                  onClick={context.proceedToNextPage}
                  style={{
                    height: 52,
                    width: "100%",
                    borderRadius: 0,
                    fontSize: 16,
                  }}
                  isLoading={context.isProceedingToNextPage}
                />
                <div className="relative px-4 pb-8 xl:px-8">
                  {false ? (
                    <div className="absolute w-full h-full bg-[rgba(255,255,255,0.8)] z-[5]" />
                  ) : null}
                  {context.categorySessionHook.categorySessionItems.length > 0
                    ? context.categorySessionHook.categorySessionItems.map((category, index) => (
                        <div className="flex flex-col items-stretch">
                          <TitleWithIcon
                            title={category.name + " Services"}
                            iconSrc={require("../../../public/title_icon_tool.png")}
                            style={{
                              marginBottom: 20,
                            }}
                          />
                          {category.services.map((service, index) => (
                            <ServiceCarItem
                              service={service}
                              index={index}
                              serviceLength={category.services.length}
                            />
                          ))}

                          {context.isReservationNotMade ? (
                            <SelectDateTechButton
                              onClick={() =>
                                context.categorySessionHook.setSelectedCategoryIndex(index)
                              }
                              style={{
                                width: "100%",
                              }}
                            />
                          ) : (
                            <SelectedTechInfoTab
                              onClickEdit={() =>
                                context.categorySessionHook.setSelectedCategoryIndex(index)
                              }
                            />
                          )}
                        </div>
                      ))
                    : null}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BookingCalendarContainer;
