import { useRouter } from "next/router";
import Paper from "../../components/atom/Paper";
import ProfileFieldValue from "../../components/atom/Texts/base/ProfileFieldValue";
import { Color } from "../../types/Color";

const PasswordBlock = () => {
  const router = useRouter();
  return (
    <Paper borders="rounded-b-lg border-t-0">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-y-6 items-start p-6">
        <p className="text-lg font-medium -mb-3 md:mb-0">Password</p>
        <div className="flex flex-col">
          <ProfileFieldValue>************</ProfileFieldValue>
          <a
            onClick={() => {
              router.push("/change_password");
            }}
            className={`text-xs text-[${Color.ACCENT}] underline cursor-pointer`}
          >
            Change password
          </a>
        </div>
      </div>
    </Paper>
  );
};

export default PasswordBlock;
