import moment, { Moment } from "moment";
import React, { createContext, useContext, useEffect, useMemo, useState } from "react";
import { globalContext } from "./GlobalContext";
import { useRouter } from "next/router";
import useAssignment from "../hooks/useAssignment";
import { useTechnicianSearch } from "../hooks/useTechnicianSearch";
import useDateTechnicianModalCalendarPicker from "../hooks/useDateTechnicianModalCalendarPicker";
import { Booking } from "../types/Booking";
import { BookingService } from "../services/BookingService";
import { usePagination } from "../hooks/usePagination";
import { ListTypeBooking } from "../types";
import { useAlertModal } from "../hooks/useAlertModal";

interface ContextValueType {
  selectDateTechnicianModalTitle: string;
  selectDateTechnicianModalButtonText: string;
  showFirstStep: boolean;
  showSecondStep: boolean;
  isSelctDateTechnicianModalOn: boolean;
  selectedMonth: Moment | null;
  selectedDate: Moment | null;
  selectedTime: Moment | null;
  getIsDateAvailable: (day: Moment) => boolean;
  onClickDate: (date: number) => void;
  onClickTime: (timeOption: string) => void;
  isWideModal: boolean;
  isLoading: boolean;
  isConfirmButtonDisabled: boolean;
  onClickConfirmButton: () => void;
  showBackButton: boolean;
  onClickBackButton: () => void;
  showCalendarPicker: boolean;
  weekDays: string[];
  generatedWeeks: (Moment | null)[][];
  isPreviousCalendarMonthDisabled: boolean;
  addMonthToSelectedTime: () => void;
  minusMonthToSelectedTime: () => void;
  availableHours: string[];
  handleOnOffSelectDateTechnicianModal: () => void;
  setBookingToEdit: (bookings: Booking[], index: number) => void;
  shouldFetchBookings: boolean;
  setShouldFetchBookings: (value: boolean) => void;
  isConfirmButtonLoading: boolean;
  setIsConfirmButtonLoading: (value: boolean) => void;
  paginationHook: ReturnType<typeof usePagination>;
  isAlertModalOn: boolean;
  setIsAlertModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  selectedBookingIndex: number;
  setSelectedBookingIndex: React.Dispatch<React.SetStateAction<number>>;
  bookings: ListTypeBooking[];
  setBookings: React.Dispatch<React.SetStateAction<ListTypeBooking[]>>;
  alertModalHook: ReturnType<typeof useAlertModal>;
  initAlertModal: (type: "SUCCESS" | "WARNING") => void;
  errorMessageFromServer: string | null;
  handleOnOffErrorAlertModal: () => void;
  router: any;
  isGoBookingLoading: boolean;
  setIsGoBookingLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const customerBookingContext = createContext<ContextValueType | null>(null);

const CustomerBookingContextProvider = ({ children }: { children: React.ReactNode }) => {
  const gContext = useContext(globalContext);
  const isMobile = useMemo(() => gContext?.isMobile || false, [gContext?.isMobile]);
  const alertModalHook = useAlertModal();

  const [bookings, setBookings] = useState<ListTypeBooking[]>([]);
  const [shouldFetchBookings, setShouldFetchBookings] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isConfirmButtonLoading, setIsConfirmButtonLoading] = useState(false);
  const [selectedBookingIndex, setSelectedBookingIndex] = useState<number>(-1);
  const [errorMessageFromServer, setErrorMessageFromServer] = useState<string | null>(null);
  const [isGoBookingLoading, setIsGoBookingLoading] = useState<boolean>(false);

  const paginationHook = usePagination();

  const router = useRouter();

  const assignmentHook = useAssignment();

  const durationWithTravelTime = useMemo(() => {
    if (selectedBooking) {
      return selectedBooking.duration + 1;
    }

    return 3;
  }, [selectedBooking]);

  const technicianSearchHook = useTechnicianSearch(
    1,
    durationWithTravelTime,
    getFSA(),
    selectedBooking,
  );

  function getFSA() {
    if (selectedBooking === null) {
      return null;
    } else if (selectedBooking.customer.address) {
      const result = selectedBooking.customer.address.postal.split(" ")[0];
      return result;
    }
    return null;
  }

  const dateTechnicianModalCalendarHook =
    useDateTechnicianModalCalendarPicker(technicianSearchHook);

  useEffect(() => {
    initAlertModal(alertModalHook.type);
  }, [alertModalHook.type]);

  async function confirmTechnician() {
    if (technicianSearchHook.technicians) {
      console.log("technicianSearchHook.technicians", technicianSearchHook.technicians);
      const assignedTech = technicianSearchHook.getAssignedTechnician();
      console.log("assignedTech", assignedTech);
      const assignedDateTime = technicianSearchHook.selectedTime;
      console.log("assignedDateTime", assignedDateTime);
      // booking rescheduling logic here
      try {
        if (selectedBooking && assignedDateTime) {
          await BookingService.reschedule(
            selectedBooking.id,
            assignedDateTime.format("YYYY-MM-DD HH:mm:ss"),
            assignedTech.technicianId,
          );
        }
        setSelectedBooking(null);
        setShouldFetchBookings(true);
        technicianSearchHook.resetSelection();

        alertModalHook.handleSetType();
        initAlertModal(alertModalHook.type);
        alertModalHook.setIsOn(true);
      } catch (error: any) {
        setErrorMessageFromServer(error.response.data.message);
        alertModalHook.resetType();
      } finally {
        setIsConfirmButtonLoading(false);
      }
    }
  }

  async function onClickConfirmButton() {
    setIsConfirmButtonLoading(true);
    if (isMobile) {
      if (dateTechnicianModalCalendarHook.step === 0) {
        dateTechnicianModalCalendarHook.setStep(1);
        setIsConfirmButtonLoading(false);
      } else {
        confirmTechnician();
      }
    } else {
      confirmTechnician();
    }
  }

  function handleOnOffSelectDateTechnicianModal() {
    setSelectedBooking(null);
    technicianSearchHook.resetSelection();
    dateTechnicianModalCalendarHook.setStep(0);
    setIsConfirmButtonLoading(false);
  }

  function setBookingToEdit(bookings: Booking[], index: number) {
    console.log("setBookingToEdit", bookings);
    console.log("setBookingToEditIndex", index);
    if (bookings === null) {
      return;
    } else {
      setSelectedBooking(bookings[index]);
    }
  }

  function initAlertModal(type: "SUCCESS" | "WARNING") {
    if (type === "WARNING") {
      alertModalHook.setTitle("Please Note!");
      alertModalHook.setDescription(
        <span>
          Rescheduling your appointment within 20 hours of the appointment time may require us to
          charge a $20 fee if the original time is not filled. Sorry about that!
          <br />
          <br />
          Please click 'I understand' to proceed.
        </span>,
      );
      alertModalHook.setIsButton(true);
      alertModalHook.setButtonTitle("I understand");
    } else if (type === "SUCCESS") {
      alertModalHook.setTitle("Reschedule completed");
      alertModalHook.setDescription(<span>Your booking has been rescheduled!</span>);
      alertModalHook.setIsButton(false);
      alertModalHook.setButtonTitle("");
    }
  }

  function handleOnOffErrorAlertModal() {
    assignmentHook.resetReservationState();
    _resetErrorMessageFromServer();
  }

  function _resetErrorMessageFromServer() {
    setErrorMessageFromServer(null);
  }

  const value: ContextValueType = {
    ...dateTechnicianModalCalendarHook,
    showSecondStep: dateTechnicianModalCalendarHook.showSecondStep,
    isSelctDateTechnicianModalOn: selectedBooking !== null,
    ...technicianSearchHook,
    isWideModal: technicianSearchHook.selectedDate !== null,
    isLoading: technicianSearchHook.isLoading || isLoading,
    onClickDate: (date: number) => technicianSearchHook.setSelectedDate(date),
    onClickTime: (timeOption: string) => {
      technicianSearchHook.setSelectedTimeOption(timeOption);
      technicianSearchHook.setSelectedTechIndex(0);
    },
    isConfirmButtonDisabled: dateTechnicianModalCalendarHook.isConfirmButtonDisabled,
    onClickConfirmButton,
    showBackButton: dateTechnicianModalCalendarHook.showBackButton,
    onClickBackButton: dateTechnicianModalCalendarHook.onClickBackButton,
    generatedWeeks: dateTechnicianModalCalendarHook.generateWeeks(),
    handleOnOffSelectDateTechnicianModal,
    setBookingToEdit,
    shouldFetchBookings,
    setShouldFetchBookings,
    isConfirmButtonLoading,
    setIsConfirmButtonLoading,
    paginationHook,
    isAlertModalOn: alertModalHook.isOn,
    setIsAlertModalOn: alertModalHook.setIsOn,
    selectedBookingIndex,
    setSelectedBookingIndex,
    bookings,
    setBookings,
    alertModalHook,
    initAlertModal,
    errorMessageFromServer,
    handleOnOffErrorAlertModal,
    router,
    isGoBookingLoading,
    setIsGoBookingLoading,
  };

  return (
    <customerBookingContext.Provider value={value}>{children}</customerBookingContext.Provider>
  );
};

export default CustomerBookingContextProvider;
