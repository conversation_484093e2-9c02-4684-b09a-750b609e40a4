import React, { createContext, useEffect } from "react";
import useWindowDimensions from "../hooks/useWindowDimensions";

interface ContextValueType {
  dimensionWidth: number | undefined;
  dimensionHeight: number | undefined;
  isMobile: boolean | undefined;
  getResponsiveModalWidthByDeviceSize: any;
  getResponsiveModalHeightByDeviceSize: any;
  getResponsiveModalFullWidthByDeviceSize: any;
  getResponsiveModalFullHeightByDeviceSize: any;
  getResponsiveModalTooltipWidth: any;
}

export const globalContext = createContext<ContextValueType | null>(null);

const GlobalContextProvider = ({ children }: { children: React.ReactNode }) => {
  const dimensionHook = useWindowDimensions();

  const value: ContextValueType = {
    dimensionWidth: dimensionHook.dimension?.width,
    dimensionHeight: dimensionHook.dimension?.height,
    isMobile: dimensionHook.isMobile,
    getResponsiveModalWidthByDeviceSize: dimensionHook.getResponsiveModalWidthByDeviceSize,
    getResponsiveModalHeightByDeviceSize: dimensionHook.getResponsiveModalHeightByDeviceSize,
    getResponsiveModalFullWidthByDeviceSize: dimensionHook.getResponsiveModalFullWidthByDeviceSize,
    getResponsiveModalFullHeightByDeviceSize: dimensionHook.getResponsiveModalFullHeightByDeviceSize,
    getResponsiveModalTooltipWidth: dimensionHook.getResponsiveModalTooltipWidth,
  };

  return <globalContext.Provider value={value}>{children}</globalContext.Provider>;
};

export default GlobalContextProvider;
