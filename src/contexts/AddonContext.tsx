import assert from "assert";
import moment from "moment";
import { useRouter } from "next/router";
import { event } from "nextjs-google-analytics";
import React, {
  MutableRefObject,
  createContext,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useSelector } from "react-redux";
import { RootState } from "../@redux";
import { useAddAddon } from "../hooks/useAddAddon";
import { useAlertModal } from "../hooks/useAlertModal";
import useAssignment from "../hooks/useAssignment";
import { useCarServices } from "../hooks/useCarServices";
import { useCarServiceExtensions } from "../hooks/useCarServicesExtensions";
import { useCarServiceExtensionsSummaryMediator } from "../hooks/useCarServicesExtensionsSummaryMediator";
import useCategorySession from "../hooks/useCategorySession";
import { useOrderSummary } from "../hooks/useOrderSummary";
import { useRemoteAddon } from "../hooks/useRemoteAddon";
import { useSeeMoreAddons } from "../hooks/useSeeMoreAddons";
import { SearchedTechnician } from "../hooks/useTechnicianSearch";
import useTimer from "../hooks/useTimer";
import { useTimerAddon } from "../hooks/useTimerAddon";
import { NewAddress } from "../services/AddressService";
import { BookingService } from "../services/BookingService";
import { OccupationService } from "../services/OccupationService";
import { SessionService } from "../services/SessionService";
import { Service } from "../types";
import { Addon } from "../types/Addon";
import { Car } from "../types/Car";
import { CarServiceExtension } from "../types/CarServiceExtension";
import { useBookingTireStored } from "../hooks/useBookingTireStored";
import { useBookingHeardFrom } from "../hooks/useBookingHeardFrom";
import { useBookingNote } from "../hooks/useBookingNote";
import BookingNote from "../pageComponents/addons_information/BookingNote";
import { globalContext } from "./GlobalContext";
import { MD } from "../values";
import { useOrderSummarySticky } from "../hooks/useOrderSummarySticky";

type AddonContextProviderType = AddAddonControlType & {
  selectedAddons: Addon[];
  setSelectedAddons: React.Dispatch<React.SetStateAction<Addon[]>>;
  findNonMatchingAddons: (displayedAddons: Addon[], selectedAddons: Addon[]) => Addon[];
  isSelectAddonModalOn: boolean;
  setIsSelectAddonModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  getIsAddonInCarServiceExtensions: (addon: Addon) => boolean;
  handleOnClickClearAddonInPage: () => void;
  displayedAddonClickedRef: MutableRefObject<any>;

  selectedCarAddonIndex: number;
  setSelectedCarAddonIndex: React.Dispatch<React.SetStateAction<number>>;

  isDispatchLoading: boolean;
  isAddonsLoading: boolean;

  seeMoreAddonsIndexArr: number[];
  setSeeMoreAddonsIndexArr: React.Dispatch<React.SetStateAction<number[]>>;

  categorySessionHook: ReturnType<typeof useCategorySession>;
  carServiceHook: ReturnType<typeof useCarServices>;
  orderSummaryHook: ReturnType<typeof useOrderSummary>;
  carServiceExtensionHook: ReturnType<typeof useCarServiceExtensions>;
  timerHook: ReturnType<typeof useTimer>;

  remoteAddons: Addon[] | undefined;
  displayedAddons: Addon[];
  setDisplayedAddons: React.Dispatch<React.SetStateAction<Addon[]>>;

  getIsAddonInArray: (target: Addon) => boolean;
  handleOnClickAddonBlock: (target: Addon) => void;
  handleOnClickAddAddon: (addons: Addon[]) => void;
  handleOnClickEditAddon: (car: Car, services: Service[], addons: Addon[]) => void;
  handleOnClickClearAddon: () => void;

  totalDurationByMin: number;
  totalDurationByHour: number;

  nowAddress: NewAddress | null;

  isReservationReady: boolean;
  assignedDateTime: moment.Moment | null;
  assignedTech: SearchedTechnician | null;

  handleOnClickPayNow: () => void;
  handleOnClickGoBack: () => void;

  isLoadingPayment: boolean;

  isLoadingGoback: boolean;

  isDisabledGoback: boolean;
  setIsDisabledGoback: React.Dispatch<React.SetStateAction<boolean>>;

  scrollableElementRef: React.RefObject<HTMLDivElement>;
  orderSummaryStickyHook: ReturnType<typeof useOrderSummarySticky>;

  isFetchLoading: boolean;

  errorMessageFromServer: string | null;
  resetErrorMessageFromServer: () => void;

  errorMessage: {
    title: string;
    description: string;
  } | null;
  setErrorMessage: React.Dispatch<
    React.SetStateAction<{
      title: string;
      description: string;
    } | null>
  >;

  deleteOccupation: () => void;
  router: any;

  isAlertModalOn: boolean;
  setIsAlertModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  handleOnClickContinueAlertModal: () => void;
  isLoadingContinueAlertModal: boolean;

  isSessionFetchErrorOccured: boolean;

  bookingTireStoredHook: ReturnType<typeof useBookingTireStored>;
  bookingHeardFromHook: ReturnType<typeof useBookingHeardFrom>;
  bookingNoteHook: ReturnType<typeof useBookingNote>;

  scrollTargetWebRef: React.RefObject<any>;
  scrollTargetMobileRef: React.RefObject<any>;

  isDisplayedAddonsStretched: boolean;
  setIsDisplayedAddonsStretched: React.Dispatch<React.SetStateAction<boolean>>;

  numOfTotalAddons: number;
};

interface AddAddonControlType {}

export const addonContext = createContext<AddonContextProviderType | null>(null);

export const AddonContextProvider = ({ children }: { children: React.ReactNode }) => {
  const gContext = useContext(globalContext)!;
  const isDispatchLoading = useSelector((state: RootState) => state.auth.isLoading);
  const carServiceHook = useCarServices();
  const orderSummaryHook = useOrderSummary();
  const carServiceExtensionsHook = useCarServiceExtensions();
  const carServiceExtensionsSummaryMediator = useCarServiceExtensionsSummaryMediator(
    orderSummaryHook,
    carServiceExtensionsHook,
  );
  const categorySessionHook = useCategorySession();
  const remoteAddonHook = useRemoteAddon();
  const reservationHook = useAssignment();
  const router = useRouter();
  const timerHook = useTimer();
  const alertModalHook = useAlertModal();
  const timerAddonHook = useTimerAddon(timerHook, alertModalHook);
  const useSeeMoreAddonsHook = useSeeMoreAddons(carServiceExtensionsHook);

  const scrollableElementRef = useRef<HTMLDivElement>(null);
  const orderSummaryStickyHook = useOrderSummarySticky(
    scrollableElementRef,
    orderSummaryHook.dynamicHeightRef,
  );

  const [nowAddress, setNowAddress] = useState<NewAddress | null>(null);
  const [nowFSA, setNowFSA] = useState<string>("");

  const [isPaying, setIsPaying] = useState<boolean>(false);
  const [isDisabledGoback, setIsDisabledGoback] = useState<boolean>(false);
  const [isLoadingGoback, setIsLoadingGoback] = useState<boolean>(false);
  const [isFetchSessionAddress, setIsFetchSessionAddress] = useState<boolean>(false);
  const [errorMessageFromServer, setErrorMessageFromServer] = useState<string | null>(null);
  const [isDisplayedAddonsStretched, setIsDisplayedAddonsStretched] = useState<boolean>(true);

  // for front error message
  const [errorMessage, setErrorMessage] = useState<{
    title: string;
    description: string;
  } | null>(null);

  const bookingTireStoredHook = useBookingTireStored();
  const bookingHeardFromHook = useBookingHeardFrom();
  const bookingNoteHook = useBookingNote();

  // for moving scroll when error occured
  const scrollTargetWebRef = useRef<any>(null);
  const scrollTargetMobileRef = useRef<any>(null);

  const [isLoadingContinueAlertModal, setIsLoadingContinueAlertModal] = useState<boolean>(false);

  const isFetchLoading =
    !categorySessionHook.categorySessionItems ||
    carServiceExtensionsHook.isLoading ||
    isFetchSessionAddress ||
    !reservationHook.assignedTech ||
    !reservationHook.assignedDateTime;

  useEffect(() => {
    fetchSessionAddressAndSetFSA();
  }, [nowFSA]);

  useEffect(() => {
    if (reservationHook.isReservationNotMade) {
      fetchCategorySessionAndReservationData();
    }

    async function fetchCategorySessionAndReservationData() {
      assert(reservationHook.isReservationNotMade);

      const categorySession = await SessionService.getCategorySession();
      if (categorySession !== "" && categorySession.length > 0 && categorySession[0].services) {
        if (categorySession[0].technician) {
          reservationHook.setAssignedTech(categorySession[0].technician!);
          reservationHook.setAssignedDateTime(moment(categorySession[0].timeslot?.startDateTime));
        }
        categorySessionHook.setCategorySessionItems(categorySession);
      } else {
        router.push("/");
      }
    }
  }, [reservationHook.assignedDateTime]);

  const addAddonHook = useAddAddon({
    onAddAddons: (addons: Addon[]) => {
      if (carServiceExtensionsHook.carServices) {
        carServiceExtensionsHook.setCarServicesExtensions((prev) => {
          if (!prev) {
            return prev;
          }

          let tmp = { ...prev[0] };
          tmp.cars![carServiceExtensionsHook.selectedCarAddonIndex] = {
            ...tmp.cars![carServiceExtensionsHook.selectedCarAddonIndex],
            addons: [...addons],
          };
          const result = [tmp];

          return result;
        });
      }
    },
    getIsCarAlreadySelected: (carId: number) =>
      carServiceHook.carServices.findIndex((item) => item.car.id === carId) !== -1,
    getIsAddonInCarServiceExtensions: (addon: Addon) => {
      if (carServiceExtensionsHook.carServices) {
        const selectedCarAddons =
          carServiceExtensionsHook.carServices[carServiceExtensionsHook.selectedCarAddonIndex]
            ?.addons || [];
        return selectedCarAddons.some((item) => item.id === addon.id);
      }

      return false;
    },
    onClearAddons: () => {
      if (carServiceExtensionsHook.carServices) {
        carServiceExtensionsHook.setCarServicesExtensions((prev) => {
          if (!prev) {
            return prev;
          }

          let tmp = { ...prev[0] };
          tmp.cars![carServiceExtensionsHook.selectedCarAddonIndex] = {
            ...tmp.cars![carServiceExtensionsHook.selectedCarAddonIndex],
            addons: [],
          };
          const result = [tmp];

          return result;
        });
      }
    },
  });

  async function fetchSessionAddressAndSetFSA() {
    try {
      setIsFetchSessionAddress(true);
      let sessionAddress: NewAddress | "";
      let postal;
      let fsa;

      sessionAddress = await SessionService.getSessionAddress();
      if (sessionAddress && sessionAddress !== null) {
        postal = sessionAddress["postal"];
        fsa = postal.split(" ")[0];
        setNowFSA(fsa);
        setNowAddress(sessionAddress);
      } else {
        setNowFSA("");
        setNowAddress(null);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsFetchSessionAddress(false);
    }
  }

  async function handleOnClickPayNow() {
    console.log("handleOnClickPayNow you're in the right spot!");
    function _validateBookingData(
      bookingTireStoredHook: ReturnType<typeof useBookingTireStored>,
      bookingHeardFromHook: ReturnType<typeof useBookingHeardFrom>,
    ) {
      let isValidBookingTireStored, isValidBookingHeardFrom;
      if (bookingTireStoredHook.bookingTireStored.length === 0) {
        bookingTireStoredHook.handleError();
        isValidBookingTireStored = false;
      } else {
        bookingTireStoredHook.handleResetError();
        isValidBookingTireStored = true;
      }

      if (bookingHeardFromHook.bookingHeardFrom.length === 0) {
        bookingHeardFromHook.handleError();
        isValidBookingHeardFrom = false;
      } else {
        bookingHeardFromHook.handleResetError();
        isValidBookingHeardFrom = true;
      }

      if (isValidBookingTireStored && isValidBookingHeardFrom) {
        return true;
      } else {
        _moveScollIntoTarget(gContext.dimensionWidth, {
          webRef: scrollTargetWebRef,
          mobileRef: scrollTargetMobileRef,
        });

        setErrorMessage({
          title: "Incomplete Checklist Form",
          description:
            "Some fields haven't been filled in for the checklist form below. Please fill in the mandatory fields (*) and try again!",
        });
        return false;
      }
    }

    if (!_validateBookingData(bookingTireStoredHook, bookingHeardFromHook)) {
      return;
    }

    try {
      if (carServiceExtensionsHook.carServicesExtensions) {
        setIsPaying(true);
        setIsDisabledGoback(true);

        const carServicesAndAddons = formatCarServiceExentionsToCarServiceAndAddons(
          carServiceExtensionsHook.carServicesExtensions,
        );

        console.log("carServicesAndAddons", carServicesAndAddons);

        await SessionService.createSessionAddons(carServicesAndAddons);

        console.log("SessionService.createSessionAddons Success");

        event("payment", {
          category: "Payment",
          label: "Payment is clicked.",
        });

        await router.push(
          (
            await BookingService.create(
              bookingTireStoredHook.bookingTireStored,
              bookingHeardFromHook.bookingHeardFrom,
              bookingNoteHook.bookingNote,
            )
          ).checkoutUrl,
        );
      }
    } catch (error: any) {
      console.log(error);

      if (error.response && error.response.data) {
        setErrorMessageFromServer(error.response.data.message);
      }
    } finally {
      setIsPaying(false);
    }

    function formatCarServiceExentionsToCarServiceAndAddons(
      carServiceExtensions: CarServiceExtension[],
    ) {
      let carServicesAndAddons = [];

      for (let i = 0; i < carServiceExtensions.length; i++) {
        if (carServiceExtensions[i].cars) {
          for (let carService of carServiceExtensions[i].cars!) {
            carServicesAndAddons.push({
              carId: carService.car.id,
              serviceIds: carService.services ? carService.services.map((item) => item.id) : [],
              addonsWithCount: carService.addons
                ? carService.addons.map((item) => ({
                    addonId: item.id,
                    count: 1,
                  }))
                : [],
            });
          }
        }
      }

      return carServicesAndAddons;
    }
  }

  function _moveScollIntoTarget(
    deviceWidth: number | undefined,
    target: {
      webRef: React.RefObject<any>;
      mobileRef: React.RefObject<any>;
    },
  ) {
    if (deviceWidth !== undefined) {
      if (deviceWidth >= MD) {
        if (target.webRef.current) {
          target.webRef.current.scrollIntoView({ behavior: "smooth" });
        }
      } else {
        if (target.mobileRef.current) {
          target.mobileRef.current.scrollIntoView({ behavior: "smooth" });
        }
      }
    } else {
      return;
    }
  }

  async function handleOnClickGoBack() {
    setIsLoadingGoback(true);
    await router.push("/booking_date");
    setIsLoadingGoback(false);
  }

  function resetErrorMessageFromServer() {
    setErrorMessageFromServer(null);
  }

  async function deleteOccupation() {
    try {
      const data = await OccupationService.delete();
      console.log(data);
    } catch (error) {
      console.log(error);
    }
  }

  async function handleOnClickContinueAlertModal() {
    setIsLoadingContinueAlertModal(true);
    await deleteOccupation();
    alertModalHook.setIsOn(false);
    setIsLoadingContinueAlertModal(false);
    router.back();
  }

  const value: AddonContextProviderType = {
    selectedAddons: addAddonHook.selectedAddons,
    setSelectedAddons: addAddonHook.setSelectedAddons,
    findNonMatchingAddons: addAddonHook.findNonMatchingAddons,
    isSelectAddonModalOn: addAddonHook.isSelectAddonModalOn,
    setIsSelectAddonModalOn: addAddonHook.setIsSelectAddonModalOn,
    getIsAddonInCarServiceExtensions: addAddonHook.getIsAddonInCarServiceExtensions,
    handleOnClickClearAddonInPage: addAddonHook.handleOnClickClearAddonInPage,
    displayedAddonClickedRef: addAddonHook.displayedAddonClickedRef,

    selectedCarAddonIndex: carServiceExtensionsHook.selectedCarAddonIndex,
    setSelectedCarAddonIndex: carServiceExtensionsHook.setSelectedCarAddonIndex,

    isAddonsLoading: carServiceExtensionsHook.isLoading,
    isDispatchLoading,

    seeMoreAddonsIndexArr: useSeeMoreAddonsHook.seeMoreAddonsIndexArr,
    setSeeMoreAddonsIndexArr: useSeeMoreAddonsHook.setSeeMoreAddonsIndexArr,

    timerHook,
    categorySessionHook,
    carServiceHook,
    orderSummaryHook,
    carServiceExtensionHook: carServiceExtensionsHook,

    remoteAddons: remoteAddonHook.remoteAddons,
    displayedAddons: remoteAddonHook.displayedAddons,
    setDisplayedAddons: remoteAddonHook.setDisplayedAddons,

    getIsAddonInArray: addAddonHook.getIsAddonInArray,
    handleOnClickAddonBlock: addAddonHook.handleOnClickAddonBlock,
    handleOnClickAddAddon: addAddonHook.handleOnClickAddAddon,
    handleOnClickEditAddon: addAddonHook.handleOnClickEditAddon,
    handleOnClickClearAddon: addAddonHook.handleOnClickClearAddon,

    totalDurationByMin: carServiceHook.calcTotalDuration,
    totalDurationByHour: carServiceHook.calcTotalDuration / 60,

    nowAddress,

    isReservationReady: reservationHook.isReservationReady,
    assignedDateTime: reservationHook.assignedDateTime,
    assignedTech: reservationHook.assignedTech,

    handleOnClickPayNow,
    handleOnClickGoBack,

    isLoadingPayment: isPaying,

    isLoadingGoback,

    isDisabledGoback,
    setIsDisabledGoback,

    scrollableElementRef,
    orderSummaryStickyHook,

    isFetchLoading,

    errorMessageFromServer,
    resetErrorMessageFromServer,

    errorMessage,
    setErrorMessage,

    deleteOccupation,
    router,

    isAlertModalOn: alertModalHook.isOn,
    setIsAlertModalOn: alertModalHook.setIsOn,
    handleOnClickContinueAlertModal,
    isLoadingContinueAlertModal,

    isSessionFetchErrorOccured: carServiceExtensionsHook.isSessionFetchErrorOccured,

    bookingTireStoredHook,
    bookingHeardFromHook,
    bookingNoteHook,

    scrollTargetWebRef,
    scrollTargetMobileRef,

    isDisplayedAddonsStretched,
    setIsDisplayedAddonsStretched,

    numOfTotalAddons: carServiceExtensionsHook.totalAddons,
  };

  return <addonContext.Provider value={value}>{children}</addonContext.Provider>;
};

export default AddonContextProvider;
