import { createContext, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../@redux";
import { useRouter } from "next/router";
import { useAddresses } from "../hooks/useAddresses";
import { useAddressForm } from "../hooks/useAddressForm";
import { useCarServices } from "../hooks/useCarServices";
import { useOrderSummary } from "../hooks/useOrderSummary";
import { ServiceContent } from "../types/ServiceContent";
import { NewAddress } from "../services/AddressService";
import { SessionService } from "../services/SessionService";
import { User } from "../types/User";
import { useCarServicesSummaryMediator } from "../hooks/useCarServicesSummaryMediator";
import { useAlertModal } from "../hooks/useAlertModal";

interface ContextValueType {
  user: User | undefined;
  isDispatchLoading: boolean;
  addressHook: ReturnType<typeof useAddresses>;
  isAddressExisted: boolean;
  isUsingAddress: boolean;
  isAddressItemSelected: (index: number) => boolean;
  openAddModal: () => void;
  openEditModal: (index: number) => void;
  openDeleteModal: (index: number) => void;
  selectAddress: (index: number) => void;
  isAddressFetchLoading: boolean;
  addresses: NewAddress[];
  isCarServiceExisted: boolean;
  orderSummaryHook: any;
  addressFormHook: any;
  isProceedingToNextPage: boolean;
  isLoadingGoback: boolean;
  goBackPage: () => void;
  proceedToNextPage: () => Promise<void>;
  scrollableElementRef: React.RefObject<HTMLDivElement>;
  isFetchLoadingOrderSummary: boolean;
  alertModalHook: ReturnType<typeof useAlertModal>;
}

export const addressContext = createContext<ContextValueType | null>(null);

const AddressContextProvider = ({ children }: { children: React.ReactNode }) => {
  const user = useSelector((state: RootState) => state.auth.user);
  const isDispatchLoading = useSelector((state: RootState) => state.auth.isLoading);
  const router = useRouter();
  const addressHook = useAddresses();
  const addressFormHook = useAddressForm({
    name: user?.firstName + " " + user?.lastName || "",
    phone: user?.phone || "",
  });

  const [isLoadingGoback, setIsLoadingGoback] = useState<boolean>(false);
  const [isProceedingToNextPage, setIsProceedingToNextPage] = useState<boolean>(false);
  const [isFetchLoading, setIsFetchLoading] = useState<boolean>(false);

  const carServiceHook = useCarServices();
  const orderSummaryHook = useOrderSummary();
  const carServicesSummaryMediatorHook = useCarServicesSummaryMediator(
    orderSummaryHook,
    carServiceHook
  );
  const alertModalHook = useAlertModal();

  const scrollableElementRef = useRef<HTMLDivElement>(null);

  function isAddressExisted(addresses: NewAddress[]) {
    if (addresses.length !== 0 && addresses) {
      return true;
    } else return false;
  }

  async function goBackPage() {
    setIsLoadingGoback(true);
    await router.push("/");
    setIsLoadingGoback(false);
  }

  function setAlertModalContent(errorMsg: string) {
    if (errorMsg === "Invalid postal code") {
      alertModalHook.setIsOn(true);
      alertModalHook.setTitle("Please Note!.");
      alertModalHook.setDescription(
        <>
          Currently, we don't support this location for booking. However, we've just sent
          you an email outlining our exciting plans to expand to more locations. Thanks for your
          interest!
        </>
      );
      alertModalHook.setType("WARNING");
    } else return;
  }

  async function proceedToNextPage() {
    if (addressHook.selectedAddressIndex !== -1) {
      setIsProceedingToNextPage(true);
      try {
        await SessionService.updateSessionAddress(
          addressHook.addresses[addressHook.selectedAddressIndex].addressId
        );
        await router.push("/booking_date");
      } catch (error: any) {
        console.log(error.response.data.message);
        if (error.response.data.message === "Invalid postal code") {
          setAlertModalContent(error.response.data.message);
          //TO DO : Send email to dennis.
        } else {
          alert("An error occurred while updating your address. Please try again later.");
        }
        console.error("session address update failed");
      } finally {
        setIsProceedingToNextPage(false);
      }
    } else {
      throw new Error("Address isn't selected");
    }
  }

  function addressItemSelected(index: number) {
    if (addressHook.addresses.length === 1) {
      return true;
    } else {
      if (addressHook.selectedAddressIndex === index) {
        return true;
      } else return false;
    }
  }

  const value: ContextValueType = {
    user,

    isDispatchLoading,

    addressHook,
    addresses: addressHook.addresses,
    isAddressExisted: isAddressExisted(addressHook.addresses),
    isUsingAddress: addressHook.selectedAddressIndex !== -1,
    isAddressItemSelected: addressItemSelected,

    openAddModal: addressHook.openAddModal,
    openEditModal: addressHook.openEditModal,
    openDeleteModal: addressHook.openDeleteModal,
    selectAddress: addressHook.select,
    isAddressFetchLoading: addressHook.isFetchLoading,

    isCarServiceExisted: carServiceHook.carServices.length > 0,

    orderSummaryHook,
    addressFormHook,

    isProceedingToNextPage,
    isLoadingGoback,
    goBackPage,
    proceedToNextPage,

    scrollableElementRef,
    isFetchLoadingOrderSummary:
      carServiceHook.isLoading || orderSummaryHook.serviceCars === undefined,
    alertModalHook,
  };

  return <addressContext.Provider value={value}>{children}</addressContext.Provider>;
};

export default AddressContextProvider;
