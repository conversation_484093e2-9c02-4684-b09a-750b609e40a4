import assert from "assert";
import moment from "moment";
import { useRouter } from "next/router";
import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../@redux";
import useAssignment from "../hooks/useAssignment";
import { useCarServices } from "../hooks/useCarServices";
import useCategorySession from "../hooks/useCategorySession";
import useDateTechnicianModalCalendarPicker from "../hooks/useDateTechnicianModalCalendarPicker";
import { useOrderSummary } from "../hooks/useOrderSummary";
import useSelectedAddress from "../hooks/useSelectedAddress";
import { SearchedTechnician, useTechnicianSearch } from "../hooks/useTechnicianSearch";
import { NewAddress } from "../services/AddressService";
import { OccupationService } from "../services/OccupationService";
import { SessionService } from "../services/SessionService";
import { CustomError } from "../types/CustomError";
import { ServiceContent } from "../types/ServiceContent";
import { globalContext } from "./GlobalContext";
import { useCarServicesSummaryMediator } from "../hooks/useCarServicesSummaryMediator";

interface ContextValueType {
  isReservationReady: boolean;
  isReservationNotMade: boolean;
  categorySessionHook: ReturnType<typeof useCategorySession>;
  isConfirmButtonDisabled: boolean;
  onClickConfirmButton: () => void;
  onClickBackButton: () => void;
  proceedToNextPage: any;
  isProceedingToNextPage: boolean;
  goBackPage: () => void;
  isLoadingGoback: boolean;
  assignedTech: SearchedTechnician | null;
  assignedDateTime: moment.Moment | null;
  setAssignedTech: (tech: SearchedTechnician) => void;
  setAssignedDateTime: (dateTime: moment.Moment | null) => void;
  confirmTechnician: () => void;
  technicianSearchHook: ReturnType<typeof useTechnicianSearch>;
  step: number;
  setStep: (step: number) => void;

  showFirstStep: boolean;
  showSecondStep: boolean;

  reviewTechIndex: number;
  setReviewTechIndex: (index: number) => void;
  selectDateTechnicianModalTitle: string;
  selectDateTechnicianModalButtonText: string;
  handleOnOffSelectDateTechnicianModal: () => void;
  nowAddress: NewAddress | null;
  weekDays: string[];
  firstDayOfMonth: moment.Moment;
  lastDayOfMonth: moment.Moment;
  generateWeeks: () => (moment.Moment | null)[][];
  totalDurationByMin: number;
  totlaDurationByHour: number;
  orderSummaryHook: any;
  isDispatchLoading: boolean;
  availableHours: string[];
  isPreviousCalendarMonthDisabled: boolean;
  scrollableElementRef: React.RefObject<HTMLDivElement>;
  isFetchLoading: boolean;
  showBackButton: boolean;
  errorMessageFromServer: string | null;
  handleOnOffErrorAlertModal: () => void;
}

export const calendarContext = createContext<ContextValueType | null>(null);

const CalendarContextProvider = ({ children }: { children: React.ReactNode }) => {
  const gContext = useContext(globalContext);
  const isMobile = useMemo(() => gContext?.isMobile || false, [gContext?.isMobile]);
  const router = useRouter();
  const isDispatchLoading = useSelector((state: RootState) => state.auth.isLoading);
  const categorySessionHook = useCategorySession();
  const assignmentHook = useAssignment();
  const selectedAddressHook = useSelectedAddress();
  const technicianSearchHook = useTechnicianSearch(
    categorySessionHook.getSelectedCategory()?.id,
    categorySessionHook.getSelectedCategoryTotalDuration() + 1,
    selectedAddressHook.getFSA(),
    null,
  );
  const dateTechnicianModalCalendarHook =
    useDateTechnicianModalCalendarPicker(technicianSearchHook);
  const carServiceHook = useCarServices();
  const orderSummaryHook = useOrderSummary();
  const carServicesSummaryMediatorHook = useCarServicesSummaryMediator(
    orderSummaryHook,
    carServiceHook,
  );

  const scrollableElementRef = useRef<HTMLDivElement>(null);
  const [isCategorySessionFetching, setIsCategorySessionFetching] = useState<boolean>(false);
  const [errorMessageFromServer, setErrorMessageFromServer] = useState<string | null>(null);

  const isFetchLoading =
    carServiceHook.isLoading ||
    selectedAddressHook.isLoading ||
    !categorySessionHook.categorySessionItems ||
    isCategorySessionFetching;

  const [isProceedingToNextPage, setIsProceedingToNextPage] = useState<boolean>(false);
  const [isLoadingGoback, setIsLoadingGoback] = useState<boolean>(false);

  useEffect(() => {
    if (assignmentHook.isReservationNotMade) {
      fetchCategorySessionAndReservationData();
    }

    async function fetchCategorySessionAndReservationData() {
      setIsCategorySessionFetching(true);
      assert(assignmentHook.isReservationNotMade);

      setTimeout(async () => {
        const categorySession = await SessionService.getCategorySession();
        console.log("categorySession", categorySession);
        if (categorySession !== "" && categorySession.length > 0 && categorySession[0].services) {
          if (categorySession[0].technician) {
            assignmentHook.setAssignedTech(categorySession[0].technician!);
            assignmentHook.setAssignedDateTime(moment(categorySession[0].timeslot?.startDateTime));
          }
          categorySessionHook.setCategorySessionItems(categorySession);
          setIsCategorySessionFetching(false);
        } else {
          router.push("/");
        }
      }, 500);
    }
  }, [assignmentHook.assignedDateTime]);

  async function onClickConfirmButton() {
    if (isMobile) {
      if (dateTechnicianModalCalendarHook.step === 0) {
        dateTechnicianModalCalendarHook.setStep(1);
      } else {
        confirmTechnician();
      }
    } else {
      confirmTechnician();
    }
  }

  async function proceedToNextPage() {
    console.log(
      "assignmentHook.assignedTech!.technicianId",
      assignmentHook.assignedTech!.technicianId,
    );
    if (assignmentHook.isReservationReady) {
      setIsProceedingToNextPage(true);
      let result = [];
      let serviceIds = [];

      for (let i = 0; i < categorySessionHook.categorySessionItems[0].services.length; i++) {
        serviceIds.push({
          id: categorySessionHook.categorySessionItems[0].services[i].id,
          count: categorySessionHook.categorySessionItems[0].services[i].cars.length,
        });
      }

      result.push({
        technicianId: assignmentHook.assignedTech!.technicianId,
        categoryId: 1,
        startDateTime: assignmentHook.assignedDateTime!.format("YYYY-MM-DD HH:mm:ss"),
        serviceIds,
        timezoneName: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });

      try {
        await OccupationService.create(result);
        router.push("/addons_information");
      } catch (error: any) {
        console.log(error);
        setIsProceedingToNextPage(false);

        if (error.response && error.response.data) {
          setErrorMessageFromServer(error.response.data.message);
        }
      }
    } else {
      new CustomError("processToNextPage", "Technician or time block isn't selected").throw();
    }
  }

  async function goBackPage() {
    setIsLoadingGoback(true);
    router.push("/booking_address");
    setIsLoadingGoback(false);
  }

  function confirmTechnician() {
    if (technicianSearchHook.technicians) {
      console.log("technicianSearchHook.selectedTechIndex", technicianSearchHook.selectedTechIndex);
      console.log("technicianSearchHook.technicians", technicianSearchHook.technicians);
      assignmentHook.setAssignedTech(
        technicianSearchHook.technicians[technicianSearchHook.selectedTechIndex],
      );
      assignmentHook.setAssignedDateTime(technicianSearchHook.selectedTime);
      technicianSearchHook.resetSelection();
      dateTechnicianModalCalendarHook.setStep(0);
      categorySessionHook.setSelectedCategoryIndex(-1); // This turns off the SelectDateTechnicianModal
    }
  }

  const selectDateTechnicianModalTitle = useMemo(() => {
    if (isMobile) {
      if (dateTechnicianModalCalendarHook.step === 0) {
        return "Select a Day";
      } else if (dateTechnicianModalCalendarHook.step === 1) {
        return "Select a Time";
      } else if (dateTechnicianModalCalendarHook.step === 2) {
        return "Technician That Are Available";
      }
    } else {
      return "Select Date & Time";
    }

    return "";
  }, [isMobile, dateTechnicianModalCalendarHook.step]);

  const selectDateTechnicianModalButtonText = useMemo(() => {
    if (isMobile) {
      if (dateTechnicianModalCalendarHook.step === 0) {
        return "Select a Day";
      } else if (dateTechnicianModalCalendarHook.step === 1) {
        return "Select a Time";
      } else if (dateTechnicianModalCalendarHook.step === 2) {
        return "Confirm Schedule";
      }
    } else {
      return "Confirm";
    }

    return "";
  }, [isMobile, dateTechnicianModalCalendarHook.step]);

  function handleOnOffSelectDateTechnicianModal() {
    categorySessionHook.setSelectedCategoryIndex(-1);
    technicianSearchHook.resetSelection();
    dateTechnicianModalCalendarHook.setStep(0);
  }

  function handleOnOffErrorAlertModal() {
    assignmentHook.resetReservationState();
    _resetErrorMessageFromServer();
  }

  function _resetErrorMessageFromServer() {
    setErrorMessageFromServer(null);
  }

  const value: ContextValueType = {
    isReservationReady: assignmentHook.isReservationReady,
    isReservationNotMade: assignmentHook.isReservationNotMade,
    categorySessionHook,
    isConfirmButtonDisabled: dateTechnicianModalCalendarHook.isConfirmButtonDisabled,
    onClickConfirmButton,
    onClickBackButton: dateTechnicianModalCalendarHook.onClickBackButton,
    proceedToNextPage,
    isProceedingToNextPage: isProceedingToNextPage,
    isLoadingGoback: isLoadingGoback,
    goBackPage,
    assignedTech: assignmentHook.assignedTech,
    assignedDateTime: assignmentHook.assignedDateTime,
    setAssignedTech: assignmentHook.setAssignedTech,
    setAssignedDateTime: assignmentHook.setAssignedDateTime,
    confirmTechnician,
    step: dateTechnicianModalCalendarHook.step,
    setStep: dateTechnicianModalCalendarHook.setStep,

    showFirstStep: dateTechnicianModalCalendarHook.showFirstStep,
    showSecondStep: dateTechnicianModalCalendarHook.showSecondStep,

    technicianSearchHook,
    reviewTechIndex: dateTechnicianModalCalendarHook.reviewTechIndex,
    setReviewTechIndex: dateTechnicianModalCalendarHook.setReviewTechIndex,
    selectDateTechnicianModalTitle,
    selectDateTechnicianModalButtonText,
    handleOnOffSelectDateTechnicianModal,
    nowAddress: selectedAddressHook.selectedAddress,

    weekDays: dateTechnicianModalCalendarHook.weekDays,
    firstDayOfMonth: dateTechnicianModalCalendarHook.firstDayOfMonth,
    lastDayOfMonth: dateTechnicianModalCalendarHook.lastDayOfMonth,
    generateWeeks: dateTechnicianModalCalendarHook.generateWeeks,

    totalDurationByMin: carServiceHook.calcTotalDuration,
    totlaDurationByHour: carServiceHook.calcTotalDuration / 60,

    orderSummaryHook: orderSummaryHook,

    isDispatchLoading: isDispatchLoading,
    availableHours: technicianSearchHook.availableHours,
    isPreviousCalendarMonthDisabled:
      dateTechnicianModalCalendarHook.isPreviousCalendarMonthDisabled,

    scrollableElementRef,
    isFetchLoading,
    showBackButton: dateTechnicianModalCalendarHook.showBackButton,
    errorMessageFromServer,
    handleOnOffErrorAlertModal,
  };

  return <calendarContext.Provider value={value}>{children}</calendarContext.Provider>;
};

export default CalendarContextProvider;
