import { NextRouter, useRouter } from "next/router";
import { createContext, useEffect, useMemo, useRef, useState } from "react";
import { useAddCarService } from "../hooks/useAddCarService";
import { CarServiceWithoutAddon, useCarServices } from "../hooks/useCarServices";
import { useContinueButton } from "../hooks/useCarServices/useContinueButton";
import { useRemoteService } from "../hooks/useRemoteService";
import { Service } from "../types";
import { Addon } from "../types/Addon";
import { Car } from "../types/Car";
import { useOrderSummary } from "../hooks/useOrderSummary";
import { ServiceContent } from "../types/ServiceContent";
import { useCarServicesSummaryMediator } from "../hooks/useCarServicesSummaryMediator";
import { useSeeMoreServices } from "../hooks/useSeeMoreServices";

export interface CarService extends CarServiceWithoutAddon {
  addons: Addon[];
}
type CarServiceContextProviderType = AddCarServiceControlType & {
  router: NextRouter;
  orderSummaryHook: ReturnType<typeof useOrderSummary>;
  carServices: CarServiceWithoutAddon[];
  handleOnClickAddService: (services: Service[]) => void;

  setByCars: (cars: Car[]) => void;
  isLoading: boolean;
  carServiceHook: ReturnType<typeof useCarServices>;
  continueButtonHook: ReturnType<typeof useContinueButton>;

  remoteServices: Service[] | undefined;
  isOrderSummaryFolded: boolean;
  setIsOrderSummaryFolded: React.Dispatch<React.SetStateAction<boolean>>;

  handleOnClickDeleteCarService: (index: number) => void;
  orderSummaryPrices: {
    servicePrice: number;
    travelFees: number;
    totalPrice: number;
  };
  scrollableElementRef: React.RefObject<HTMLDivElement>;
};

interface AddCarServiceControlType {
  ownedCarArray: Car[] | undefined;
  isAddCarContinuable: boolean;
  setOwnedCarArray: React.Dispatch<React.SetStateAction<Car[] | undefined>>;
  selectedCar: Car | undefined;
  selectedServices: Service[];
  selectedCarServiceIndex: number;
  setSelectedCarServiceIndex: React.Dispatch<React.SetStateAction<number>>;
  setSelectedServices: React.Dispatch<React.SetStateAction<Service[]>>;
  isAddCarModalOn: boolean;
  setIsAddCarModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  isSelectServiceModalOn: boolean;
  setIsSelectServiceModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  isSelectServiceUpdateModalOn: boolean;
  setIsSelectServiceUpdateModalOn: React.Dispatch<React.SetStateAction<boolean>>;
  removingCarServiceIndex: number;
  setRemovingCarServiceIndex: React.Dispatch<React.SetStateAction<number>>;
  fetchCar: () => Promise<void>;
  isLoadingFetchCar: boolean;
  getIsServiceInArray: (target: Service) => boolean;
  handleOnClickOwnedCarOnAddNewCarModal: (target: Car) => void;
  handleOnClickContinueOnAddNewCarModal: () => void;
  handleOnClickGoBackToSelectCar: () => void;
  handleOnClickServiceBlock: (target: Service) => void;
  handleOnClickEditService: (car: Car, services: Service[]) => void;
  handleOnClickClearService: (car: Car) => void;
  seeMoreServiceIndexArr: number[];
  setSeeMoreServiceIndexArr: React.Dispatch<React.SetStateAction<number[]>>;
  totalDuration: number;
  notSelectedOwnedCars: Car[] | undefined;
}

export const carServiceContext = createContext<CarServiceContextProviderType | null>(null);

export const CarServiceContextProvider = ({ children }: { children: React.ReactNode }) => {
  const carServiceHook = useCarServices();
  const addCarServiceHook = useAddCarService(carServiceHook);

  const orderSummaryHook = useOrderSummary();
  const carServicesSummaryMediatorHook = useCarServicesSummaryMediator(
    orderSummaryHook,
    carServiceHook
  );

  const router = useRouter();
  const continueButtonHook = useContinueButton(carServiceHook.carServices);
  const [isOrderSummaryFolded, setIsOrderSummaryFolded] = useState<boolean>(false);
  const [removingCarServiceIndex, setRemovingCarServiceIndex] = useState<number>(-1);
  const seeMoreServicesHook = useSeeMoreServices(carServiceHook);

  const scrollableElementRef = useRef<HTMLDivElement>(null);

  const remoteServiceHook = useRemoteService();

  function handleOnClickDeleteCarService(index: number) {
    carServiceHook.deleteByIndex(index);
  }

  const value: CarServiceContextProviderType = {
    router: router,
    orderSummaryHook,

    carServices: carServiceHook.carServices,

    setByCars: carServiceHook.setByCars,
    isLoading: carServiceHook.isLoading,
    carServiceHook,
    continueButtonHook,

    remoteServices: remoteServiceHook.remoteServices,
    isOrderSummaryFolded,
    setIsOrderSummaryFolded,

    seeMoreServiceIndexArr: seeMoreServicesHook.seeMoreServiceIndexArr,
    setSeeMoreServiceIndexArr: seeMoreServicesHook.setSeeMoreServiceIndexArr,

    removingCarServiceIndex,
    setRemovingCarServiceIndex,

    handleOnClickDeleteCarService,
    orderSummaryPrices: carServiceHook.orderSummaryPrices,

    totalDuration: carServiceHook.calcTotalDuration,

    handleOnClickAddService: addCarServiceHook.handleOnClickAddService,
    isAddCarContinuable: addCarServiceHook.isContinuable,
    selectedCar: addCarServiceHook.selectedCar,
    selectedServices: addCarServiceHook.selectedServices,
    selectedCarServiceIndex: addCarServiceHook.selectedCarServiceIndex,
    setSelectedCarServiceIndex: addCarServiceHook.setSelectedCarServiceIndex,
    setSelectedServices: addCarServiceHook.setSelectedServices,

    ownedCarArray: addCarServiceHook.ownedCarArray,
    setOwnedCarArray: addCarServiceHook.setOwnedCarArray,
    fetchCar: addCarServiceHook.fetchCar,
    isLoadingFetchCar: addCarServiceHook.isLoadingFetchCar,

    isAddCarModalOn: addCarServiceHook.isAddCarModalOn,
    setIsAddCarModalOn: addCarServiceHook.setIsAddCarModalOn,
    isSelectServiceModalOn: addCarServiceHook.isSelectServiceModalOn,
    setIsSelectServiceModalOn: addCarServiceHook.setIsSelectServiceModalOn,
    isSelectServiceUpdateModalOn: addCarServiceHook.isSelectServiceUpdateModalOn,
    setIsSelectServiceUpdateModalOn: addCarServiceHook.setIsSelectServiceUpdateModalOn,
    getIsServiceInArray: addCarServiceHook.getIsServiceInArray,
    handleOnClickOwnedCarOnAddNewCarModal: addCarServiceHook.handleOnClickOwnedCar,
    handleOnClickContinueOnAddNewCarModal: addCarServiceHook.handleOnClickContinueOnAddNewCarModal,
    handleOnClickServiceBlock: addCarServiceHook.handleOnClickServiceBlock,
    handleOnClickGoBackToSelectCar: addCarServiceHook.handleOnClickGoBackToSelectCar,
    handleOnClickEditService: addCarServiceHook.handleOnClickEditService,
    handleOnClickClearService: addCarServiceHook.handleOnClickClearService,

    scrollableElementRef,
    notSelectedOwnedCars: addCarServiceHook.notSelectedOwnedCars,
  };

  return <carServiceContext.Provider value={value}>{children}</carServiceContext.Provider>;
};

export default CarServiceContextProvider;
