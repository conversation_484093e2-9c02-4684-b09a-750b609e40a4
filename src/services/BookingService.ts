import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { API_URL } from "../values";

export class BookingService {
  static async create(tireStored: "YES" | "NO" | "", heardFrom: string, notes: string) {
    const headers = await getTokenContainedHeader();
    const createdResult = (
      await axios.post(
        API_URL + "/bookings",
        {
          tireStored,
          heardFrom,
          notes,
        },
        {
          headers,
        },
      )
    ).data as {
      checkoutUrl: string;
    };
    return createdResult;
  }

  static async reschedule(bookingId: number, newBookingDateTime: string, newTechnicianId: number) {
    const headers = await getTokenContainedHeader();
    const rescheduledResult = (
      await axios.put(
        API_URL + `/bookings/${bookingId}/reschedule`,
        {
          newBookingDateTime,
          newTechnicianId,
        },
        {
          headers,
        },
      )
    ).data as {
      checkoutUrl: string;
    };
    return rescheduledResult;
  }
}
