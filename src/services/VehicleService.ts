import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { API_URL } from "../values";

export class VehicleService {
  static async getAll() {
    const headers = await getTokenContainedHeader();
    const cars = (
      await axios.get(API_URL + "/cars", {
        headers,
      })
    ).data;

    return cars;
  }

  static async save(year: string, make: string, model: string, color: string, pictureUri?: string) {
    const headers = await getTokenContainedHeader();
    const createdResult = (
      await axios.post(
        API_URL + "/cars",
        {
          year,
          make,
          model,
          pictureUri,
          color,
        },
        {
          headers,
        },
      )
    ).data;

    return createdResult;
  }

  static async update(
    id: number,
    params: {
      year?: string;
      make?: string;
      model?: string;
      color?: string;
      pictureUri?: string;
    },
  ) {
    const headers = await getTokenContainedHeader();
    const updatedResult = (
      await axios.put(API_URL + "/cars/" + id, params, {
        headers,
      })
    ).data;

    return updatedResult;
  }
}
