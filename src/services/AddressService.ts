import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { API_URL } from "../values";

export type NewAddress = {
  addressId: number;
  street1: string;
  street2?: string;
  city: string;
  country: string;
  postal: string;
  province: string;
  customerName: string;
  customerPhone: string;
};

export class AddressService {
  static async get() {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.get(API_URL + "/addresses", {
        headers,
      })
    ).data;

    return result as NewAddress[];
  }

  static async create(
    street1: string,
    street2: string | undefined,
    city: string,
    province: string,
    postal: string,
    country: string,
    customerName: string,
    customerPhone: string,
  ) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.post(
        API_URL + "/addresses",
        {
          street1,
          street2,
          city,
          province,
          postal,
          country,
          customerName,
          customerPhone,
        },
        {
          headers,
        },
      )
    ).data;
  }

  static async update(
    addressId: number,
    street1: string,
    street2: string | undefined,
    city: string,
    province: string,
    postal: string,
    country: string,
    customerName: string,
    customerPhone: string,
  ) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.put(
        API_URL + "/addresses/" + addressId,
        {
          street1,
          street2,
          city,
          province,
          postal,
          country,
          customerName,
          customerPhone,
        },
        {
          headers,
        },
      )
    ).data;
  }

  static async delete(addressId: number) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.delete(API_URL + "/addresses/" + addressId, {
        headers,
      })
    ).data;
  }
}
