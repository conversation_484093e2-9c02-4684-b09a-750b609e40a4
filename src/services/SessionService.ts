import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { CategorySessionItem } from "../types/Category";
import { API_URL } from "../values";
import { NewAddress } from "./AddressService";
import { CarService } from "../types/CarService";
import { CarServiceExtension } from "../types/CarServiceExtension";

export class SessionService {
  static async getCarServiceSession() {
    const headers = await getTokenContainedHeader();
    const sessionResult = (
      await axios.get(API_URL + "/sessions", {
        headers,
        params: {
          mode: 1,
        },
      })
    ).data as
      | {
          id: number;
          carServices: CarService[];
        }[]
      | "";

    console.log("getCarServiceSession", sessionResult);
    return sessionResult;
  }

  static async getCarServiceExtension() {
    const headers = await getTokenContainedHeader();
    const sessionResult = (
      await axios.get(API_URL + "/sessions", {
        headers,
        params: {
          mode: 3,
        },
      })
    ).data as CarServiceExtension[] | "";

    return sessionResult;
  }

  static async createCarServiceSession(
    carServices: {
      carId: number;
      serviceIds: number[];
      addonsWithCount?: {
        addonId: number;
        count: number;
      }[];
    }[],
  ) {
    const headers = await getTokenContainedHeader();
    const result = (
      await axios.post(
        API_URL + "/sessions",
        {
          carServices,
        },
        {
          headers,
        },
      )
    ).data;

    return result;
  }

  static async getCategorySession() {
    const headers = await getTokenContainedHeader();
    const sessionResult = (
      await axios.get(API_URL + "/sessions", {
        headers,
        params: {
          mode: 2,
        },
      })
    ).data as CategorySessionItem[] | "";

    return sessionResult;
  }

  static async getSessionAddress() {
    const headers = await getTokenContainedHeader();
    const sessionAddressResult = (
      await axios.get(API_URL + "/sessions/address", {
        headers,
      })
    ).data as NewAddress | "";

    return sessionAddressResult;
  }

  static async updateSessionAddress(addressId: number) {
    const headers = await getTokenContainedHeader();
    await axios.put(
      API_URL + "/sessions/address",
      {
        addressId,
      },
      {
        headers,
      },
    );
  }

  static async createSessionAddons(
    carServices: {
      carId: number;
      serviceIds: number[];
      addonsWithCount?: {
        addonId: number;
        count: number;
      }[];
    }[],
  ) {
    const headers = await getTokenContainedHeader();
    const result = (
      await axios.post(
        API_URL + "/sessions/addons",
        {
          carServices,
        },
        {
          headers,
        },
      )
    ).data;

    return result;
  }
}
