import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { Address } from "../types/Address";
import { Booking } from "../types/Booking";
import { CalendarBlock, CalendarBlockType } from "../types/CalendarBlock";
import { Customer } from "../types/Customer";
import { GoogleCalendarBlock } from "../types/GoogleCalendarBlock";
import { Technician } from "../types/Technician";
import { API_URL } from "../values";

export class CalendarService {
  static async get(startDate: string, endDate: string, technicianId?: number) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.get(API_URL + "/calendar", {
        headers,
        params: {
          startDate,
          endDate,
          technicianId,
        },
      })
    ).data as {
      calendarBlocks: any[];
      availability: any[];
    };

    return {
      calendarBlocks: result.calendarBlocks.map((item) => {
        if (item.type === CalendarBlockType.BOOKING) {
          const customer = new Customer(
            item.customer.id,
            item.customer.firstName,
            item.customer.lastName,
            item.customer.email,
            item.customer.phone,
            item.customer.roleId,
            item.customer.profilePictureUri,
            new Address(
              item.customer.address.street1,
              item.customer.address.street2,
              item.customer.address.city,
              item.customer.address.province,
              item.customer.address.postal,
              item.customer.address.country,
              null,
            ),
          );

          const technician = new Technician(
            item.technician.id,
            item.technician.firstName,
            item.technician.lastName,
            item.technician.email,
            item.technician.phone,
            item.technician.roleId,
            item.technician.profilePictureUri,
            item.technician.technicianId,
            item.technician.biography,
            item.technician.isCertified,
          );

          return new Booking(
            item.startDateTime,
            item.duration,
            item.type,
            item.id,
            customer,
            technician,
            item.bookingName,
            item.status,
            item.createdDateTime,
            item.services,
            item.paymentId,
            item.amountTotal,
            item.bookingRef,
            item.receiptUrl,
            item.addons,
          );
        } else if (item.type === CalendarBlockType.GOOGLE) {
          return new GoogleCalendarBlock(
            item.startDateTime,
            item.duration,
            item.previewTitle,
            item.rrule,
            item.eventId,
            item.description,
            item.location,
            item.calendarName,
            item.calendarColor,
            item.originalStartDateTime,
          );
        } else {
          return new CalendarBlock(
            item.startDateTime,
            item.duration,
            item.type,
            item.previewTitle,
            item.rrule,
          );
        }
      }),
      availability: result.availability,
    };
  }

  static async saveGoogleTokens(code: string) {
    const headers = await getTokenContainedHeader();
    const result = (
      await axios.post(
        API_URL + "/oauth/google/token",
        {
          code,
        },
        {
          headers,
        },
      )
    ).data;

    return result;
  }

  static async getGoogleOAuthLink() {
    const headers = await getTokenContainedHeader();
    const result = (
      await axios.post(
        API_URL + "/oauth/google",
        {},
        {
          headers,
        },
      )
    ).data;

    return result;
  }
}
