import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { API_URL } from "../values";
import { Zone } from "../pages/zone";

export class ZoneService {
  static async get() {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.get(API_URL + "/zones", {
        headers,
      })
    ).data;

    return result as Zone[];
  }

  static async create(name: string, description: string) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.post(
        API_URL + "/zones",
        {
          name,
          description,
        },
        {
          headers,
        },
      )
    ).data;
  }

  static async update(zoneId: number, name: string, description: string) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.put(
        API_URL + "/zones/" + zoneId,
        {
          name,
          description,
        },
        {
          headers,
        },
      )
    ).data;
  }

  static async delete(zoneId: number) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.delete(API_URL + "/zones/" + zoneId, {
        headers,
      })
    ).data;
  }

  static async getRestrictions(date: string) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.get(API_URL + "/zones/restrictions", {
        headers,
        params: {
          date,
        },
      })
    ).data;
    // console.log("getRestrictions result", result);

    return result as {
      restrictionStartTime: string;
      restrictionEndTime: string;
      expiredAt: string | null;
      fsas: {
        name: string;
      }[];
      technicianId: number;
    }[];
  }
}
