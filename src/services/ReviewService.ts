import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { Review } from "../types/Review";
import { API_URL } from "../values";

export class ReviewService {
  static async getByTechnicianId(technicianId: number) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.get(API_URL + "/technicians/review", {
        headers,
        params: {
          technicianId,
        },
      })
    ).data;

    return result as Review[];
  }

  static async create(
    bookingId: number,
    rating: number,
    description: string,
    serviceNames: string[],
  ) {
    const headers = await getTokenContainedHeader();

    const result = (
      await axios.post(
        API_URL + "/technicians/review",
        {
          bookingId,
          rating,
          description,
          serviceNames,
        },
        {
          headers,
        },
      )
    ).data;
  }
}
