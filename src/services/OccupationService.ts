import axios from "axios";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { API_URL } from "../values";

export class OccupationService {
  static async create(
    content: {
      technicianId: number;
      categoryId: number;
      serviceIds: { id: number; count: number }[];
      startDateTime: string;
      timezoneName: string;
    }[],
  ) {
    const headers = await getTokenContainedHeader();
    const result = await axios.post(
      API_URL + "/occupations",
      {
        content,
      },
      {
        headers,
      },
    );
  }

  static async delete() {
    const headers = await getTokenContainedHeader();
    const result = await axios.delete(API_URL + "/occupations", {
      headers,
    });
    return result.data;
  }
}
