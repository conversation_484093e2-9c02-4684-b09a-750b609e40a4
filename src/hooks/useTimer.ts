import axios from "axios";
import React, { useEffect, useState, useRef } from "react";
import { getTokenContainedHeader } from "../functions/cognito/util";
import { API_URL } from "../values";
import { useRouter } from "next/router";

function useTimer() {
  const router = useRouter();

  const [nowSecond, setNowSecond] = useState<number>(60 * 5);
  const isTimerCalled = useRef<boolean>(false);

  useEffect(() => {
    const fetchOccupation = async () => {
      if (!isTimerCalled.current) {
        isTimerCalled.current = true;
        const result = await apiGetOccupation();

        if (result.occupiedTime === null || result.timeslots === null) {
          alert("Please start over again from the beginning!");
          router.push("/");
        } else {
          const endTime = new Date(result.occupiedTime).getTime();
          const interval = 1000;
          let timeoutId: NodeJS.Timeout | null = null;

          const updateNowSecond = () => {
            const currentTime = new Date().getTime();
            const remainingTimeInSeconds = Math.floor((endTime - currentTime) / 1000) + 300;
            setNowSecond(remainingTimeInSeconds);

            if (remainingTimeInSeconds <= 0) {
              if (timeoutId) {
                clearInterval(timeoutId);
              }
            }
          };

          timeoutId = setInterval(updateNowSecond, interval);
          updateNowSecond();

          return () => {
            if (timeoutId) {
              clearInterval(timeoutId);
            }
          };
        }
      }
    };
    fetchOccupation();
  }, []);

  async function apiGetOccupation() {
    const headers = await getTokenContainedHeader();
    const result = (
      await axios.get(API_URL + "/occupations", {
        headers,
      })
    ).data as {
      occupiedTime: string;
      timeslots: {
        categoryId: number;
        technicianId: number;
        startDateTime: string;
        endDateTime: string;
      }[];
    }[];

    return result[0];
  }

  return {
    nowSecond,
    isTimerCalled,
  };
}

export default useTimer;
