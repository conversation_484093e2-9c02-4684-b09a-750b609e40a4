import moment, { Moment } from "moment";
import { useEffect, useMemo, useState } from "react";
import { apiListTechnicians } from "../functions/api/technician";
import { BusinessRule } from "../types/BusinessRule";
import { DURATION_UNIT, START_TIME } from "../values";
import useRestrictions from "./useRestrictions";
import { Time } from "../types/Time";
import { DayAvailability } from "../types/DayAvailability";
import useCalendarSelection from "./useCalendarSelection";
import assert from "assert";
import { useCarServices } from "./useCarServices";
import { Booking } from "../types/Booking";

export type SearchedTechnician = {
  technicianId: number;
  name: string;
  profilePictureUri: string | null;
  biography: string | null;
  rating: number | null;
  numOfReviews: number;
};

export type TechnicianFreeBusy = {
  freeBusy: {
    start: string;
    end: string;
  }[][];
  technician: SearchedTechnician;
}[];

export function useTechnicianSearch(
  selectedCategoryId: number | undefined,
  duration: number,
  fsa: string | null,
  selectedBooking: Booking | null,
) {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // TODO: should change to map due to performance
  const [technicianFreeBusy, setTechnicianFreeBusy] = useState<DayAvailability[] | null>(null);
  const [selectedTechIndex, setSelectedTechIndex] = useState<number>(-1);

  const calendarSelectionHook = useCalendarSelection({
    year: moment().year(),
    month: moment().month() + 1,
  });

  const useAddCarService = useCarServices();

  const restrictionHook = useRestrictions();

  useEffect(() => {
    const yearMonth = calendarSelectionHook.getSelectedYearMonth();
    if (yearMonth !== null) {
      console.log("fetchRestrictions");
      restrictionHook.fetchRestrictions(moment(yearMonth).startOf("month"));
    }
  }, [calendarSelectionHook.selectedYear, calendarSelectionHook.selectedMonth]);

  useEffect(() => {
    fetchMonthTechsOnCondition();
  }, [selectedCategoryId, duration, restrictionHook.isFetching, fsa]);

  function fetchMonthTechsOnCondition() {
    if (selectedCategoryId !== undefined && !restrictionHook.isFetching && fsa !== null) {
      const yearMonth = calendarSelectionHook.getSelectedYearMonth();
      assert(yearMonth !== null, "yearMonth is null");
      console.log("fetchMonthTechs");
      _fetchMonthTechs(yearMonth, selectedCategoryId);
    }
  }

  const selectedDateFreeBusy = useMemo(() => {
    if (
      technicianFreeBusy === null ||
      calendarSelectionHook.getSelectedYearMonthDate() === null ||
      fsa === null
    ) {
      return null;
    }

    const dayIndex = technicianFreeBusy.findIndex(
      (item) => item.getDay() === calendarSelectionHook.getSelectedYearMonthDate()!.date(),
    );
    if (dayIndex === -1) {
      return null;
    }

    return technicianFreeBusy[dayIndex].getTimes();
  }, [
    technicianFreeBusy,
    calendarSelectionHook.selectedYear,
    calendarSelectionHook.selectedMonth,
    calendarSelectionHook.selectedDate,
    calendarSelectionHook.selectedTime,
    restrictionHook.restrictions,
    fsa,
  ]);

  const availableHours = useMemo(() => {
    const result =
      selectedDateFreeBusy?.map((item) => {
        return item.time;
      }) || [];
    return result;
  }, [calendarSelectionHook.getSelectedYearMonthDate()]);

  async function _fetchMonthTechs(selectedTime: Moment, selectedCategoryId: number | undefined) {
    console.log("_fetchMonthTechs: selectedTime", selectedTime);
    console.log("_fetchMonthTechs: selectedCategoryId", selectedCategoryId);
    console.log("_fetchMonthTechs: useAddCarService.carServices", useAddCarService.carServices);
    if (selectedCategoryId === undefined) {
      console.error("selectedCategoryId is undefined");
      return;
    }
    setIsLoading(true);

    const startDateTime = moment(selectedTime).startOf("month").toISOString();
    const endDateTime = moment(selectedTime).endOf("month").toISOString();
    let serviceIds: number[] = [];
    let zoneId: number | null = null;

    console.log("selectedBooking ", selectedBooking);
    if (selectedBooking !== null && selectedBooking !== undefined) {
      serviceIds = selectedBooking.services.map((service) => service.id);
      zoneId = parseInt(selectedBooking.customer.address!.zoneId!);
    } else {
      // map over each car, in each car map over all services and get ids.
      // use set to remove diplicate values.
      serviceIds = [
        ...new Set(
          useAddCarService.carServices.flatMap((carService) =>
            carService.services.map((service) => service.id),
          ),
        ),
      ];
    }
    console.log("serviceIds", serviceIds);

    if (selectedCategoryId !== undefined) {
      setTechnicianFreeBusy(null);
      const availableTechs = await apiListTechnicians(
        selectedCategoryId,
        serviceIds,
        startDateTime,
        endDateTime,
        zoneId,
      );
      console.log("availableTechs", availableTechs);

      const freeBusy = createAvailableTimesWithTechnician(
        startDateTime,
        endDateTime,
        duration,
        availableTechs,
      );
      // let changed = false;
      // const now = moment(selectedTime);
      // for (const date of freeBusy) {
      //   if (date.day >= now.date()) {
      //     for (const time of date.times) {
      //       if (time.time >= now.format("h:mm A")) {
      //         setSelectedTime((prev) => {
      //           prev.date(date.day);
      //           prev.hour(parseInt(time.time.split(":")[0]));
      //           prev.minute(parseInt(time.time.split(":")[1]));

      //           return prev;
      //         });
      //         changed = true;
      //         break;
      //       }
      //     }

      //     if (changed) {
      //       break;
      //     } else {
      //       setSelectedTime((prev) => {
      //         prev.date(date.day + 1);
      //         prev.hour(0);
      //         prev.minute(0);
      //         return prev;
      //       });
      //       changed = true;
      //       break;
      //     }
      //   }
      // }

      // if (!changed) {
      //   alert("No available technician for this month");
      // }

      const dayAvailabilityInstances = freeBusy.map(
        (item) => new DayAvailability(item.day, item.times),
      );
      console.log("dayAvailabilityInstances", dayAvailabilityInstances);

      setTechnicianFreeBusy(dayAvailabilityInstances);

      // const tmp = moment(selectedTime);
      // let isFound = false;
      // for (let i = 0; i < freeBusy.length; i++) {
      //   for (let j = 0; j < freeBusy[i].times.length; j++) {
      //     if (
      //       moment(
      //         moment().date(freeBusy[i].day).format("YYYY-MM-DD") + " " + freeBusy[i].times[j].time
      //       ).diff(moment(), "hours") >= 8
      //     ) {
      //       isFound = true;
      //       setSelectedTime((prev) => {
      //         tmp.date(freeBusy[i].day);
      //         return moment(tmp.format("YYYY-MM-DD") + " " + freeBusy[i].times[j].time);
      //       });
      //       break;
      //     }
      //   }
      //   if (isFound) {
      //     break;
      //   }
      // }

      // if (!isFound) {
      //   // addMonthToSelectedTime();
      // }
      // console.log(freeBusy);
      setIsLoading(false);
    }

    function createAvailableTimesWithTechnician(
      startDateTime: string,
      endDateTime: string,
      duration: number,
      techData: TechnicianFreeBusy,
    ): {
      day: number;
      times: {
        time: string;
        technicians: SearchedTechnician[];
      }[];
    }[] {
      assert(fsa !== null, "fsa is null");
      console.log("duration", duration);
      console.log("fsa", fsa);

      const start = moment(startDateTime).startOf("month");
      const end = moment(endDateTime).endOf("month");
      let calledTimes = 0;

      let result: any = [];

      const datesBetween = Time.generateDaysBetween(start, end);
      for (const date of datesBetween) {
        let dayItem = {
          day: date.date(),
          times: [] as { time: string; technicians: SearchedTechnician[] }[],
        };

        const availableTimes = Time.generateAvailableTimesOnDate(date);
        for (const time of availableTimes) {
          let timeItem = {
            time: time.format("h:mm A"),
            technicians: [] as SearchedTechnician[],
          };

          for (const tech of techData) {
            if (restrictionHook.getIfTimeIsRestricted(time, fsa, tech.technician.technicianId)) {
              continue;
            }

            let isTechBusy = false;

            for (const calendarBusy of tech.freeBusy) {
              const isConflictingFreeBusy = Time.getIsTimeConflicting(
                {
                  start: time,
                  end: moment(time).add(duration * DURATION_UNIT, "minutes"),
                },
                calendarBusy,
              );

              // Should decouple this later
              let isConflictingBorder = false;
              isConflictingBorder = Time.getIsTimeConflicting(
                {
                  start: time,
                  end: moment(time).add(duration * DURATION_UNIT, "minutes"),
                },
                [
                  {
                    start: moment(time).hour(12).minute(30).format("YYYY-MM-DDTHH:mm:ssZ"),
                    end: moment(time).hour(13).minute(0).format("YYYY-MM-DDTHH:mm:ssZ"),
                  },
                  {
                    start: moment(time).hour(18).minute(0).format("YYYY-MM-DDTHH:mm:ssZ"),
                    end: moment(time).hour(18).minute(30).format("YYYY-MM-DDTHH:mm:ssZ"),
                  },
                ],
              );

              isTechBusy = isConflictingFreeBusy || isConflictingBorder;

              if (isTechBusy) {
                break;
              }
            }

            if (!isTechBusy) {
              timeItem.technicians.push(tech.technician);
            }
          }

          if (timeItem.technicians.length > 0) {
            dayItem.times.push(timeItem);
          }
        }

        if (dayItem.times.length > 0) {
          result.push(dayItem);
        }
      }

      return result;
    }
  }

  const selectedDateAvailableTechs: SearchedTechnician[] = useMemo(() => {
    if (
      technicianFreeBusy === null ||
      calendarSelectionHook.getSelectedYearMonthDateHour() === null
    ) {
      return [];
    }

    const dayIndex = technicianFreeBusy.findIndex(
      (item) => item.getDay() === calendarSelectionHook.getSelectedYearMonthDateHour()!.date(),
    );
    if (dayIndex === -1) {
      return [];
    }

    return technicianFreeBusy[dayIndex].getTechniciansOnTime(
      calendarSelectionHook.getSelectedYearMonthDateHour()!.format("h:mm A"),
    );
  }, [technicianFreeBusy, calendarSelectionHook.getSelectedYearMonthDateHour()]);

  function getIsDateAvailable(date: moment.Moment) {
    if (technicianFreeBusy === null) {
      return false;
    }

    if (BusinessRule.getNearestBookableTimeFromNow() > date) {
      return false;
    }

    const dayIndex = technicianFreeBusy.findIndex((item) => item.getDay() === date.date());
    if (dayIndex === -1) {
      return false;
    }

    return true;
  }

  // function getIsTimeAvailable(time: moment.Moment) {
  //   if (technicianFreeBusy === null || fsa === null) {
  //     return false;
  //   }

  //   const dayIndex = technicianFreeBusy.findIndex((item) => item.getDay() === time.date());
  //   if (dayIndex === -1) {
  //     return false;
  //   }

  //   const timeIndex = technicianFreeBusy[dayIndex]
  //     .getTimes()
  //     .findIndex((item) => item.time === time.format("h:mm A"));
  //   if (timeIndex === -1) {
  //     return false;
  //   }

  //   if (restrictionHook.getIfTimeIsRestricted(time, fsa)) {
  //     return false;
  //   }

  //   return true;
  // }

  // TODO : move to available date, time
  function addMonthToSelectedTime() {
    setIsLoading(true);
    setSelectedTechIndex(-1);
    calendarSelectionHook.resetDateTime();
    calendarSelectionHook.selectMonth(
      calendarSelectionHook.getSelectedYearMonth()!.month() + 1 + 1,
    );
    // When it's December, set the year to the next year and set the month to January.
    if (calendarSelectionHook.getSelectedYearMonth()!.month() + 1 === 12) {
      calendarSelectionHook.selectYear(calendarSelectionHook.getSelectedYear()!.year() + 1);
      calendarSelectionHook.selectMonth(
        calendarSelectionHook.getSelectedYearMonth()!.month() + 1 - 11,
      );
    }
  }

  // TODO : move to available date, time
  function minusMonthToSelectedTime() {
    setIsLoading(true);
    setSelectedTechIndex(-1);
    calendarSelectionHook.resetDateTime();
    calendarSelectionHook.selectMonth(
      calendarSelectionHook.getSelectedYearMonth()!.month() + 1 - 1,
    );
    // When it's January, set the year to the previous year and set the month to December.
    if (calendarSelectionHook.getSelectedYearMonth()!.month() + 1 === 1) {
      calendarSelectionHook.selectYear(calendarSelectionHook.getSelectedYear()!.year() - 1);
      calendarSelectionHook.selectMonth(
        calendarSelectionHook.getSelectedYearMonth()!.month() + 1 + 11,
      );
    }
  }

  function setSelectedDate(date: number) {
    calendarSelectionHook.selectDate(date);
  }

  function setSelectedTimeOption(timeOption: string) {
    calendarSelectionHook.selectTime(timeOption);
  }

  function resetSelection() {
    calendarSelectionHook.resetYearMonthDateTime();
    setSelectedTechIndex(-1);
  }

  function getAssignedTechnician() {
    return selectedDateAvailableTechs[selectedTechIndex];
  }

  return {
    technicians: selectedDateAvailableTechs,
    getIsDateAvailable,

    selectedTechIndex,
    setSelectedTechIndex,

    addMonthToSelectedTime,
    minusMonthToSelectedTime,
    setSelectedDate,
    setSelectedTimeOption,

    isLoading: restrictionHook.isFetching || isLoading,
    resetSelection,

    selectedDateFreeBusy,
    isDateSelected: calendarSelectionHook.getSelectedYearMonthDate() !== null,
    isTimeSelected: calendarSelectionHook.getSelectedYearMonthDateHour() !== null,
    selectedMonth: calendarSelectionHook.getSelectedYearMonth(),
    selectedDate: calendarSelectionHook.getSelectedYearMonthDate(),
    selectedTime: calendarSelectionHook.getSelectedYearMonthDateHour(),

    availableHours,
    getAssignedTechnician,
  };
}
