import { useState, useEffect } from "react";

const useReducedMotion = (defaultVal = true) => {
  const [reducedMotion, setReducedMotion] = useState(defaultVal);

  function queryChangeHandler(event: any) {
    setReducedMotion(event.target.matches);
  }

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");

    if (mediaQuery.addEventListener) {
      setReducedMotion(mediaQuery.matches);

      mediaQuery.addEventListener("change", queryChangeHandler);

      return () => mediaQuery.removeEventListener("change", queryChangeHandler);
    } else {
      setReducedMotion(mediaQuery.matches);
      mediaQuery.addListener(queryChangeHandler);
    }
  }, []);

  return reducedMotion;
};

export default useReducedMotion;
