import { useEffect } from "react";
import { useAlertModal } from "./useAlertModal";
import useTimer from "./useTimer";

export function useTimerAddon(
  timerHook: ReturnType<typeof useTimer>,
  alertModalHook: ReturnType<typeof useAlertModal>,
) {
  useEffect(() => {
    if (timerHook.isTimerCalled && timerHook.nowSecond === 0) {
      alertModalHook.setIsOn(true);
    }
  }, [timerHook.nowSecond, timerHook.isTimerCalled]);

  return {};
}
