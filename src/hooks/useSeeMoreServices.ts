import { useEffect, useState } from "react";
import { useCarServices } from "./useCarServices";

export function useSeeMoreServices(carServiceHook: ReturnType<typeof useCarServices>) {
  const [seeMoreServiceIndexArr, setSeeMoreServiceIndexArr] = useState<number[]>([]);

  useEffect(() => {
    const newSeeMoreServiceIndexArr = _initializeSeeMoreServicesIndexArr(
      carServiceHook.carServices.length,
    );
    setSeeMoreServiceIndexArr(newSeeMoreServiceIndexArr);
  }, [carServiceHook.carServices.length]);

  useEffect(() => {
    console.log("seeMoreServiceIndexArr = " + seeMoreServiceIndexArr);
  }, [seeMoreServiceIndexArr]);

  function _initializeSeeMoreServicesIndexArr(length: number) {
    return Array(length).fill(-1);
  }

  return {
    seeMoreServiceIndexArr,
    setSeeMoreServiceIndexArr,
  };
}
