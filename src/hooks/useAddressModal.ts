import { useEffect, useMemo, useRef, useState } from "react";
import { Input } from "../types/Input";

export function useAddressModal() {
  const [name, setName] = useState<string>("");
  const [street1, setStreet1] = useState<string>("");
  const [street2, setStreet2] = useState<string>("");
  const [city, setCity] = useState<string>("");
  const [province, setProvince] = useState<string>("");
  const [postal, setPostal] = useState<string>("");
  const [country, setCountry] = useState<string>("Canada");
  const [phone, setPhone] = useState<string>("");
  const [phoneRegion, setPhoneRegion] = useState<string>("+1");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAutoCompleteDisabled, setIsAutoCompleteDisabled] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<{
    title: string;
    description: string;
  } | null>(null);

  // Flag indicating that populateForms occurred
  const populateFormsRef = useRef<any>(false);

  useEffect(() => {
    console.log("populateFormsRef.current = " + populateFormsRef.current);
  }, [populateFormsRef.current]);

  useEffect(() => {
    if (populateFormsRef.current) {
      setIsAutoCompleteDisabled(true);
    } else {
      setIsAutoCompleteDisabled(false);
    }
  }, [populateFormsRef.current]);

  useEffect(() => {
    if (populateFormsRef.current === true) {
      if (street1 !== "" && postal !== "") {
        console.log("test passed");
      } else {
        populateFormsRef.current = false;
        resetPopulateForms();
        setErrorMessage({
          title: "Invalid Address",
          description:
            "Please try to select your full-address again by using Address Autocomplete.",
        });
      }
    }
  }, [street1, postal]);

  const isContinuable = useMemo(() => {
    return [name, phoneRegion, phone, street1, city, province, postal, country].every(
      (x, index) => {
        return x.length > 0;
      },
    );
  }, [name, phoneRegion, phone, street1, city, province, postal, country]);

  function populateForms(
    addressLine1: string,
    city: string,
    province: string,
    postal: string,
    country: string,
  ) {
    setStreet1(addressLine1);
    setCity(city);
    setProvince(province);
    if (postal.length == 3) {
      setPostal(postal + " ");
    } else {
      setPostal(postal);
    }
    setCountry(country);
    populateFormsRef.current = true;
  }

  function setFormattedPhoneNumber(value: string) {
    const isBackspace = value.length < phone.length;
    const newValue = Input.formatNewInputIntoPhoneNumber(value, phone, isBackspace);
    setPhone(newValue || "");
  }

  function resetVal() {
    setName("");
    setStreet1("");
    setStreet2("");
    setCity("");
    setProvince("");
    setCountry("Canada");
    setPhone("");
    setPhoneRegion("+1");
    setPostal("");
    setErrorMessage(null);
    populateFormsRef.current = false;
  }

  function resetPopulateForms() {
    setStreet1("");
    setStreet2("");
    setCity("");
    setProvince("");
    setPostal("");
    setCountry("Canada");
  }

  function onChangePostalCodeText(value: string) {
    const maxLength = 7;
    const FSALength = 3;
    const regexNotNumberOrLetter = /[^a-zA-Z0-9]/;
    const lastChar = value.charAt(value.length - 1);
    const isBackspace = value.length < postal.length;

    if (isBackspace) {
      setPostal(postal.slice(0, 4));
      return;
    }

    if (regexNotNumberOrLetter.test(lastChar)) {
      if (!isBackspace) {
        return;
      }
    }

    if (value.length > maxLength) return;

    if (value.length == FSALength) {
      // if (isBackspace) {
      //   value = value.slice(0, FSALength - 1);
      // } else {
      //   value = value + " ";
      // }
      return;
    }

    setPostal(value.toUpperCase());
  }

  function isValidatePhone() {
    const phoneNumberPattern = /^\(\d{3}\) \d{3}-\d{4}$/;

    if (phoneNumberPattern.test(phone) === false) {
      setErrorMessage({
        title: "Invalid Phone Number",
        description:
          "The phone number must be in exactly 10-digit format. Please enter a valid phone number and try again!",
      });
      return false;
    } else return true;
  }

  function isValidatePostalCode() {
    const canadianPostalCodePattern = /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/;

    if (canadianPostalCodePattern.test(postal) === false) {
      setErrorMessage({
        title: "Invalid Postal Code",
        description:
          "Postal code must be in A1B 2C3 format. Please enter a valid postal code and try again!",
      });
      return false;
    } else return true;
  }

  return {
    name,
    setName,

    street1,
    setStreet1,

    street2,
    setStreet2,

    city,
    setCity,

    province,
    setProvince,

    postal,
    setPostal,

    country,
    setCountry,

    phone,
    setPhone,

    phoneRegion,
    setPhoneRegion,

    isLoading,
    setIsLoading,

    isAutoCompleteDisabled,
    setIsAutoCompleteDisabled,

    errorMessage,
    setErrorMessage,

    populateFormsRef,

    isContinuable,
    populateForms,
    setFormattedPhoneNumber,
    resetVal,
    resetPopulateForms,
    onChangePostalCodeText,

    isValidatePhone,
    isValidatePostalCode,
  };
}
