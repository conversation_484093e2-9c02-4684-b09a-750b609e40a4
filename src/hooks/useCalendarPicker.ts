import moment, { Moment } from "moment";
import { useMemo, useState } from "react";

const useCalendarPicker = (selectedMonth: moment.Moment | null) => {
  const [step, setStep] = useState<number>(0);
  const [reviewTechIndex, setReviewTechIndex] = useState<number>(-1);

  const weekDays = moment.weekdaysShort(); // ["Sun", "Mon", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat"]
  const firstDayOfMonth = moment(selectedMonth).startOf("month");
  const lastDayOfMonth = moment(selectedMonth).endOf("month");

  const isPreviousCalendarMonthDisabled = useMemo(() => {
    const firstDayOfCurrentMonth = moment().startOf("month");
    return firstDayOfMonth.isSameOrBefore(firstDayOfCurrentMonth);
  }, [selectedMonth]);

  function generateDays() {
    const days = [];
    let day = firstDayOfMonth;
    let startWeekDay = 0;
    let shouldOffset = true;

    while (day <= lastDayOfMonth) {
      if (shouldOffset && startWeekDay !== moment(day).day()) {
        startWeekDay++;
        days.push(null);
      } else {
        shouldOffset = false;
        days.push(moment(day));
        day = moment(day).add(1, "day");
      }
    }

    return days;
  }

  function generateWeeks() {
    const weeks = [];
    let week: (Moment | null)[] = [];
    const days = generateDays();
    days.forEach((day) => {
      week.push(day);
      if (week.length === 7) {
        weeks.push(week);
        week = [];
      }
    });
    if (week.length > 0) {
      weeks.push(week);
    }
    return weeks;
  }

  return {
    step,
    setStep,
    reviewTechIndex,
    setReviewTechIndex,
    weekDays,
    firstDayOfMonth,
    lastDayOfMonth,
    generateWeeks,
    isPreviousCalendarMonthDisabled,
  };
};

export default useCalendarPicker;
