import { useEffect, useMemo, useRef, useState } from "react";
import { AddonCar } from "../types/AddonCar";
import { BusinessRule } from "../types/BusinessRule";
import { ServiceCar } from "../types/ServiceCar";
import { TAX_RATE, TRAVEL_FEE } from "../values";

export function useOrderSummary() {
  const [serviceCars, setServiceCars] = useState<ServiceCar[]>();
  const [addonCars, setAddonCars] = useState<AddonCar[]>();
  const [isFolded, setIsFolded] = useState<boolean>(true);
  const [isGoBack, setIsGoBack] = useState<boolean>(false);

  const [dynamicHeight, setDynamicHeight] = useState<number>(0);

  const dynamicHeightRef = useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    if (dynamicHeightRef !== null && dynamicHeightRef.current) {
      setDynamicHeight(dynamicHeightRef.current.offsetHeight);
    }
  };

  // Use useEffect to log carServices whenever it changes
  useEffect(() => {
    console.log("Updated serviceCars:", serviceCars);
  }, [serviceCars]);

  useEffect(() => {
    handleScroll();
  }, [dynamicHeightRef]);

  const orderSummaryPrices = useMemo(() => {
    console.log("orderSummaryPrices");
    const noTaxServiceNames: string[] = ["Girl Guide Cookies"];
    let servicePrice = 0;
    let travelFees = 0;
    let numOfServices = 0;
    let addonPrice = 0;
    let notTaxedPrice = 0;

    if (serviceCars) {
      serviceCars.forEach((carService) => {
        const price = BusinessRule.calculateEachTargetPrice(
          carService.service.price,
          carService.cars.length,
        );
        servicePrice += price;
        const notAllowedDiscountServiceNames: string[] = ["Tire Storage", "Girl Guide Cookies"];

        if (!notAllowedDiscountServiceNames.includes(carService.service.name)) {
          numOfServices += carService.cars.length;
        }

        if (noTaxServiceNames.includes(carService.service.name)) {
          notTaxedPrice += price;
          // console.log("notTaxedPrice", notTaxedPrice);
        }
      });
    }

    if (addonCars) {
      addonCars.forEach((addonCar) => {
        const price = BusinessRule.calculateEachTargetAddonPrice(
          addonCar.addon.cost,
          addonCar.cars.length,
        );
        addonPrice += price;
        // console.log("addonCar.addon.name", addonCar.addon.name);

        if (noTaxServiceNames.includes(addonCar.addon.name)) {
          notTaxedPrice += price;
          console.log("notTaxedPrice", notTaxedPrice);
        }
      });
    }

    travelFees = numOfServices ? ((numOfServices - 1) * TRAVEL_FEE) / 100 : 0;
    const subtotalPrice = servicePrice + addonPrice;
    const taxPrice = (subtotalPrice - notTaxedPrice) * TAX_RATE;
    const totalPrice = subtotalPrice + taxPrice;

    return {
      servicePrice,
      travelFees,
      addonPrice,
      subtotalPrice,
      taxPrice,
      totalPrice,
    };
  }, [serviceCars, addonCars]);

  return {
    serviceCars,
    setData: setServiceCars,

    addonCars,
    setAddonCars,

    isFolded,
    setIsFolded,

    isGoBack,
    setIsGoBack,

    orderSummaryPrices,

    dynamicHeight,
    dynamicHeightRef,
  };
}
