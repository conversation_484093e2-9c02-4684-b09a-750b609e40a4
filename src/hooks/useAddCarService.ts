import { useMemo, useState } from "react";
import { getCurrentUser } from "../functions/cognito/util";
import { loadCarLocalStorage, saveCarLocalStorage } from "../functions/local";
import { VehicleService } from "../services/VehicleService";
import { Service, isServiceEqual } from "../types";
import { Car } from "../types/Car";
import { CarServiceWithoutAddon, useCarServices } from "./useCarServices";
import assert from "assert";

export function useAddCarService(carServiceHook: ReturnType<typeof useCarServices>) {
  const [isAddCarModalOn, setIsAddCarModalOn] = useState<boolean>(false);
  const [ownedCars, setOwnedCars] = useState<Car[]>();
  const [selectedCar, setSelectedCar] = useState<Car | undefined>();
  const [isLoadingFetchCar, setIsLoadingFetchCar] = useState<boolean>(false);

  const [isSelectServiceModalOn, setIsSelectServiceModalOn] = useState<boolean>(false);
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [isSelectServiceUpdateModalOn, setIsSelectServiceUpdateModalOn] = useState<boolean>(false);
  const [selectedCarServiceIndex, setSelectedCarServiceIndex] = useState<number>(-1);

  const isContinuable = useMemo(() => {
    if (selectedCar === undefined) {
      return false;
    } else {
      return true;
    }
  }, [selectedCar]);

  async function fetchOwnedCars() {
    setIsLoadingFetchCar(true);
    if ((await getCurrentUser()) === null) {
      const savedCars = await loadCarLocalStorage();
      if (savedCars === null) {
        await saveCarLocalStorage([]);
        setOwnedCars([]);
      } else {
        setOwnedCars(savedCars);
      }
    } else {
      const result = await VehicleService.getAll();

      setOwnedCars(result);
    }
    setIsLoadingFetchCar(false);
  }

  const notSelectedOwnedCars = useMemo(() => {
    if (ownedCars) {
      return updateOwnedCarsNotSelected(ownedCars);
    }
  }, [ownedCars, carServiceHook.carServices]);

  function updateOwnedCarsNotSelected(prevCars: Car[]) {
    let newResult: Car[] = [];
    if (carServiceHook.carServices) {
      let result = prevCars.filter((prev) => {
        return !carServiceHook.carServices.some((service) => service.car.id === prev.id);
      });
      newResult = result;
      return newResult;
    }
  }

  function handleOnSelectOwnedCar(target: Car) {
    setSelectedCar(target);
  }

  function getIsCarAlreadySelected(carId: number) {
    return carServiceHook.carServices.findIndex((item) => item.car.id === carId) !== -1;
  }

  function handleOnClickContinueOnAddNewCarModal() {
    if (selectedCar) {
      assert(!getIsCarAlreadySelected(selectedCar.id), "That car is already selected");
      setIsAddCarModalOn(false);
      setIsSelectServiceModalOn(true);
    }
  }

  function addCarService(carService: CarServiceWithoutAddon) {
    const index = carServiceHook.carServices.findIndex((item) => item.car.id === carService.car.id);
    if (index === -1) {
      carServiceHook.setCarServices([...carServiceHook.carServices, carService]);
    } else {
      carServiceHook.setCarServices((prev) => {
        const result = [...prev];
        result[index] = carService;
        return result;
      });
    }
  }

  function handleOnClickSubmitOnSelectServiceModal(services: Service[]) {
    console.log("handleOnClickSubmitOnSelectServiceModal", services);
    if (selectedCar !== undefined) {
      let carService: CarServiceWithoutAddon = {
        car: selectedCar,
        services,
      };
      addCarService(carService);
      setSelectedCar(undefined);
    }
  }

  function handleOnClickEditService(car: Car, services: Service[]) {
    let tmpArr = [];
    tmpArr = services;
    setSelectedServices(tmpArr);
    setSelectedCar(car);
  }

  function handleOnClickClearService(car: Car) {
    let tmpArr: Service[] = [];
    setSelectedServices(tmpArr);
  }

  function handleOnClickServiceBlock(target: Service) {
    console.log("handleOnClickServiceBlock", target);
    if (target.needContact === 1) {
      return;
    } else {
      if (getIsServiceAlreadySelected(target)) {
        setSelectedServices((prev) => prev.filter((item) => !isServiceEqual(item, target)));
      } else {
        setSelectedServices((prev) => prev.concat(target));
      }
    }
  }

  function getIsServiceAlreadySelected(target: Service) {
    for (let i = 0; i < selectedServices.length; i++) {
      if (isServiceEqual(selectedServices[i], target)) {
        return true;
      }
    }

    return false;
  }

  function handleOnClickGoBackToSelectCar() {
    setIsAddCarModalOn(true);
    setIsSelectServiceModalOn(false);
  }

  return {
    selectedCar,
    selectedServices,
    setSelectedServices,
    selectedCarServiceIndex,
    setSelectedCarServiceIndex,

    fetchCar: fetchOwnedCars,
    isLoadingFetchCar,
    isContinuable,
    ownedCarArray: ownedCars,
    setOwnedCarArray: setOwnedCars,
    isAddCarModalOn,
    setIsAddCarModalOn,
    isSelectServiceModalOn,
    setIsSelectServiceModalOn,
    isSelectServiceUpdateModalOn,
    setIsSelectServiceUpdateModalOn,
    handleOnClickOwnedCar: handleOnSelectOwnedCar,
    handleOnClickContinueOnAddNewCarModal,
    handleOnClickAddService: handleOnClickSubmitOnSelectServiceModal,
    handleOnClickEditService,
    handleOnClickClearService,
    handleOnClickServiceBlock,
    handleOnClickGoBackToSelectCar,

    getIsServiceInArray: getIsServiceAlreadySelected,

    notSelectedOwnedCars,
  };
}
