import { useEffect, useState } from "react";
import { CategorySessionItem } from "../types/Category";
import { Service } from "../types";

const useCategorySession = () => {
  const [categorySessionItems, setCategorySessionItems] = useState<CategorySessionItem[]>([]);
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState<number>(-1);

  function getSelectedCategory() {
    if (selectedCategoryIndex !== -1) {
      return categorySessionItems[selectedCategoryIndex];
    } else {
      return null;
    }
  }

  useEffect(() => {
    console.log("Updated categorySessionItems:", categorySessionItems);
  }, [categorySessionItems]);

  function getSelectedCategoryTotalDuration() {
    // TODO : should handle multi category case
    if (categorySessionItems.length > 0) {
      let result = 0;
      let firstService: CategorySessionItem["services"][0] | undefined;
      for (let service of categorySessionItems[0].services) {
        if (service.cars.length > 0) {
          if (firstService === undefined) {
            firstService = service;
            result += service.cars.length * service.duration + -1 * (service.cars.length - 1);
          } else {
            result += service.cars.length * service.duration + -1 * service.cars.length;
          }
        }
      }
      return result;
    } else {
      return 0;
    }
  }

  return {
    categorySessionItems,
    selectedCategoryIndex,
    setCategorySessionItems,
    setSelectedCategoryIndex,
    getSelectedCategory,
    getSelectedCategoryTotalDuration,
  };
};

export default useCategorySession;
