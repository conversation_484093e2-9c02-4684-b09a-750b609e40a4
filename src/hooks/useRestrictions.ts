import moment, { Moment } from "moment";
import { useEffect, useMemo, useState } from "react";
import { ZoneService } from "../services/ZoneService";

const useRestrictions = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [restrictions, setRestrictions] = useState<
    | {
        restrictionStartTime: string;
        restrictionEndTime: string;
        expiredAt: string | null;
        fsas: {
          name: string;
        }[];
        technicianId: number;
      }[]
    | undefined
  >();

  useEffect(() => {
    fetchRestrictions(moment());
  }, []);

  async function fetchRestrictions(selectedDate: Moment) {
    setIsLoading(true);
    console.log("selectedDate = " + selectedDate);
    const restrictions = await ZoneService.getRestrictions(selectedDate.format("YYYY-MM-DD"));
    setRestrictions(restrictions);
    console.log("restrictions", restrictions);
    setIsLoading(false);
  }

  function getIfTimeIsRestricted(time: moment.Moment, fsa: string, technicianId: number) {
    if (isLoading) {
      throw new Error("Restrictions are still being fetched");
    }
    // console.log("getIfTimeIsRestricted", technicianId + " " + fsa);

    // Only check restrictions that apply to this specific technician
    const technicianRestrictions = restrictions!.filter(
      (restriction) => restriction.technicianId === technicianId,
    );

    for (const restriction of technicianRestrictions) {
      const restrictionStartTimeWithoutZ = restriction.restrictionStartTime.slice(0, -1);
      const restrictionEndTimeWithoutZ = restriction.restrictionEndTime.slice(0, -1);

      const withoutZ = moment(restrictionStartTimeWithoutZ).format("YYYY-MM-DD");

      if (withoutZ === time.format("YYYY-MM-DD")) {
        if (
          moment(time).isBetween(
            restrictionStartTimeWithoutZ,
            restrictionEndTimeWithoutZ,
            undefined,
            "[]",
          )
        ) {
          if (fsa !== "") {
            const index = restriction.fsas.findIndex((item) => item.name === fsa);
            if (index === -1) {
              return true;
            }
          } else {
            throw new Error("FSA is missing value");
          }
        }
      }
    }

    return false;
  }

  return {
    isFetching: isLoading,
    restrictions,
    fetchRestrictions,
    getIfTimeIsRestricted,
  };
};

export default useRestrictions;
