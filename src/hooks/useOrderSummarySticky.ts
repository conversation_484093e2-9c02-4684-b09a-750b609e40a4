import { useContext } from "react";
import { globalContext } from "../contexts/GlobalContext";
import { MD } from "../values";
import useScrollPosition from "./useScrollPosition";

const SCROLLBAR_WIDTH = 16;
const DISTANCE_FROM_TOP_ON_BLOCK = 40;
const DISTANCE_FROM_TOP_ON_STICKY = 24;
const PAGE_RIGHT_PADDING = 40;
const ORDERSUMMARY_BORDER_WIDTH = 0.5;

export function useOrderSummarySticky(
  scrollableElementRef: React.RefObject<HTMLDivElement>,
  orderSummaryRef: React.RefObject<HTMLDivElement>,
) {
  const scrollPositionHook = useScrollPosition(scrollableElementRef!, orderSummaryRef);
  const deviceWidth = useContext(globalContext)?.dimensionWidth;

  function getRight() {
    if (deviceWidth && deviceWidth >= 1440) {
      if (scrollPositionHook.isScrolledToBottom) {
        return "auto";
      } else {
        if (
          scrollPositionHook.scrollPosition <
          DISTANCE_FROM_TOP_ON_BLOCK - DISTANCE_FROM_TOP_ON_STICKY
        ) {
          return "auto";
        } else {
          return `calc(((100vw - 1360px - ${SCROLLBAR_WIDTH}px))/2 + ${SCROLLBAR_WIDTH}px - ${ORDERSUMMARY_BORDER_WIDTH}px)`;
        }
      }
    } else {
      if (scrollPositionHook.isScrolledToBottom) {
        return "auto";
      } else {
        if (
          scrollPositionHook.scrollPosition <
          DISTANCE_FROM_TOP_ON_BLOCK - DISTANCE_FROM_TOP_ON_STICKY
        ) {
          return "auto";
        } else {
          return PAGE_RIGHT_PADDING + SCROLLBAR_WIDTH - ORDERSUMMARY_BORDER_WIDTH;
        }
      }
    }
  }

  function getTop() {
    const result =
      scrollPositionHook!.scrollHeight - orderSummaryRef.current?.clientHeight! - 40 - 40;
    if (scrollPositionHook!.isScrolledToBottom) {
      return result > 0 ? result : 0;
    } else {
      if (
        scrollPositionHook!.scrollPosition <
        DISTANCE_FROM_TOP_ON_BLOCK - DISTANCE_FROM_TOP_ON_STICKY
      ) {
        return "auto";
      } else {
        return 145;
      }
    }
  }

  function getPosition() {
    if (scrollPositionHook!.isScrolledToBottom) {
      return "relative";
    } else {
      if (
        scrollPositionHook!.scrollPosition <
        DISTANCE_FROM_TOP_ON_BLOCK - DISTANCE_FROM_TOP_ON_STICKY
      ) {
        return "relative";
      } else {
        return "fixed";
      }
    }
  }

  function getDisplay() {
    if (deviceWidth && deviceWidth >= MD) {
      if (
        scrollPositionHook!.scrollPosition <
        DISTANCE_FROM_TOP_ON_BLOCK - DISTANCE_FROM_TOP_ON_STICKY
      ) {
        return "none";
      } else {
        if (scrollPositionHook!.isScrolledToBottom) {
          return "none";
        } else {
          return "block";
        }
      }
    } else {
      return "none";
    }
  }
  return {
    getRight,
    getTop,
    getPosition,
    getDisplay,
  };
}
