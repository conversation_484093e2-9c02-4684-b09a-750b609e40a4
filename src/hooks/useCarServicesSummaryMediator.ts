import { useEffect } from "react";
import { useCarServices } from "./useCarServices";
import { useOrderSummary } from "./useOrderSummary";
import { ServiceContent } from "../types/ServiceContent";

export function useCarServicesSummaryMediator(
  orderSummaryHook: ReturnType<typeof useOrderSummary>,
  carServiceHook: ReturnType<typeof useCarServices>,
) {
  useEffect(() => {
    console.log("useCarServicesSummaryMediator carServiceHook.carServices", carServiceHook.carServices);
    orderSummaryHook.setData(ServiceContent.convertToServiceCar(carServiceHook.carServices));
  }, [carServiceHook.carServices]);

  return {};
}
