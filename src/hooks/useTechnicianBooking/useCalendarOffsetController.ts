import { useState } from "react";

const shortDayNames = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];

export function useCalendarOffsetController(colCount: number) {
  const [dateOffset, setDateOffset] = useState<Date>(new Date());

  function handleOnClickCalendarNextWeek() {
    setDateOffset((prev) => {
      let result = new Date(prev.getTime());
      result.setDate(prev.getDate() + colCount);
      const now = new Date();
      return result;
    });
  }

  function handleOnClickCalendarPrevWeek() {
    setDateOffset((prev) => {
      let result = new Date(prev.getTime());
      result.setDate(prev.getDate() - colCount);
      if (result < new Date()) {
        return new Date();
      } else {
        return result;
      }
    });
  }

  function handleOnClickCalendarToday() {
    setDateOffset(new Date());
  }

  function resetDateOffset() {
    setDateOffset(new Date());
  }

  function getDateItemsFromOffset(count: number) {
    let result = [];
    let date = new Date(dateOffset.getTime());

    for (let i = 0; i < count; i++) {
      result.push({
        year: date.getFullYear(),
        month: date.getMonth() + 1,
        date: date.getDate(),
        day: shortDayNames[date.getDay()],
      });
      date.setDate(date.getDate() + 1);
    }

    return result;
  }

  return {
    dateOffset,
    handleOnClickCalendarNextWeek,
    handleOnClickCalendarPrevWeek,
    handleOnClickCalendarToday,
    resetDateOffset,
    getDateItemsFromOffset,
  };
}
