import moment from "moment";
import { useEffect, useState } from "react";
import { CalendarService } from "../../services/CalendarService";
import { Technician } from "../../types";
import { BusinessRule } from "../../types/BusinessRule";
import { CalendarBlock, CalendarBlockType } from "../../types/CalendarBlock";
import { CategorySessionItem } from "../../types/Category";
import { GoogleCalendarBlock } from "../../types/GoogleCalendarBlock";
import { useCalendarOffsetController } from "./useCalendarOffsetController";

export function useTechnicianBooking() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<CategorySessionItem[]>([]);
  const [selectedCategoryIndex, setSelectedCategoryIndex] = useState<number>(-1);

  const [selectedBookingIndex, setSelectedBookingIndex] = useState<
    | {
        year: number;
        month: number;
        date: number;
        index: number;
      }
    | undefined
  >();

  function getMomentSelectedTimeBlockTime() {
    // const result = moment().set({
    //   hour: parseInt(selectedTmpBlockTime.startTime.split(":")[0]),
    //   minute: parseInt(selectedTmpBlockTime.startTime.split(":")[1]),
    //   second: 0,
    //   millisecond: 0,
    // });
    // return result;
  }

  const calendarControllerHook = useCalendarOffsetController(7);

  const [clickedBookingIndex, setClickedBookingIndex] = useState<number | null>(null);
  const [bookingData, setBookingData] = useState<CalendarBlock[]>();
  const [selectedTechnician, setSelectedTechnician] = useState<Technician | null>(null);

  useEffect(() => {
    fetchCalendarData();
    // console.log("useTechnicianBooking useEffect");
  }, [calendarControllerHook.dateOffset]);

  async function fetchCalendarData() {
    setIsLoading(true);
    const startDate = moment(calendarControllerHook.dateOffset).format("YYYY-MM-DD");
    const endDate = moment(startDate).add(7, "days").format("YYYY-MM-DD");
    const result = await CalendarService.get(startDate, endDate);
    const dividedEvent = divideIntoNonThroughDayEvent(result.calendarBlocks).filter(
      (item) => item.duration > 0 || item.startDateTime !== undefined,
    );
    console.log("dividedEvent", dividedEvent);

    let recurredEvent: (CalendarBlock | undefined)[] = [];
    for (let i = 0; i < dividedEvent.length; i++) {
      if (dividedEvent[i].type === CalendarBlockType.GOOGLE && dividedEvent[i].rrule) {
        recurredEvent.push(
          ...BusinessRule.getRRuledGoogleCalendarBlock(
            dividedEvent[i] as GoogleCalendarBlock,
            new Date(startDate),
            new Date(endDate),
          ),
        );
      } else {
        recurredEvent.push(dividedEvent[i]);
      }
    }

    for (let i = 0; i < recurredEvent.length; i++) {
      if (recurredEvent[i] !== undefined) {
        if (recurredEvent[i]!.type === CalendarBlockType.GOOGLE) {
          const block = recurredEvent[i] as GoogleCalendarBlock;
          if (block.originalStartDateTime) {
            const splittedName = block.eventId.split("_");
            const originalEventId = splittedName.slice(0, splittedName.length - 1).join("_");
            const originalRecurredEventIndex = recurredEvent.findIndex((item) => {
              if (item !== undefined && "eventId" in item) {
                return (
                  item.eventId === originalEventId &&
                  moment(block.originalStartDateTime).isSame(moment(item.startDateTime))
                );
              } else {
                return false;
              }
            });
            recurredEvent[originalRecurredEventIndex] = undefined;

            if (block.startDateTime === undefined) {
              recurredEvent[i] = undefined;
            }
          }
        }
      }
    }

    setBookingData(recurredEvent.filter((item) => item !== undefined) as CalendarBlock[]);
    setIsLoading(false);
  }

  function divideIntoNonThroughDayEvent(events: CalendarBlock[]): CalendarBlock[] {
    let dayDividedResult = [];
    for (const block of events) {
      const dividedResult = BusinessRule.getNonThroughDayCalendarBlock(block);
      dayDividedResult.push(...dividedResult);
    }

    return dayDividedResult;
  }

  /** Calendar util functions */
  function getCategoryTotalDuration(categoryIndex: number) {
    if (categoryIndex !== -1) {
      let result = 0;
      for (let i = 0; i < categories[categoryIndex].services.length; i++) {
        result += categories[categoryIndex].services[i].cars.length;
      }
      return result;
    } else {
      return 0;
    }
  }

  return {
    isLoading,
    categories,
    selectedCategoryIndex,
    setSelectedCategoryIndex,
    calendarControllerHook,
    clickedBookingIndex,
    setClickedBookingIndex,
    bookingData,
    setBookingData,
    selectedBookingIndex,
    setSelectedBookingIndex,
    getMomentSelectedTimeBlockTime,
    selectedTechnician,
    getCategoryTotalDuration,
    fetchCalendarData,
  };
}
