import { useMemo, useState } from "react";

function usePassword() {
  const [pwd, setPwd] = useState("");
  const [confirmPwd, setConfirmPwd] = useState("");
  const [isPasswordInvalid, setIsPasswordInvalid] = useState(false);
  const [passwordErrorMsg, setPasswordErrorMsg] = useState("");

  const canContinue = useMemo(() => {
    return pwd.length > 0 && confirmPwd.length > 0;
  }, [pwd, confirmPwd]);

  function validatePassword() {
    if (pwd.length < 8) {
      setPasswordErrorMsg("Your password needs at least 8 characters");
    } else if (pwd.toLowerCase() == pwd) {
      setPasswordErrorMsg("Your password needs an uppercase letter");
    } else if (pwd.toUpperCase() == pwd) {
      setPasswordErrorMsg("Your password needs a lowercase letter");
    } else if (!/\d/.test(pwd)) {
      setPasswordErrorMsg("Your password needs a number");
    } else if (pwd !== confirmPwd) {
      setPasswordErrorMsg("Password doesn't match");
    } else {
      setIsPasswordInvalid(false);
      return true;
    }

    setIsPasswordInvalid(true);
    return false;
  }

  function resetPasswordMsg() {
    setPasswordErrorMsg("");
  }

  return {
    pwd,
    setPwd,
    confirmPwd,
    setConfirmPwd,
    isPasswordInvalid,
    validatePassword,
    passwordErrorMsg,
    canContinue,
    resetPasswordMsg,
  };
}

export default usePassword;
