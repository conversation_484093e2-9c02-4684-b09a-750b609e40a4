import { useEffect, useState } from "react";
import { AddressService, NewAddress } from "../services/AddressService";

export function useAddresses() {
  const [isFetchLoading, setIsFetchLoading] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);

  const [addresses, setAddresses] = useState<NewAddress[]>([]);
  const [isAddModalOn, setIsAddModalOn] = useState<boolean>(false);

  const [selectedAddressIndex, setSelectedAddressIndex] = useState<number>(-1);
  const [editingAddressIndex, setEditingAddressIndex] = useState<number>(-1);
  const [deletingAddressIndex, setDeletingAddressIndex] = useState<number>(-1);

  useEffect(() => {
    fetch();
  }, []);

  useEffect(() => {
    autoSelect();
  }, [addresses]);

  async function fetch() {
    setIsFetchLoading(true);
    const addresses = await AddressService.get();
    setAddresses(addresses);
    setIsFetchLoading(false);
  }

  function autoSelect() {
    if (addresses.length === 1) {
      setSelectedAddressIndex(0);
    } else {
      setSelectedAddressIndex(-1);
    }
  }

  function select(index: number) {
    setSelectedAddressIndex(index);
  }

  function openAddModal() {
    setIsAddModalOn(true);
  }

  function closeAddModal() {
    setIsAddModalOn(false);
  }

  async function addAddress(
    street1: string,
    street2: string,
    city: string,
    province: string,
    postal: string,
    country: string,
    customerName: string,
    customerPhone: string,
  ) {
    const newAddress = await AddressService.create(
      street1,
      street2,
      city,
      province,
      postal,
      country,
      customerName,
      customerPhone,
    );
    fetch();
    closeAddModal();
  }

  function openEditModal(index: number) {
    setEditingAddressIndex(index);
  }

  function closeEditModal() {
    setEditingAddressIndex(-1);
  }

  function openDeleteModal(index: number) {
    setDeletingAddressIndex(index);
  }

  function closeDeleteModal() {
    setDeletingAddressIndex(-1);
  }

  async function deleteAddress() {
    if (addresses && deletingAddressIndex < addresses.length && deletingAddressIndex >= 0) {
      setIsDeleteLoading(true);
      await AddressService.delete(addresses[deletingAddressIndex].addressId);
      setIsDeleteLoading(false);
      if (deletingAddressIndex === selectedAddressIndex) {
        setSelectedAddressIndex(-1);
      }
      setAddresses(addresses.filter((_, i) => i !== deletingAddressIndex));
      closeDeleteModal();
    } else {
      console.log(deletingAddressIndex);
    }
  }

  return {
    addresses,

    select,
    selectedAddressIndex,

    fetch,
    isFetchLoading,
    isAddModalOn,
    openAddModal,
    closeAddModal,
    addAddress,

    editingAddressIndex,
    openEditModal,
    closeEditModal,

    deletingAddressIndex,
    openDeleteModal,
    closeDeleteModal,
    deleteAddress,
    isDeleteLoading,
  };
}
