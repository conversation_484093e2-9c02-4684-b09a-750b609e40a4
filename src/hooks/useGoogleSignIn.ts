import { CognitoHostedUIIdentityProvider } from "@aws-amplify/auth";
import { Auth, Hub } from "aws-amplify";
import { getCookie } from "cookies-next";
import { NextRouter } from "next/router";
import { event } from "nextjs-google-analytics";
import { useEffect, useState } from "react";
import { Mixpanel } from "../functions/mixpanel";
import { fetchUser } from "../pages/login";

function useGoogleSignIn(router: NextRouter) {
  const [isGoogleLoading, setIsGoogleLoading] = useState<boolean>(false);

  useEffect(() => {
    const unsubscribe = Hub.listen("auth", async ({ payload: { event: hubEvent, data } }) => {
      switch (hubEvent) {
        case "signIn":
          if (data.username.includes("google")) {
            try {
              setIsGoogleLoading(true);
              await fetchUser(router);
              event("google_login", {
                category: "Auth",
                label: "Sign in with Google.",
              });
            } catch (error: any) {
              console.error(error);
            }
          }
          break;
        case "signOut":
          break;
        default:
          break;
      }
    });

    return unsubscribe;
  }, []);

  async function handleOnClickSignInWithGoogle() {
    if (getCookie("localConsent") === true) {
      console.log("hi");
      Mixpanel.track("Sign in with Google Clicked");
    }
    setIsGoogleLoading(true);
    try {
      await Auth.federatedSignIn({
        provider: CognitoHostedUIIdentityProvider.Google,
      });
    } catch (error) {
      console.error(error);
      setIsGoogleLoading(false);
    }
  }

  return { isGoogleLoading, handleOnClickSignInWithGoogle };
}

export default useGoogleSignIn;
