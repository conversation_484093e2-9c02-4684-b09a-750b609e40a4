import { useEffect, useState } from "react";
import { useCarServiceExtensions } from "./useCarServicesExtensions";

export function useSeeMoreAddons(
  carServiceExtensionsHook: ReturnType<typeof useCarServiceExtensions>,
) {
  const [seeMoreAddonsIndexArr, setSeeMoreAddonsIndexArr] = useState<number[]>([]);

  useEffect(() => {
    if (
      carServiceExtensionsHook.carServicesExtensions &&
      carServiceExtensionsHook.carServicesExtensions[0].cars
    ) {
      const newSeeMoreServiceIndexArr = _initializeSeeMoreAddonsIndexArr(
        carServiceExtensionsHook.carServicesExtensions[0].cars.length,
      );

      setSeeMoreAddonsIndexArr(newSeeMoreServiceIndexArr);
    }
  }, [carServiceExtensionsHook.carServices?.length]);

  function _initializeSeeMoreAddonsIndexArr(length: number) {
    return Array(length).fill(-1);
  }

  return {
    seeMoreAddonsIndexArr,
    setSeeMoreAddonsIndexArr,
  };
}
