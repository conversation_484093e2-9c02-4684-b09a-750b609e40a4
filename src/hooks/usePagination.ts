import { useState } from "react";

export function usePagination() {
  const [totalCount, setTotalCount] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [numOfRowsPerPage, setNumOfRowsPerPage] = useState<number>(5);

  function onClickPrevPage() {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  }

  function getIsPrevPageDisabled() {
    return currentPage === 1;
  }

  function onClickNextPage() {
    if (totalCount !== null && currentPage < totalCount) {
      setCurrentPage(currentPage + 1);
    }
  }

  function getIsNextPageDisabled() {
    return totalCount === null || currentPage === totalCount;
  }

  function resetCurrentPage() {
    setCurrentPage(1);
  }

  function resetTotalCount() {
    setTotalCount(null);
  }

  return {
    totalCount,
    setTotalCount,
    currentPage,
    setCurrentPage,
    numOfRowsPerPage,
    setNumOfRowsPerPage,
    onClickPrevPage,
    getIsPrevPageDisabled,
    onClickNextPage,
    getIsNextPageDisabled,
    resetCurrentPage,
    resetTotalCount,
  };
}
