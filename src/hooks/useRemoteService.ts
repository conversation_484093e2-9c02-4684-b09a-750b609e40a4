import { useEffect, useState } from "react";
import { apiListServices } from "../functions/api/service";
import { Service } from "../types";

export function useRemoteService() {
  const [remoteServices, setRemoteServices] = useState<Service[]>();

  useEffect(() => {
    fetchServices();
  }, []);

  async function fetchServices() {
    const services = await apiListServices();
    console.log("services", services);
    setRemoteServices(services);
  }

  return {
    remoteServices,
  };
}
