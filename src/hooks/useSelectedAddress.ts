import { useEffect, useMemo, useState } from "react";
import { NewAddress } from "../services/AddressService";
import { SessionService } from "../services/SessionService";

const useSelectedAddress = () => {
  const [selectedAddress, setSelectedAddress] = useState<NewAddress | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    fetch();
  }, []);

  function getFSA() {
    if (selectedAddress === null) {
      return null;
    }

    return selectedAddress.postal.split(" ")[0];
  }

  async function fetch() {
    try {
      setIsLoading(true);
      let sessionAddress: NewAddress | "";
      sessionAddress = await SessionService.getSessionAddress();
      if (sessionAddress && sessionAddress !== null) {
        setSelectedAddress(sessionAddress);
      } else {
        // TODO: Should assert that option. There's no case this happens except bug.
        setSelectedAddress(null);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }

  return {
    isLoading,
    selectedAddress,
    getFSA,
    fetch,
  };
};

export default useSelectedAddress;
