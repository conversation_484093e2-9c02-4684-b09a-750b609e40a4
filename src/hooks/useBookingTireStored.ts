import { useState } from "react";

export function useBookingTireStored() {
  const [bookingTireStored, setBookingTireStored] = useState<"YES" | "NO" | "">("");
  const [error, setError] = useState<string>("");

  function handleError() {
    setError("Please check this form");
  }

  function handleResetError() {
    setError("");
  }

  function handleOnClick(value: "YES" | "NO") {
    if (value === "YES") {
      setBookingTireStored("YES");
    } else {
      setBookingTireStored("NO");
    }
  }

  return {
    bookingTireStored,
    error,

    handleError,
    handleResetError,
    handleOnClick,
  };
}
