import { useState } from "react";

export function useAlertModal() {
  const [isOn, setIsOn] = useState<boolean>(false);
  const [type, setType] = useState<"SUCCESS" | "WARNING">("WARNING");
  const [title, setTitle] = useState<string>("");
  const [description, setDescription] = useState<React.ReactNode>();
  const [isButton, setIsButton] = useState<boolean>(false);
  const [buttonTitle, setButtonTitle] = useState<string>("");

  function handleSetType() {
    if (type === "SUCCESS") {
      setType("WARNING");
    } else if (type === "WARNING") {
      setType("SUCCESS");
    }
  }

  function resetType() {
    setType("WARNING");
  }

  return {
    isOn,
    setIsOn,

    type,
    setType,

    title,
    setTitle,

    description,
    setDescription,

    buttonTitle,
    setButtonTitle,

    isButton,
    setIsButton,

    handleSetType,

    resetType,
  };
}
