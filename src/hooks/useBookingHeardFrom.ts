import { useState } from "react";

export function useBookingHeardFrom() {
  const [bookingHeardFrom, setBookingHeardFrom] = useState<string>("");
  const [error, setError] = useState<string>("");

  function handleBookingHeardFrom(e: any) {
    setBookingHeardFrom(e.target.value);
  }

  function handleError() {
    setError("Please fill in this form");
  }

  function handleResetError() {
    setError("");
  }

  return {
    bookingHeardFrom,
    handleBookingHeardFrom,

    error,
    handleError,
    handleResetError,
  };
}
