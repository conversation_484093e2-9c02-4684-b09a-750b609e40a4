import { useEffect, useRef, useState } from "react";
import { Service, isAddonEqual } from "../types";
import { Addon } from "../types/Addon";
import { Car } from "../types/Car";

export function useAddAddon(data: {
  onAddAddons: (addons: Addon[]) => void;
  getIsCarAlreadySelected: (carId: number) => boolean;
  getIsAddonInCarServiceExtensions: (addon: Addon) => boolean;
  onClearAddons: () => void;
}) {
  const [selectedCar, setSelectedCar] = useState<Car | undefined>();
  const [selectedServices, setSelectedServices] = useState<Service[] | undefined>([]);
  const [isSelectAddonModalOn, setIsSelectAddonModalOn] = useState<boolean>(false);
  const [selectedAddons, setSelectedAddons] = useState<Addon[]>([]);

  // For indicating flag of displayed addon clicked.
  // To distinguish data addition method between modal and page
  const displayedAddonClickedRef = useRef<any>(false);

  // For adding addon data in page(Not modal) to carServiceExtension.
  useEffect(() => {
    if (displayedAddonClickedRef.current === true) {
      data.onAddAddons(selectedAddons);
      displayedAddonClickedRef.current = false;
    }
  }, [selectedAddons]);

  useEffect(() => {
    console.log("selectedAddons = " + JSON.stringify(selectedAddons));
  }, [selectedAddons]);

  function handleOnClickSubmitOnSelectAddonModal(addons: Addon[]) {
    data.onAddAddons(addons);
  }

  function getIsAddonInArrayByUsingNotModal(addon: Addon) {
    return data.getIsAddonInCarServiceExtensions(addon);
  }

  function handleOnClickClearAddonInPage() {
    handleOnClickClearAddon();
    data.onClearAddons();
  }

  function findNonMatchingAddons(displayedAddons: Addon[], selectedAddons: Addon[]): Addon[] {
    const nonMatchingAddons = selectedAddons.filter(
      (itemB) => !displayedAddons.some((itemA) => itemA.id === itemB.id),
    );
    return nonMatchingAddons;
  }

  function handleOnClickEditAddon(car: Car, services: Service[], addons: Addon[]) {
    let tmpArr = [];
    let tmpArr2 = [];
    tmpArr = addons;
    setSelectedAddons(tmpArr);
    tmpArr2 = services;
    setSelectedCar(car);
    setSelectedServices(tmpArr2);
  }

  function handleOnClickClearAddon() {
    let tmpArr: Addon[] = [];
    setSelectedAddons(tmpArr);
  }

  function handleOnClickAddonBlock(target: Addon) {
    if (getIsAddonAlreadySelected(target)) {
      setSelectedAddons((prev) => prev.filter((item) => !isAddonEqual(item, target)));
    } else {
      setSelectedAddons((prev) => prev.concat(target));
    }
  }

  function getIsAddonAlreadySelected(target: Addon) {
    for (let i = 0; i < selectedAddons.length; i++) {
      if (isAddonEqual(selectedAddons[i], target)) {
        return true;
      }
    }

    return false;
  }

  return {
    selectedAddons,
    setSelectedAddons,

    isSelectAddonModalOn,
    setIsSelectAddonModalOn,

    handleOnClickAddonBlock,

    getIsAddonInCarServiceExtensions: getIsAddonInArrayByUsingNotModal,
    getIsAddonInArray: getIsAddonAlreadySelected,
    handleOnClickAddAddon: handleOnClickSubmitOnSelectAddonModal,
    handleOnClickEditAddon,
    handleOnClickClearAddon,
    handleOnClickClearAddonInPage,
    findNonMatchingAddons,

    displayedAddonClickedRef,
  };
}
