import moment from "moment";
import { useState } from "react";

const useCalendarSelection = (initial?: {
  year?: number;
  month?: number;
  date?: number;
  time?: string;
}) => {
  const [selectedYear, setSelectedYear] = useState<moment.Moment | null>(
    initial && initial.year ? moment(initial.year, "YYYY") : null,
  );
  const [selectedMonth, setSelectedMonth] = useState<moment.Moment | null>(
    initial && initial.month ? moment(initial.month, "MM") : null,
  );
  const [selectedDate, setSelectedDate] = useState<moment.Moment | null>(
    initial && initial.date ? moment(initial.date, "DD") : null,
  );
  const [selectedTime, setSelectedTime] = useState<moment.Moment | null>(
    initial && initial.time ? moment(initial.time, "HH:mm A") : null,
  );

  function getSelectedYear() {
    return selectedYear;
  }

  function getSelectedYearMonth() {
    if (selectedYear !== null && selectedMonth !== null) {
      return moment(`${selectedYear.format("YYYY")}-${selectedMonth.format("MM")}`);
    } else {
      return null;
    }
  }

  function getSelectedYearMonthDate() {
    if (selectedYear !== null && selectedMonth !== null && selectedDate !== null) {
      return moment(
        `${selectedYear.format("YYYY")}-${selectedMonth.format("MM")}-${selectedDate.format("DD")}`,
      );
    } else {
      return null;
    }
  }

  function getSelectedYearMonthDateHour() {
    if (
      selectedYear !== null &&
      selectedMonth !== null &&
      selectedDate !== null &&
      selectedTime !== null
    ) {
      return moment(
        `${selectedYear.format("YYYY")}-${selectedMonth.format("MM")}-${selectedDate.format(
          "DD",
        )} ${selectedTime.format("HH:mm")}`,
      );
    } else {
      return null;
    }
  }

  function selectYear(year: number) {
    setSelectedYear(moment(year, "YYYY"));
  }

  function selectMonth(month: number) {
    setSelectedMonth(moment(month, "MM"));
  }

  function selectDate(date: number) {
    const currentYear = selectedYear ? selectedYear.year() : undefined;
    const currentMonth = selectedMonth ? selectedMonth.month() : undefined;

    const newSelectedDate = moment({
      year: currentYear,
      month: currentMonth,
      date: date,
    });

    setSelectedDate(newSelectedDate);
  }

  function selectTime(time: string) {
    setSelectedTime(moment(time, "HH:mm A"));
  }

  function resetTime() {
    if (initial && initial.time) {
      setSelectedTime(moment(initial.time, "HH:mm A"));
    } else {
      setSelectedTime(null);
    }
  }

  function resetDateTime() {
    resetTime();

    if (initial && initial.date) {
      setSelectedDate(moment(initial.date, "DD"));
    } else {
      setSelectedDate(null);
    }
  }

  function resetMonthDateTime() {
    resetDateTime();

    if (initial && initial.month) {
      setSelectedMonth(moment(initial.month, "MM"));
    } else {
      setSelectedMonth(null);
    }
  }

  function resetYearMonthDateTime() {
    resetMonthDateTime();

    if (initial && initial.year) {
      setSelectedYear(moment(initial.year, "YYYY"));
    } else {
      setSelectedYear(null);
    }
  }

  return {
    selectYear,
    selectMonth,
    selectDate,
    selectTime,

    getSelectedYear,
    getSelectedYearMonth,
    getSelectedYearMonthDate,
    getSelectedYearMonthDateHour,

    resetTime,
    resetDateTime,
    resetMonthDateTime,
    resetYearMonthDateTime,

    selectedYear,
    selectedMonth,
    selectedDate,
    selectedTime,
  };
};

export default useCalendarSelection;
