import { useEffect, useState } from "react";

function useScrollPosition(
  elementRef: React.RefObject<HTMLDivElement>,
  targetRef: React.RefObject<HTMLDivElement>,
) {
  const [scrollPosition, setScrollPosition] = useState<number>(0);
  const [windowHeight, setWindowHeight] = useState<number>(0);
  const [scrollHeight, setScrollHeight] = useState<number>(0);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState<boolean>(false);

  const handleScroll = () => {
    if (elementRef.current && targetRef.current) {
      const scrollTop = elementRef.current.scrollTop;
      const windowHeight = elementRef.current.offsetHeight;
      const fullHeight = elementRef.current.scrollHeight;
      const targetHeight = targetRef.current.clientHeight;
      setScrollPosition(scrollTop);
      setWindowHeight(windowHeight);
      setScrollHeight(fullHeight);

      setIsScrolledToBottom(scrollTop + targetHeight + 40 + 40 > fullHeight);
    }
  };

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (elementRef.current) {
        elementRef.current.removeEventListener("scroll", handleScroll);
      }
    };
  }, [elementRef.current]);

  return {
    scrollPosition,
    windowHeight,
    scrollHeight,
    isScrolledToBottom,
  };
}

export default useScrollPosition;
