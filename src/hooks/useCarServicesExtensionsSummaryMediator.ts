import { useEffect } from "react";
import { useCarServiceExtensions } from "./useCarServicesExtensions";
import { useOrderSummary } from "./useOrderSummary";
import { ServiceContent } from "../types/ServiceContent";

export function useCarServiceExtensionsSummaryMediator(
  orderSummaryHook: ReturnType<typeof useOrderSummary>,
  carServiceExtensionsHook: ReturnType<typeof useCarServiceExtensions>,
) {
  useEffect(() => {
    if (carServiceExtensionsHook.carServices) {
      console.log("carServiceExtensionsHook.carServices", carServiceExtensionsHook.carServices);
      orderSummaryHook.setData(
        ServiceContent.convertToServiceCar(carServiceExtensionsHook.carServices),
      );
      orderSummaryHook.setAddonCars(
        ServiceContent.convertToAddonCar(carServiceExtensionsHook.carServices),
      );
    }
  }, [carServiceExtensionsHook.carServices, carServiceExtensionsHook.carServicesExtensions]);
  return {};
}
