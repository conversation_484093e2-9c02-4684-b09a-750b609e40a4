import { useMemo, useState } from "react";
import { SearchedTechnician } from "./useTechnicianSearch";

const useAssignment = () => {
  const [assignedTech, setAssignedTech] = useState<SearchedTechnician | null>(null);
  const [assignedDateTime, setAssignedDateTime] = useState<moment.Moment | null>(null);

  const isReservationReady = useMemo(() => {
    return assignedTech !== null && assignedDateTime !== null;
  }, [assignedTech, assignedDateTime]);

  const isReservationNotMade = useMemo(() => {
    return assignedTech === null && assignedDateTime === null;
  }, [assignedTech, assignedDateTime]);

  function resetReservationState() {
    setAssignedTech(null);
    setAssignedDateTime(null);
  }

  return {
    assignedTech,
    setAssignedTech,
    assignedDateTime,
    setAssignedDateTime,
    isReservationReady,
    isReservationNotMade,
    resetReservationState,
  };
};

export default useAssignment;
