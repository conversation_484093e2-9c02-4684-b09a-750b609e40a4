import { useEffect, useRef, useState } from "react";
import { Availability } from "../@redux/modules/techBooking";
import { apiCreateAvailability, apiListAvailability } from "../functions/api/availability";
import { toast } from "react-toastify";
import moment from "moment";

export function useAvailabilities() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [availabilities, setAvailabilities] = useState<Availability[]>([]);
  const [isAvailabilityModalOn, setIsAvailabilityModalOn] = useState<boolean>(false);
  const availabilityTmp = useRef<any>(null);

  const [availabilityInputData, setAvailabilityInputData] = useState<
    {
      dayName: string;
      /** Modal day's start, end select state */
      startTimeInput: string;
      endTimeInput: string;
      /** Modal day's added slots state */
      slots: Availability["slots"];
    }[]
  >([
    {
      dayName: "Monday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
    {
      dayName: "Tuesday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
    {
      dayName: "Wednesday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
    {
      dayName: "Thursday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
    {
      dayName: "Friday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
    {
      dayName: "Saturday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
    {
      dayName: "Sunday",
      startTimeInput: "none",
      endTimeInput: "none",
      slots: [],
    },
  ]);

  useEffect(() => {
    syncLoadedAvailabilitiesToInput();
  }, [isAvailabilityModalOn]);

  function syncLoadedAvailabilitiesToInput() {
    if (isAvailabilityModalOn && availabilities !== undefined) {
      for (const serverItem of availabilities) {
        const index = availabilityInputData.findIndex(
          (item) => item.dayName === serverItem.dayOfWeek,
        );
        if (index === -1) {
          throw new Error("Availability error");
        } else {
          availabilityInputData[index].slots = [...serverItem.slots];
        }
      }
      availabilityTmp.current = [...availabilityInputData];
      setAvailabilityInputData([...availabilityInputData]);
    }
  }

  function handleOnClickAdd(dayIndex: number) {
    if (
      availabilityInputData[dayIndex].startTimeInput !== "none" &&
      availabilityInputData[dayIndex].endTimeInput !== "none"
    )
      availabilityInputData[dayIndex].slots.push({
        startTime: availabilityInputData[dayIndex].startTimeInput!,
        endTime: availabilityInputData[dayIndex].endTimeInput!,
      });
    availabilityInputData[dayIndex].slots.sort((a, b) => {
      if (a.startTime > b.startTime) {
        return 1;
      } else if (a.startTime === b.startTime) {
        return 0;
      } else {
        return -1;
      }
    });
    availabilityInputData[dayIndex].startTimeInput = "none";
    availabilityInputData[dayIndex].endTimeInput = "none";
    setAvailabilityInputData([...availabilityInputData]);
  }

  /** Fetch availabilities on start */
  useEffect(() => {
    fetchAvailabilities();
  }, []);

  async function fetchAvailabilities() {
    const result = await apiListAvailability(undefined, undefined);

    // let sortedResult: Availability[] = [];
    // for(let i=0;i<result.length;i++) {
    //   for(let j=0;j<result[i].slots.length;j++) {
    //     const dayOfWeekOfStart = moment(result[i].slots[j].startTime).format('dddd');
    //     const index = sortedResult.findIndex((item) => item.dayOfWeek === dayOfWeekOfStart);
    //     if(index === -1) {
    //       sortedResult.push({
    //         dayOfWeek: dayOfWeekOfStart,
    //         slots: [result[i].slots[j]]
    //       })
    //     } else {
    //       sortedResult[index].slots.push(result[i].slots[j]);
    //     }
    //   }
    // }

    // for(let i=0;i<sortedResult.length;i++) {
    //   sortedResult[i].slots.sort((a, b) => {
    //     if (a.startTime > b.startTime) {
    //       return 1;
    //     } else if (a.startTime === b.startTime) {
    //       return 0;
    //     } else {
    //       return -1;
    //     }
    //   });
    // }

    // console.log(sortedResult);

    setAvailabilities(result);
  }

  async function handleOnClickUpdate() {
    setIsLoading(true);
    await apiCreateAvailability(
      availabilityInputData.map((item) => ({
        dayOfWeek: item.dayName,
        slots: item.slots.map((slotItem) => ({
          startTime: !getIsAddedTime(slotItem.startTime)
            ? slotItem.startTime
            : new Date(
                2022,
                3,
                3,
                parseInt(slotItem.startTime.substring(0, 2)),
                parseInt(slotItem.startTime.substring(3)),
              ).toISOString(),
          endTime: !getIsAddedTime(slotItem.endTime)
            ? slotItem.endTime
            : new Date(
                2022,
                3,
                3,
                parseInt(slotItem.endTime.substring(0, 2)),
                parseInt(slotItem.endTime.substring(3)),
              ).toISOString(),
        })),
      })),
    );
    await fetchAvailabilities();
    toast.success("Availability updated!", {
      position: "bottom-left",
      autoClose: 1000,
      hideProgressBar: true,
      closeOnClick: true,
      progress: undefined,
    });
    setIsLoading(false);
  }

  function getIsAddedTime(value: string) {
    return value.length <= 10;
  }

  async function handleOnClickDelete(dayIndex: number, slotIndex: number) {
    availabilityInputData[dayIndex].slots = availabilityInputData[dayIndex].slots.filter(
      (item, index) => index !== slotIndex,
    );
    setAvailabilityInputData([...availabilityInputData]);
  }

  return {
    availabilities,
    setAvailabilities,
    isAvailabilityModalOn,
    setIsAvailabilityModalOn,
    availabilityInputData,
    setAvailabilityInputData,
    handleOnClickAdd,
    handleOnClickDelete,
    handleOnClickUpdate,
    isLoading,
    getIsAddedTime,
  };
}
