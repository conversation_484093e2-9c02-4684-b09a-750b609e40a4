import { useRouter } from "next/router";
import { useEffect, useMemo, useState } from "react";
import { SessionService } from "../../services/SessionService";
import { CarServiceExtension } from "../../types/CarServiceExtension";

export function useCarServiceExtensions() {
  const router = useRouter();
  const [carServiceExtensions, setCarServiceExtensions] = useState<CarServiceExtension[]>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedCarAddonIndex, setSelectedCarAddonIndex] = useState<number>(0);
  const [isSessionFetchErrorOccured, setIsSessionFetchErrorOccured] = useState<boolean>(true);

  const carServices = useMemo(() => {
    console.log("useCarServiceExtensions useMemo carServiceExtensions", carServiceExtensions);
    if (carServiceExtensions) {
      return carServiceExtensions[0].cars;
    }
  }, [carServiceExtensions]);

  useEffect(() => {
    fetchSession();
  }, []);

  async function fetchSession() {
    try {
      setIsLoading(true);
      const session: CarServiceExtension[] | "" = await SessionService.getCarServiceExtension();
      console.log("useCarServiceExtensions session: CarServiceExtension[]", session);
      setIsSessionFetchErrorOccured(false);

      if (session !== "" && session.length) {
        const convertedSession = session.map((item) => ({
          ...item,
          cars: item.cars?.map((car) => ({
            ...car,
            addons: car.addons === null ? [] : car.addons,
          })),
        }));
        setCarServiceExtensions(convertedSession);
      }
      setIsSessionFetchErrorOccured(false);
    } catch (error: any) {
      console.log(error);
      setIsSessionFetchErrorOccured(true);
      router.back();
    } finally {
      setIsLoading(false);
    }
  }

  const totalAddons = useMemo(() => {
    if (carServiceExtensions && carServiceExtensions[0].cars) {
      return carServiceExtensions[0].cars.reduce(
        (total, carService) => total + carService.addons.length,
        0,
      );
    } else return 0;
  }, [carServiceExtensions]);

  return {
    isLoading,

    selectedCarAddonIndex,
    setSelectedCarAddonIndex,

    carServicesExtensions: carServiceExtensions,
    setCarServicesExtensions: setCarServiceExtensions,

    carServices,

    isSessionFetchErrorOccured,

    totalAddons,
  };
}
