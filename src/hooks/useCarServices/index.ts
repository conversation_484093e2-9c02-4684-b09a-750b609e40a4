import { useEffect, useMemo, useState } from "react";
import { SessionService } from "../../services/SessionService";
import { Service } from "../../types";
import { Car, isCarEqual } from "../../types/car";
import { DURATION_UNIT, TRAVEL_FEE } from "../../values";
import { ServiceCar } from "../../types/ServiceCar";
import { ServiceContent } from "../../types/ServiceContent";

export interface CarServiceWithoutAddon {
  car: Car;
  services: Service[];
}

export function useCarServices() {
  const [carServices, setCarServices] = useState<CarServiceWithoutAddon[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    fetchSession();
  }, []);

  async function fetchSession() {
    setIsLoading(true);
    const session = await SessionService.getCarServiceSession();
    console.log("useCarServices session", session);
    if (session !== "" && session.length) {
      console.log("setCarServices", session);
      setCarServices((session as any)[0].carServices);
    }
    setIsLoading(false);
  }

  function deleteByIndex(index: number) {
    setCarServices(carServices.filter((_, carServiceIndex) => carServiceIndex !== index));
  }

  const orderSummaryPrices = useMemo(() => {
    let servicePrice = 0;
    let travelFees = 0;
    let numOfServices = 0;

    for (let carService of carServices) {
      for (let service of carService.services) {
        servicePrice += service.price + TRAVEL_FEE;
        numOfServices += 1;
      }
    }
    travelFees = numOfServices ? (numOfServices - 1) * TRAVEL_FEE : 0;

    return {
      servicePrice,
      travelFees,
      totalPrice: servicePrice - travelFees,
    };
  }, [carServices]);

  function addServiceAtSelected(service: Service, selectedCarServiceIndex: number) {
    let newCarServices = [...carServices];
    let newSelectedCarService = newCarServices[selectedCarServiceIndex];

    const index = newSelectedCarService.services.findIndex((item) => item.id === service.id);
    if (index === -1) {
      let newServices = [...newSelectedCarService.services];
      newServices.push(service);
      newSelectedCarService.services = newServices;
    } else {
      newSelectedCarService.services = newSelectedCarService.services.filter(
        (item) => item.id !== service.id,
      );
    }

    newCarServices[selectedCarServiceIndex] = newSelectedCarService;
    setCarServices(newCarServices);
  }

  function setByCars(cars: Car[]) {
    let newCarServices = carServices.filter((carService) => {
      return cars.find((car) => isCarEqual(car, carService.car)) !== undefined;
    });

    for (let car of cars) {
      if (carServices.find((carService) => isCarEqual(car, carService.car)) === undefined) {
        newCarServices.push({
          car,
          services: [],
        });
      }
    }
    setCarServices(newCarServices);
  }

  const calcTotalDuration = useMemo(() => {
    let totalDuration = 0;
    console.log("calcTotalDuration useMemo carServices:", carServices)
    let serviceCars: ServiceCar[] = ServiceContent.convertToServiceCar(carServices);
    console.log("calcTotalDuration useMemo serviceCars:", serviceCars)
    let firstService: ServiceCar["service"] | undefined;
    for (let serviceCar of serviceCars) {
      if (serviceCar.cars.length > 0) {
        if (firstService === undefined) {
          firstService = serviceCar.service;
          totalDuration +=
            serviceCar.cars.length * serviceCar.service.duration +
            -1 * (serviceCar.cars.length - 1);
        } else {
          totalDuration +=
            serviceCar.cars.length * serviceCar.service.duration + -1 * serviceCar.cars.length;
        }
      }
    }

    return totalDuration * DURATION_UNIT;
  }, [carServices]);

  return {
    carServices,
    setCarServices,
    deleteByIndex,
    orderSummaryPrices,
    calcTotalDuration,
    addServiceAtSelected,
    setByCars,
    isLoading,
  };
}
