import { useRouter } from "next/router";
import { useMemo, useState } from "react";
import { CarServiceWithoutAddon } from ".";
import { SessionService } from "../../services/SessionService";

export function useContinueButton(carServices: CarServiceWithoutAddon[]) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const isContinuable = useMemo(() => {
    if (isLoading) return false;
    if (carServices.length === 0) {
      return false;
    }

    for (let i = 0; i < carServices.length; i++) {
      if (carServices[i].services?.length === 0) {
        return false;
      }
    }
    return true;
  }, [isLoading, carServices]);

  async function handleOnClickContinue() {
    try {
      setIsLoading(true);
      let result = [];

      for (let i = 0; i < carServices.length; i++) {
        let item: {
          carId: number;
          serviceIds: number[];
          addonsWithCount: {
            addonId: number;
            count: number;
          }[];
        } = {
          carId: carServices[i].car.id,
          serviceIds: [],
          addonsWithCount: [],
        };

        if (carServices[i].services) {
          for (let j = 0; j < carServices[i].services.length; j++) {
            item.serviceIds.push(carServices[i].services[j].id);
          }
          result.push(item);
        }
      }

      const creationResult = await SessionService.createCarServiceSession(result);
      // router.push("/booking_calendar");
      router.push("/booking_address");
    } catch (error) {
      setIsLoading(false);
    }
  }

  return {
    isLoading,
    isContinuable,
    handleOnClickContinue,
  };
}
