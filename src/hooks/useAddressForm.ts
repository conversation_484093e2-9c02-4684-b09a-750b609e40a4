import { useEffect, useMemo, useState } from "react";
import { AddressService } from "../services/AddressService";

export function useAddressForm(data: { name: string | null; phone: string | null }) {
  const [name, setName] = useState<string>("");
  const [street1, setStreet1] = useState<string>("");
  const [street2, setStreet2] = useState<string>("");
  const [city, setCity] = useState<string>("");
  const [province, setProvince] = useState<string>("");
  const [postal, setPostal] = useState<string>("");
  const [country, setCountry] = useState<string>("Canada");
  const [phoneRegion, setPhoneRegion] = useState<string>("");
  const [phone, setPhone] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const isContinuable = useMemo(() => {
    return [street1, city, province, postal, country, phone, phoneRegion, name].every(
      (x, index) => {
        return x.length > 0;
      },
    );
  }, [street1, city, province, postal, country, phone, phoneRegion, name]);

  useEffect(() => {
    if (data.name !== null) {
      setName(data.name);
    }
  }, [data.name]);

  useEffect(() => {
    if (data.phone !== null) {
      setPhone(data.phone);
    }
  }, [data.phone]);

  function populateForms(addressLine1: string) {
    setStreet1(addressLine1);
  }

  async function SaveAddress() {
    setIsLoading(true);
    await AddressService.create(street1, street2, city, province, postal, country, name, phone);
    setIsLoading(false);
    resetVal();
  }

  function resetVal() {
    setName("");
    setStreet1("");
    setStreet2("");
    setCity("");
    setProvince("");
    setCountry("");
    setPhone("");
    setPhoneRegion("");
    setPostal("");
  }

  return {
    name,
    street1,
    street2,
    city,
    province,
    postal,
    country,
    phoneRegion,
    phone,
    setName,
    setStreet1,
    setStreet2,
    setCity,
    setProvince,
    setPostal,
    setCountry,
    setPhoneRegion,
    setPhone,

    isLoading,
    setIsLoading,

    populateForms,

    isContinuable,

    SaveAddress,
    resetVal,
  };
}
