import { useContext, useMemo } from "react";
import { globalContext } from "../contexts/GlobalContext";
import useCalendarPicker from "./useCalendarPicker";
import moment from "moment";
import { useTechnicianSearch } from "./useTechnicianSearch";

const useDateTechnicianModalCalendarPicker = (
  technicianSearchHook: ReturnType<typeof useTechnicianSearch>,
) => {
  const gContext = useContext(globalContext);
  const isMobile = useMemo(() => gContext?.isMobile || false, [gContext?.isMobile]);

  const calendarPickerHook = useCalendarPicker(technicianSearchHook.selectedMonth);

  const selectDateTechnicianModalTitle = useMemo(() => {
    if (isMobile) {
      if (calendarPickerHook.step === 0) {
        return "Select a Day";
      } else if (calendarPickerHook.step === 1) {
        return "Select a Time";
      } else if (calendarPickerHook.step === 2) {
        return "Technician That Are Available";
      }
    } else {
      return "Select Date & Time";
    }

    return "";
  }, [isMobile, calendarPickerHook.step]);

  const selectDateTechnicianModalButtonText = useMemo(() => {
    if (isMobile) {
      if (calendarPickerHook.step === 0) {
        return "Select a Day";
      } else if (calendarPickerHook.step === 1) {
        return "Select a Time";
      } else if (calendarPickerHook.step === 2) {
        return "Confirm Schedule";
      }
    } else {
      return "Confirm";
    }

    return "";
  }, [isMobile, calendarPickerHook.step]);

  const showFirstStep = useMemo(() => {
    return !isMobile || calendarPickerHook.step === 0;
  }, [isMobile, calendarPickerHook.step]);

  const showSecondStep = useMemo(() => {
    if ((!isMobile || calendarPickerHook.step === 1) && technicianSearchHook.selectedDate) {
      return true;
    } else {
      return false;
    }
  }, [isMobile, calendarPickerHook.step, technicianSearchHook.selectedDate]);

  const isConfirmButtonDisabled = useMemo(() => {
    if (isMobile) {
      if (calendarPickerHook.step === 0 && technicianSearchHook.isDateSelected) {
        return false;
      } else if (calendarPickerHook.step === 1 && technicianSearchHook.isTimeSelected) {
        return false;
      } else return true;
    } else {
      if (technicianSearchHook.isDateSelected && technicianSearchHook.isTimeSelected) {
        return false;
      } else return true;
    }
  }, [
    gContext?.isMobile,
    calendarPickerHook.step,
    technicianSearchHook.isDateSelected,
    technicianSearchHook.isTimeSelected,
  ]);

  async function onClickBackButton() {
    if (isMobile) {
      if (calendarPickerHook.step > 0) {
        calendarPickerHook.setStep(calendarPickerHook.step - 1);
      }
    }
  }

  const showBackButton = useMemo(() => {
    return (isMobile && calendarPickerHook.step > 0) || false;
  }, [isMobile, calendarPickerHook.step]);

  return {
    ...calendarPickerHook,
    selectDateTechnicianModalTitle,
    selectDateTechnicianModalButtonText,
    isPreviousCalendarMonthDisabled: calendarPickerHook.isPreviousCalendarMonthDisabled,
    showFirstStep,
    showSecondStep,
    showCalendarPicker: isMobile || calendarPickerHook.step === 0 || calendarPickerHook.step === 1,
    isConfirmButtonDisabled,
    onClickBackButton,
    showBackButton,
  };
};

export default useDateTechnicianModalCalendarPicker;
