import { useEffect, useState } from "react";
import { apiListAddons } from "../functions/api/addon";
import { Addon } from "../types/Addon";

export function useRemoteAddon() {
  const [remoteAddons, setRemoteAddons] = useState<Addon[]>();
  const [isAddonModalOn, setIsAddonModalOn] = useState<boolean>(false);

  // For displayed addons in order to advertise
  const [displayedAddons, setDisplayedAddons] = useState<Addon[]>([]);

  useEffect(() => {
    fetchAddons();
  }, []);

  useEffect(() => {
    if (remoteAddons) {
      setDisplayedAddons(remoteAddons.slice(0, 4));
    }
  }, [remoteAddons]);

  async function fetchAddons() {
    const addons = await apiListAddons();
    setRemoteAddons(addons);
  }

  function onOffAddonModal() {
    setIsAddonModalOn(false);
  }

  return {
    remoteAddons,
    isAddonModalOn,
    setIsAddonModalOn,
    onOffAddonModal,

    displayedAddons,
    setDisplayedAddons,
  };
}
