import { useEffect, useMemo, useState } from "react";
import { MD, XL, XXS, XXXS } from "../values";

function getWindowDimensions() {
  const { innerWidth: width, innerHeight: height } = window;
  return {
    width,
    height,
  };
}

export default function useWindowDimensions() {
  const [windowDimensions, setWindowDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  useEffect(() => {
    function handleResize() {
      setWindowDimensions(getWindowDimensions());
    }

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    setScreenSize();

    window.addEventListener("resize", setScreenSize);
    window.addEventListener("orientationchange", setScreenSize);

    return () => {
      window.removeEventListener("orientationchange", setScreenSize);
      window.removeEventListener("resize", setScreenSize);
    };
  }, []);

  function setScreenSize() {
    let vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty("--vh", `${vh}px`);
  }

  const isMobile = useMemo(() => {
    if (windowDimensions !== null && windowDimensions.width !== undefined) {
      return windowDimensions.width < MD;
    } else {
      return false;
    }
  }, [windowDimensions]);

  function getResponsiveModalFullWidthByDeviceSize(webModalWidth: number) {
    if (windowDimensions) {
      if (windowDimensions.width >= XL) {
        if (windowDimensions.width < webModalWidth) {
          return "100vw";
        } else return webModalWidth;
      } else if (windowDimensions.width >= MD) {
        if (windowDimensions.width < webModalWidth) {
          return "100vw";
        } else return webModalWidth;
      } else {
        return "100vw";
      }
    } else {
      return "100vw";
    }
  }

  function getResponsiveModalFullHeightByDeviceSize(webModalHeight: number) {
    if (windowDimensions) {
      if (windowDimensions.width >= XL) {
        if (windowDimensions.height < webModalHeight) {
          return "calc(var(--vh, 1vh) * 100)";
        } else return webModalHeight;
      } else if (windowDimensions.width >= MD) {
        if (windowDimensions.height < webModalHeight) {
          return "calc(var(--vh, 1vh) * 100)";
        } else return webModalHeight;
      } else {
        return "calc(var(--vh, 1vh) * 100)";
      }
    } else {
      return "calc(var(--vh, 1vh) * 100)";
    }
  }

  function getResponsiveModalHeightByDeviceSize(webModalHeight: number, mobileModalHeight: number) {
    if (windowDimensions) {
      if (windowDimensions.width < XL) {
        if (windowDimensions.height < mobileModalHeight) {
          return "calc(var(--vh, 1vh) * 100)";
        } else return mobileModalHeight;
      } else {
        if (windowDimensions.height < webModalHeight) {
          return "calc(var(--vh, 1vh) * 100)";
        } else return webModalHeight;
      }
    } else {
      return "calc(var(--vh, 1vh) * 100)";
    }
  }

  function getResponsiveModalWidthByDeviceSize(webModalWidth: number, mobileModalWidth: number) {
    if (windowDimensions) {
      if (windowDimensions.width < XL) {
        if (windowDimensions.width < mobileModalWidth) {
          return "100vw";
        } else return mobileModalWidth;
      } else {
        if (windowDimensions.width < webModalWidth) {
          return "100vw";
        } else return webModalWidth;
      }
    }
  }

  function getResponsiveModalTooltipWidth(webTargetWidth: number, webModalWidth: number) {
    if (windowDimensions) {
      let responsiveWidth: number = (windowDimensions.width * webTargetWidth) / webModalWidth;

      if (windowDimensions.width >= MD) {
        return webTargetWidth;
      } else if (windowDimensions.width >= XXS && windowDimensions.width < MD) {
        return responsiveWidth;
      } else return windowDimensions.width - 32;
    } else {
      return 0;
    }
  }

  return {
    dimension: windowDimensions,
    isMobile,
    getResponsiveModalFullWidthByDeviceSize,
    getResponsiveModalFullHeightByDeviceSize,
    getResponsiveModalHeightByDeviceSize,
    getResponsiveModalWidthByDeviceSize,
    getResponsiveModalTooltipWidth,
  };
}
