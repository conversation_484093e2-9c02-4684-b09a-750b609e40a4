import { useContext, useEffect, useState } from "react";
import { globalContext } from "../contexts/GlobalContext";

export function useTextTruncationDetector(
  targetContainerRefArr: React.RefObject<HTMLSpanElement[] | null[]>,
  data: any,
) {
  const deviceWidth = useContext(globalContext)?.dimensionWidth;
  const [isTruncated, setIsTruncated] = useState<boolean>(false);

  useEffect(() => {
    if (targetContainerRefArr && targetContainerRefArr.current) {
      for (let i = 0; i < targetContainerRefArr.current.length; i++) {
        if (targetContainerRefArr.current[i] !== null) {
          let containerScrollWidth = targetContainerRefArr.current[i]!.scrollWidth;
          let containerClientWidth = targetContainerRefArr.current[i]!.clientWidth;
          if (containerScrollWidth > containerClientWidth) {
            setIsTruncated(true);
            break;
          }
        }
        setIsTruncated(false);
      }
    }
  }, [data, deviceWidth]);

  return {
    isTruncated,
  };
}
