// This is nothing to do with previous "Availability".

import assert from "assert";
import { SearchedTechnician } from "../hooks/useTechnicianSearch";

export class DayAvailability {
  constructor(
    private day: number,
    private times: {
      time: string;
      technicians: SearchedTechnician[];
    }[],
  ) {}

  getDay() {
    return this.day;
  }

  getTimes() {
    return this.times;
  }

  getTechniciansOnTime(time: string) {
    assert(/^\d{1,2}:\d{2} [AP]M$/.test(time), "Time must be in h:mm A format");

    const timeIndex = this.times.findIndex((item) => item.time === time);
    if (timeIndex === -1) {
      return [];
    } else {
      return this.times[timeIndex].technicians;
    }
  }
}
