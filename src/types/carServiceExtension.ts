import { Addon } from "./Addon";
import { CarService } from "./CarService";

export type CategoryExtension = {
  name: string;
  technicianInfo: {
    id: number;
    name: string;
  };
  time: string;
  duration: number;
  total: number;
};

export interface AddonWithCount extends Addon {
  count: number;
}

export type CarServiceExtension = {
  id: number;
  name: string;
  technician: {
    technicianId: number;
    name: string;
    profilePictureUri: string | null;
    rating: number;
    numOfReviews: number;
  };
  occupiedTime: string;
  timeslot: {
    startDateTime: string;
    endDateTime: string;
  };
  cars?: CarService[];
};
