import { CalendarBlock, CalendarBlockType } from "./CalendarBlock";

export class GoogleCalendarBlock extends CalendarBlock {
  constructor(
    startDateTime: string,
    duration: number,
    previewTitle: string,
    rrule: string | undefined,
    public eventId: string,
    public description: string,
    public location: string,
    public calendarName: string,
    public calendarColor: string,
    public originalStartDateTime?: string,
    public status?: string,
  ) {
    super(startDateTime, duration, CalendarBlockType.GOOGLE, previewTitle, rrule);
  }
}
