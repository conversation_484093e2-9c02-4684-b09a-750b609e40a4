import { CarServiceWithoutAddon } from "../hooks/useCarServices";
import { AddonCar } from "./AddonCar";
import { CarService } from "./CarService";
import { ServiceCar } from "./ServiceCar";

export class ServiceContent {
  static convertToServiceCar(carServices: CarServiceWithoutAddon[]) {
    console.log("convertToServiceCar carServices", carServices);

    let result: ServiceCar[] = []; // empty array

    for (let carService of carServices) { // looping over each item in carServices array. There will only ever be one item in this array Im pretty sure..
      for (let service of carService.services) {  // in the carServices we're now going to loop through each service. this should be all the services that we have selected.
        const idx = result.findIndex((item) => item.service.id === service.id); // check to see if there are any services in the result array that have the same service id that we're currently looking at.

        if (idx === -1) { // if we're all good add this service to the result array
          result.push({
            service: service,
            cars: [carService.car],
          });
        } else {
          result[idx].cars.push(carService.car);
        }
      }
    }
    return result;
  }

  static convertToAddonCar(carServices: CarService[]) {
    let result: AddonCar[] = [];

    for (let carService of carServices) {
      for (let addon of carService.addons) {
        const idx = result.findIndex((item) => item.addon.id === addon.id);

        if (idx === -1) {
          result.push({
            addon: addon,
            cars: [carService.car],
          });
        } else {
          result[idx].cars.push(carService.car);
        }
      }
    }
    return result;
  }
}
