import moment from "moment";
import { DURATION_UNIT, END_TIME, START_TIME } from "../values";

export class Time {
  static generateDaysBetween(start: moment.Moment, end: moment.Moment) {
    let result = [];
    for (let i = moment(start); i.isSameOrBefore(end); i.add(1, "d")) {
      result.push(moment(i));
    }
    return result;
  }

  static generateTimeBetween(
    start: moment.Moment,
    end: moment.Moment,
    duration: number,
    includeEnd: boolean,
  ) {
    let result = [];
    if (includeEnd) {
      for (let i = moment(start); i.isSameOrBefore(end); i.add(duration, "m")) {
        result.push(moment(i));
      }
    } else {
      for (let i = moment(start); i.isBefore(end); i.add(duration, "m")) {
        result.push(moment(i));
      }
    }
    return result;
  }

  static iterateDaysBetween(
    start: moment.Moment,
    end: moment.Moment,
    callback: (date: moment.Moment) => void,
  ) {
    for (let i = moment(start); i.isSameOrBefore(end); i.add(1, "d")) {
      callback(i);
    }
  }

  static iterateTimeBetween(
    start: moment.Moment,
    end: moment.Moment,
    duration: number,
    includeEnd: boolean,
    callback: (time: moment.Moment) => void,
  ) {
    if (includeEnd) {
      for (let i = moment(start); i.isSameOrBefore(end); i.add(duration, "m")) {
        callback(i);
      }
    } else {
      for (let i = moment(start); i.isBefore(end); i.add(duration, "m")) {
        callback(i);
      }
    }
  }

  static getIsTimeConflicting(
    targetTime: {
      start: moment.Moment;
      end: moment.Moment;
    },
    checkList: {
      start: string;
      end: string;
    }[],
  ) {
    // Legacy iteration. If binary search has problem, use this version(should change a bit though)
    //
    // for (const busy of calendarBusy) {
    //   const jEnd = moment(j).add(duration * DURATION_UNIT, "minutes");
    //   sum ++;

    //   if (!(j.isSameOrAfter(busy.end) || jEnd.isSameOrBefore(busy.start))) {
    //     isTechBusy = true;
    //     break;
    //   }
    // }
    //

    // Binary search
    let first = 0,
      last = checkList.length - 1;
    let isTechBusy = false;

    while (first <= last) {
      let mid = Math.floor((first + last) / 2);

      if (targetTime.start.isSameOrAfter(checkList[mid].end)) {
        first = mid + 1;
      } else if (targetTime.end.isSameOrBefore(checkList[mid].start)) {
        last = mid - 1;
      } else {
        isTechBusy = true;
        break;
      }
    }

    return isTechBusy;
  }

  static generateAvailableTimesOnDate(date: moment.Moment) {
    const [startHour, startMinute] = START_TIME.split(":").map(Number);
    const [endHour, endMinute] = END_TIME.split(":").map(Number);

    const start = moment(date).hour(startHour).minute(startMinute);
    const end = moment(date).hour(endHour).minute(endMinute);

    const workingHours = this.generateTimeBetween(start, end, DURATION_UNIT, false);

    return workingHours.filter((time) => {
      const text = time.format("HH:mm A");
      return !(
        text === "12:30 PM" ||
        text === "12:45 PM" ||
        text === "13:00 PM" ||
        text === "13:15 PM"
      );
    });
  }

  static convertSecondToMinute(second: number) {
    return moment.utc(second * 1000).format("m:ss");
  }
}
