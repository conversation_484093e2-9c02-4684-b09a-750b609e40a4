import moment from "moment";
import { RRuleSet, rrulestr } from "rrule";
import { CalendarBlock } from "./CalendarBlock";
import { GoogleCalendarBlock } from "./GoogleCalendarBlock";
import { DURATION_UNIT, RATE_OF_DISCOUNT, TRAVEL_FEE } from "../values";

export class BusinessRule {
  static getRRuledGoogleCalendarBlock(
    googleCalendarBlock: GoogleCalendarBlock,
    createFrom: Date,
    createTo: Date,
  ) {
    if (!googleCalendarBlock.rrule) return [googleCalendarBlock];
    if (googleCalendarBlock.rrule.includes("EXDATE")) return [];

    let result = [];
    const recurredDateResults = this.getRecurredDates(
      new Date(googleCalendarBlock.startDateTime),
      googleCalendarBlock.rrule,
      "America/Toronto",
      createFrom,
      createTo,
    );

    for (let recurredResult of recurredDateResults) {
      result.push(
        new GoogleCalendarBlock(
          recurredResult.toISOString(),
          googleCalendarBlock.duration,
          googleCalendarBlock.previewTitle,
          googleCalendarBlock.rrule,
          googleCalendarBlock.eventId,
          googleCalendarBlock.description,
          googleCalendarBlock.location,
          googleCalendarBlock.calendarName,
          googleCalendarBlock.calendarColor,
          googleCalendarBlock.originalStartDateTime,
        ),
      );
    }

    return result;
  }

  static getRecurredDates(
    dtStart: Date,
    rruleString: string,
    timezone: string,
    createFrom: Date,
    createTo: Date,
  ) {
    const rrule = rrulestr(rruleString, {
      dtstart: dtStart,
      tzid: timezone,
    });

    const rruleSet = new RRuleSet();
    rruleSet.rrule(rrule);

    return rruleSet.between(createFrom, createTo, true);
  }

  static getNonThroughDayCalendarBlock(block: CalendarBlock) {
    if (block.startDateTime === undefined) {
      // return [block]
      return [];
    }

    const newStartTime = moment(block.startDateTime).second(0).toDate();
    const newEndTime = moment(newStartTime)
      .add(block.duration * DURATION_UNIT, "minutes")
      .toDate();

    let dividedResult: any[] = [];
    let remainBlock = { ...block, startDateTime: newStartTime };

    let safetyCount = 100;
    while (
      BusinessRule.getIsThroughDayEvent(moment(remainBlock.startDateTime), moment(newEndTime))
    ) {
      countIteration();
      remainBlock = sliceFirstDate();
    }
    dividedResult.push(remainBlock);

    return dividedResult.map((block) => {
      return {
        ...block,
        startDateTime: moment(block.startDateTime).toISOString(),
      };
    });

    function sliceFirstDate() {
      const durationUntilEndOfDay =
        moment(remainBlock.startDateTime)
          .add(1, "day")
          .startOf("day")
          .diff(moment(remainBlock.startDateTime), "minutes") / 15;

      dividedResult.push({
        ...remainBlock,
        duration: durationUntilEndOfDay,
      });

      let newStartTime = moment(remainBlock.startDateTime);
      newStartTime.add(durationUntilEndOfDay * DURATION_UNIT, "minutes");

      return {
        ...remainBlock,
        startDateTime: newStartTime.toDate(),
        duration: remainBlock.duration - durationUntilEndOfDay,
      };
    }

    function countIteration() {
      safetyCount--;
      if (safetyCount < 0) {
        console.log("block", block);
        throw new Error("Infinite loop detected");
      }
    }
  }

  static getIsThroughDayEvent(startTime: moment.Moment, endTime: moment.Moment) {
    const subtractedEndTime = moment(endTime).subtract(1, "second");

    return (
      startTime.year() !== subtractedEndTime.year() ||
      startTime.month() !== subtractedEndTime.month() ||
      startTime.date() !== subtractedEndTime.date()
    );
  }

  static getIsBookableTime(time: moment.Moment) {
    return moment().add(8, "hours").isSameOrBefore(time);
  }

  static getNearestBookableTimeFromNow() {
    let result = moment();

    if (result.minute() < 15) {
      result.minute(15);
    } else {
      result.add(1, "hour");
      result.minute(0);
    }

    result.add(8, "hours");
    return result;
  }

  static calculateDurationByHour(duration: number) {
    let result: number = 0;

    if (duration === 0) return;
    else {
      return (result = (duration * DURATION_UNIT) / 60);
    }
  }

  static calculateDurationByMin(duration: number) {
    let result: number = 0;

    if (duration === 0) return;
    else {
      return (result = duration * DURATION_UNIT);
    }
  }

  static calculatePrice(price: number) {
    let result: number = 0;

    result = (price + TRAVEL_FEE) / 100;
    return result;
  }

  static calculateAddonPrice(price: number) {
    let result: number = 0;

    result = price / 100;
    return result;
  }

  static calculateEachTargetPrice(price: number, arrLen: number) {
    let eachPrice: number = 0;

    eachPrice = ((price + TRAVEL_FEE) / 100) * arrLen;
    return eachPrice;
  }

  static calculateEachTargetAddonPrice(price: number, arrLen: number) {
    let eachPrice: number = 0;

    eachPrice = (price / 100) * arrLen;
    return eachPrice;
  }

  static calculateDiscountPrice(price: number) {
    let priceOfDiscount: number = 0;
    let discountedPrice: number = 0;

    priceOfDiscount = price * (RATE_OF_DISCOUNT / 100);
    discountedPrice = price * (1 - RATE_OF_DISCOUNT / 100);

    return {
      priceOfDiscount,
      discountedPrice,
    };
  }
}
