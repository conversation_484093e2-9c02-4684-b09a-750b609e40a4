import { SearchedTechnician } from "../hooks/useTechnicianSearch";
import { Car } from "./Car";

export type CategorySessionItem = {
  id: number;
  name: string;
  services: {
    id: number;
    name: string;
    pictureUri: string;
    duration: number;
    price: number;
    cars: Car[];
  }[];
  /**
   * Until multi category, the assigned technician will be managed by "assignedTech", so this field is only used for api return type.
   */
  technician: SearchedTechnician | null;
  timeslot: {
    startDateTime: string;
    endDateTime: string;
  } | null;
  occupiedTime: string | null;
};

export class Category {
  get id(): number {
    return this._id;
  }

  get name(): string {
    return this._name;
  }

  get services(): {
    id: number;
    name: string;
    pictureUri: string;
    cars: Car[];
    price: number;
  }[] {
    return this._services;
  }

  constructor(
    private _id: number,
    private _name: string,
    private _services: {
      id: number;
      name: string;
      pictureUri: string;
      cars: Car[];
      price: number;
    }[],
  ) {}
}
