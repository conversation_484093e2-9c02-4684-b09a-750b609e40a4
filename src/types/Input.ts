import { PhoneNumber } from "./PhoneNumber";

export class Input {
  static formatNewInputIntoPhoneNumber(
    newInputValue: string,
    currentPhoneRegionValue: string,
    isBackspace: boolean,
  ) {
    let newValue = "";

    const regexNotNumber = /[^0-9]/;
    const lastChar = newInputValue.charAt(newInputValue.length - 1);

    if (regexNotNumber.test(lastChar)) {
      if (!isBackspace) return newInputValue.substring(0, newInputValue.length - 1);
    }

    const newPhoneNumber = new PhoneNumber(currentPhoneRegionValue, newInputValue);

    if (newPhoneNumber.isMaxLength()) return newInputValue.substring(0, newInputValue.length - 1);

    if (isBackspace) {
      newValue = newInputValue;
    } else {
      newValue = newPhoneNumber.getFormattedPhoneNumber();
    }

    return newValue;
  }
}
