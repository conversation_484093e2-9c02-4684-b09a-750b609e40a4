import { RoleId, User } from "./User";

export class Technician extends User {
  constructor(
    id: number,
    firstName: string,
    lastName: string,
    email: string,
    phone: string | null,
    roleId: RoleId,
    profilePictureUri: string | null,
    public technicianId: number,
    public biography: string,
    public isCertified: boolean,
  ) {
    super(id, firstName, lastName, email, phone, roleId, profilePictureUri);
  }
}
