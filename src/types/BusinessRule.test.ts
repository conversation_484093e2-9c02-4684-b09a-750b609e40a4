import moment from "moment-timezone";
import { datetime } from "rrule";
import { BusinessRule } from "./BusinessRule";

describe("getIsThroughDayEvent", () => {
  it("nonThroughDayEvent - day end check", () => {
    expect(
      BusinessRule.getIsThroughDayEvent(
        moment().date(15).startOf("day"),
        moment()
          .date(15)
          .startOf("day")
          .add(30 * 48, "minutes"),
      ),
    ).toEqual(false);
  });

  it("nonThroughDayEvent - month end check", () => {
    expect(
      BusinessRule.getIsThroughDayEvent(
        moment().endOf("month").startOf("day"),
        moment()
          .endOf("month")
          .startOf("day")
          .add(30 * 48, "minutes"),
      ),
    ).toEqual(false);
  });

  it("nonThroughDayEvent - year end check", () => {
    expect(
      BusinessRule.getIsThroughDayEvent(
        moment().endOf("year").startOf("day"),
        moment()
          .endOf("year")
          .startOf("day")
          .add(30 * 48, "minutes"),
      ),
    ).toEqual(false);
  });

  it("throughDayEvent - one day", () => {
    expect(
      BusinessRule.getIsThroughDayEvent(
        moment().date(15).startOf("day"),
        moment().date(16).startOf("day").add(1, "second"),
      ),
    ).toEqual(true);
  });

  it("throughDayEvent - one week", () => {
    expect(
      BusinessRule.getIsThroughDayEvent(
        moment().date(15).startOf("day"),
        moment().date(22).startOf("day"),
      ),
    ).toEqual(true);
  });
});

describe("getNonThroughDayCalendarBlock", () => {
  it("isAllDayEvent", () => {
    expect(
      BusinessRule.getNonThroughDayCalendarBlock({
        startDateTime: undefined,
      } as any),
    ).toEqual([]);
  });

  it("nonThroughDayEvent", () => {
    expect(
      BusinessRule.getNonThroughDayCalendarBlock({
        startDateTime: moment().startOf("day").toDate().toISOString(),
        duration: 48,
      } as any),
    ).toEqual([
      {
        startDateTime: moment().startOf("day").toDate().toISOString(),
        duration: 48,
      },
    ]);
  });

  it("throughDayEvent - one day", () => {
    expect(
      BusinessRule.getNonThroughDayCalendarBlock({
        startDateTime: moment().startOf("day").toDate().toISOString(),
        duration: 49,
      } as any),
    ).toEqual([
      {
        startDateTime: moment().startOf("day").toDate().toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30, "minutes")
          .toDate()
          .toISOString(),
        duration: 1,
      },
    ]);
  });

  it("throughDayEvent - one week", () => {
    expect(
      BusinessRule.getNonThroughDayCalendarBlock({
        startDateTime: moment().startOf("day").toDate().toISOString(),
        duration: 48 * 7 + 2,
      } as any),
    ).toEqual([
      {
        startDateTime: moment().startOf("day").toDate().toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30, "minutes")
          .toDate()
          .toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30 * 2, "minutes")
          .toDate()
          .toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30 * 3, "minutes")
          .toDate()
          .toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30 * 4, "minutes")
          .toDate()
          .toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30 * 5, "minutes")
          .toDate()
          .toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30 * 6, "minutes")
          .toDate()
          .toISOString(),
        duration: 48,
      },
      {
        startDateTime: moment()
          .startOf("day")
          .add(48 * 30 * 7, "minutes")
          .toDate()
          .toISOString(),
        duration: 2,
      },
    ]);
  });
});

describe("moment-timezone", () => {
  it("moment(a).tz(b) string", () => {
    expect(moment("2020-01-01T00:00:00+00:00").tz("America/Toronto").format()).toEqual(
      "2019-12-31T19:00:00-05:00",
    );
  });

  it("moment(a).tz(b) date", () => {
    expect(moment("2020-01-01T00:00:00+00:00").tz("America/Toronto").toDate()).toEqual(
      new Date("2020-01-01T00:00:00+00:00"),
    );
  });

  it("moment.tz(a, b)", () => {
    expect(moment.tz("2020-01-01T00:00:00+00:00", "America/Toronto").toDate()).toEqual(
      new Date("2020-01-01T00:00:00+00:00"),
    );
  });

  it("moment.tz(a, b)", () => {
    expect(moment.tz("2020-01-01", "America/Toronto").toDate()).toEqual(
      new Date("2020-01-01T05:00:00+00:00"),
    );
  });
});

describe("getRecurredDates", () => {
  it("daily", () => {
    const result = BusinessRule.getRecurredDates(
      // 2023-01-01 00:00:00 of America/Toronto
      // 2023-01-01 05:00:00 of UTC
      datetime(2023, 1, 1, 0, 0, 0),
      "FREQ=DAILY",
      "America/Toronto",
      datetime(2023, 1, 1, 0, 0, 0),
      datetime(2023, 1, 5, 14, 0, 0),
    );
    expect(result).toEqual([
      new Date("2023-01-01T14:00:00.000Z"),
      new Date("2023-01-02T14:00:00.000Z"),
      new Date("2023-01-03T14:00:00.000Z"),
      new Date("2023-01-04T14:00:00.000Z"),
      new Date("2023-01-05T14:00:00.000Z"),
    ]);
  });

  it("daily - summer time", () => {
    const result = BusinessRule.getRecurredDates(
      // 2023-01-01 00:00:00 of America/Toronto
      // 2023-01-01 05:00:00 of UTC
      datetime(2023, 3, 1, 0, 0, 0),
      "FREQ=DAILY",
      "America/Toronto",
      datetime(2023, 3, 10, 0, 0, 0),
      datetime(2023, 3, 16, 14, 0, 0),
    );
    expect(result).toEqual([
      new Date("2023-03-10T14:00:00.000Z"),
      new Date("2023-03-11T14:00:00.000Z"),
      new Date("2023-03-12T14:00:00.000Z"),
      new Date("2023-03-13T13:00:00.000Z"),
      new Date("2023-03-14T13:00:00.000Z"),
      new Date("2023-03-15T13:00:00.000Z"),
      new Date("2023-03-16T13:00:00.000Z"),
    ]);
  });
});
