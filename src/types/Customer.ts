import { Address } from "./Address";
import { RoleId, User } from "./User";

export class Customer extends User {
  constructor(
    id: number,
    firstName: string,
    lastName: string,
    email: string,
    phone: string | null,
    roleId: RoleId,
    profilePictureUri: string | null,
    public address: Address | null,
  ) {
    super(id, firstName, lastName, email, phone, roleId, profilePictureUri);
  }
}
