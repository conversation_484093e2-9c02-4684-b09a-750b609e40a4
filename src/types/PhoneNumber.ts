export class PhoneNumber {
  constructor(
    private phoneRegion: string,
    private phoneNumber: string,
  ) {
    this.phoneRegion = phoneRegion || "+1";
    this.phoneNumber = phoneNumber.replace(/\D/g, "");
  }

  static getPhoneNumberFromAPI(fullPhoneNumber: string) {
    return new PhoneNumber(fullPhoneNumber.substring(0, 2), fullPhoneNumber.substring(2));
  }

  setPhoneRegion(phoneRegion: string) {
    this.phoneRegion = phoneRegion;
  }

  setPhoneNumber(phoneNumber: string) {
    this.phoneNumber = phoneNumber;
  }

  getPhoneRegion() {
    return this.phoneRegion;
  }

  getPhoneNumber() {
    return this.phoneNumber;
  }

  getFormattedPhoneNumber() {
    if (this.phoneNumber.length === 3) {
      return `(${this.phoneNumber}) `;
    } else if (this.phoneNumber.length === 6) {
      return `(${this.phoneNumber.substring(0, 3)}) ${this.phoneNumber.substring(3)}-`;
    } else if (this.phoneNumber.length === 10) {
      return `(${this.phoneNumber.substring(0, 3)}) ${this.phoneNumber.substring(
        3,
        6,
      )}-${this.phoneNumber.substring(6)}`;
    } else {
      return this.phoneNumber;
    }
  }

  getFormattedFullPhoneNumber() {
    return `${this.phoneRegion} ${this.getFormattedPhoneNumber()}`;
  }

  getFullPhoneNumberForAPI() {
    return `${this.phoneRegion}${this.phoneNumber}`;
  }

  isMaxLength() {
    console.log(this.phoneNumber);
    return this.phoneNumber.length > 10;
  }
}
