import { CalendarBlock, CalendarBlockType } from "./CalendarBlock";
import { Customer } from "./Customer";
import { Technician } from "./Technician";

export enum BookingStatus {
  RESERVED = "RESERVED",
  COMPLETED = "COMPLETED",
}

export class Booking extends CalendarBlock {
  constructor(
    startDateTime: string,
    duration: number,
    type: CalendarBlockType,
    public id: number,
    public customer: Customer,
    public technician: Technician,
    public bookingName: string,
    public status: BookingStatus,
    public createdDateTime: string,
    public services: {
      id: number;
      name: string;
      cars: {
        id: number;
        make: string;
        model: string;
        year: number;
        color: string;
        pictureUri: string;
      }[];
    }[],
    public paymentId: number,
    public amountTotal: number,
    public bookingRef: string,
    public receiptUrl: string,
    public addons: any[],
    public userRating?: number | null,
  ) {
    super(startDateTime, duration, type, bookingName, undefined);
  }
}
