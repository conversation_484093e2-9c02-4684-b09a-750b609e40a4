import { Addon } from "./Addon";
import { Car } from "./Car";

export interface ListTypeBooking {
  id: number;
  dateTime: string;
  bookingRef: string;
  services: {
    id: number;
    name: string;
    cars: Car[];
  }[];
  total: number;
  technician: {
    id: number;
    pictureUri: string;
    name: string;
    phone: string;
  };
  status: "RESERVED";
  receiptUrl: string | null;
  createdDateTime: string;
  startDateTime: string;
}

export interface ListTypeBookingByPagination {
  id: number;
  dateTime: string;
  bookingRef: string;
  services: {
    id: number;
    name: string;
    cars: Car[];
  }[];
  total: number;
  technician: {
    id: number;
    pictureUri: string;
    name: string;
    phone: string;
  };
  status: "RESERVED";
  receiptUrl: string | null;
  createdDateTime: string;
  startDateTime: string;
  pagination: {
    totalCount: number;
    currentPage: number;
  };
}

export interface Service {
  id: number;
  name: string;
  description: string;
  price: number;
  categoryId: number;
  categoryName: string;
  servicePictureUri: string;
  duration: number;
  needContact: number;
}

export interface Technician {
  id: number;
  name: string;
  email: string;
  phone: string;
  createdDateTime: string;
  isCertified: number;
  serviceNames: string;
  services: {
    id: number;
    name: string;
  }[];
  profilePictureUri: string;
}

export interface TechnicianRequest {
  id: number;
  name: string;
  email: string;
  phone: string;
  services: {
    id: number;
    name: string;
  }[];
  location: string;
  profilePictureUri: string;
  licensePictureUri: string;
}

export type BookingTime = {
  type?: "BOOKING" | "NOT_AVAILABLE" | "BLOCKED";
  startTime: string;
  duration: number;
  name?: string;
  customer?: {
    id: number;
    name: string;
    email: string;
    profilePictureUri: string;
    address: Address;
    phone: string;
  };
  services?: {
    id: number;
    name: string;
    cars: Car[];
  }[];
  isAdded?: boolean;
};

export type ManualTime = {
  id: number;
  type: "BOOKING";
  startTime: string;
  duration: number;
  description: string;
};

export interface Address {
  street1: string;
  street2: string | null;
  city: string;
  province: string;
  country: string;
  postal: string;
}

export function isServiceEqual(service1: Service, service2: Service) {
  return service1.id === service2.id;
}

export function isAddonEqual(addon1: Addon, addon2: Addon) {
  return addon1.id === addon2.id;
}
