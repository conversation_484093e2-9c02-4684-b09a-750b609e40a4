import React from "react";
import { useAddresses } from "../hooks/useAddresses";
import AddAddressModal from "./Modal/AddAddressModal";
import ChangeAddressModal from "./Modal/ChangeAddressModal";
import RemoveWarningModal from "./organism/RemoveWarningModal";

interface AddressModalGroupProps {
  user: any;
  addressHook: ReturnType<typeof useAddresses>;
}

const AddressModalGroup = ({ user, addressHook }: AddressModalGroupProps) => {
  return (
    <>
      <AddAddressModal
        isOn={addressHook.isAddModalOn}
        onOff={addressHook.closeAddModal}
        onCreate={addressHook.fetch}
      />
      <ChangeAddressModal
        isOn={addressHook.editingAddressIndex !== -1}
        onOff={addressHook.closeEditModal}
        selectedAddress={
          addressHook.editingAddressIndex !== -1
            ? addressHook.addresses![addressHook.editingAddressIndex]
            : null
        }
        onUpdate={addressHook.fetch}
      />
      <RemoveWarningModal
        isOn={addressHook.deletingAddressIndex !== -1}
        onOff={addressHook.closeDeleteModal}
        title="Remove address"
        description="Are you sure you want to remove this address from the list? This action cannot be undone."
        iconSrc={require("../../public/trash_can_red_icon.png")}
        iconSize={52}
        onClickDelete={addressHook.deleteAddress}
        isLoading={addressHook.isDeleteLoading}
      />
    </>
  );
};

export default AddressModalGroup;
