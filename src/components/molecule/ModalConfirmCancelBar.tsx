import React from "react";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import SmallButton from "../atom/Buttons/base/SmallButton";

interface ModalConfirmCancelBarProps {
  confirmButtonTitle: string;
  onClickConfirmButton: () => void;
  cancelButtonTitle: string;
  onClickCancelButton: () => void;
  isConfirmDisabled: boolean;
  isLoading: boolean;
  styleCancelButton?: React.CSSProperties;
  styleConfirmButton?: React.CSSProperties;
  styleCancelButtonMobile?: React.CSSProperties;
  styleConfirmButtonMobile?: React.CSSProperties;
}

const ModalConfirmCancelBar = (props: ModalConfirmCancelBarProps) => {
  return (
    <>
      <div className="hidden md:block">
        <div className="bg-white pl-4 pr-8 py-4 border-t flex justify-between rounded-b-lg">
          <SmallButton
            title={props.cancelButtonTitle}
            onClick={props.onClickCancelButton}
            style={props.styleCancelButton}
          />
          <BackgroundSmallButton
            title={props.confirmButtonTitle}
            onClick={props.onClickConfirmButton}
            disabled={props.isConfirmDisabled}
            backgroundColor={
              props.confirmButtonTitle === "Delete Account" ? "DANGER" : "BLACK_PEARL"
            }
            isLoading={props.isLoading}
            style={props.styleConfirmButton}
          />
        </div>
      </div>
      <div className="block md:hidden">
        <div className="flex flex-col bg-white px-6 py-5 border-t justify-between rounded-b-lg gap-4 min-h-[141px]">
          <BackgroundSmallButton
            title={props.confirmButtonTitle}
            onClick={props.onClickConfirmButton}
            disabled={props.isConfirmDisabled}
            backgroundColor={
              props.confirmButtonTitle === "Delete Account" ? "DANGER" : "BLACK_PEARL"
            }
            isLoading={props.isLoading}
            style={{
              width: "100%",
              height: 48,
              ...props.styleConfirmButtonMobile,
            }}
          />
          <SmallButton
            title={props.cancelButtonTitle}
            onClick={props.onClickCancelButton}
            style={props.styleCancelButtonMobile}
          />
        </div>
      </div>
    </>
  );
};

export default ModalConfirmCancelBar;
