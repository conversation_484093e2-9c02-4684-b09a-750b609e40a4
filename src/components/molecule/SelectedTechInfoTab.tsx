import moment from "moment";
import Image from "next/image";
import { useContext } from "react";
import { calendarContext } from "../../contexts/CalendarContext";
import { DURATION_UNIT } from "../../values";
import BorderButton from "../atom/Buttons/BorderButton";
import GrayText14 from "../atom/Texts/GrayText14";
import Text14 from "../atom/Texts/base/Text14";
import { getSelectedCategoryTotalDuration } from "../../@redux/modules/categoryBooking/selector";
import { Color } from "../../types/Color";

interface SelectedTechInfoTabProps {
  onClickEdit: () => void;
}

const SelectedTechInfoTab = ({ onClickEdit }: SelectedTechInfoTabProps) => {
  const context = useContext(calendarContext)!;

  return (
    <div className="flex justify-between items-center bg-gray-50 px-6 py-5">
      <div className="flex items-center">
        <div style={{ width: 16, height: 16 }}>
          <Image src={require("../../../public/check.png")} />
        </div>
        {context.assignedTech?.profilePictureUri ? (
          <img
            src={context.assignedTech.profilePictureUri}
            style={{ width: 40, height: 40, borderRadius: 8, marginLeft: 12 }}
          />
        ) : (
          <div
            style={{
              width: 40,
              height: 40,
              borderRadius: 8,
              backgroundColor: Color.MERCURY,
              marginLeft: 12,
            }}
          />
        )}
        <div className="flex flex-col" style={{ marginLeft: 12 }}>
          <Text14 style={{ fontWeight: "500", marginBottom: 4 }}>
            {context.assignedTech?.name}
          </Text14>
          <GrayText14>
            {context.assignedTech?.name} will visit you on{" "}
            {context.assignedDateTime?.format("YYYY/MM/DD")} from{" "}
            {context.assignedDateTime?.format("HH:mm")} to{" "}
            {moment(context.assignedDateTime)
              .add(
                getSelectedCategoryTotalDuration(context.categorySessionHook) * DURATION_UNIT,
                "minutes"
              )
              .format("HH:mm")}
          </GrayText14>
        </div>
      </div>
      <BorderButton onClick={onClickEdit} title="Edit" />
    </div>
  );
};

export default SelectedTechInfoTab;
