import { Color } from "../../types/Color";
import Title24 from "../atom/Texts/base/Title24";

interface StepHeaderProps {
  title: string;
  description: string;
}

const StepHeader = (props: StepHeaderProps) => {
  return (
    <div className="flex flex-col">
      <Title24 style={{ color: Color.NORMAL_BLACK, marginBottom: 4 }}>{props.title}</Title24>
      <span
        style={{
          fontSize: 15,
          color: Color.BLACK_08,
        }}
      >
        {props.description}
      </span>
    </div>
  );
};

export default StepHeader;
