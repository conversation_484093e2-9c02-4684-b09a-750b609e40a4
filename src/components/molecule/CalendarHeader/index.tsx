import Image from "next/image";
import React from "react";
import Title18 from "../../atom/Texts/base/Title18";

interface CalendarHeaderProps {
  nowMonthText: string;
  onClickPrevDay: () => void;
  onClickToday: () => void;
  onClickNextDay: () => void;
}

const CalendarHeader = (props: CalendarHeaderProps) => {
  return (
    <div className="flex items-center justify-between px-6 py-4">
      <div className="flex flex-col ">
        <Title18>Schedule</Title18>
        <span
          className="text-[rgba(0,0,0,0.6)]"
          style={{
            fontSize: 13,
            marginTop: -2,
          }}
        >
          {props.nowMonthText}
        </span>
      </div>
      <div className="flex items-center">
        <button
          onClick={props.onClickPrevDay}
          className="w-8 h-8 bg-[rgba(0,0,0,0.05)] flex justify-center items-center rounded-full"
        >
          <div className="w-4 h-4 flex justify-center items-center">
            <Image src={require("../../../../public/calendar_left.png")} />
          </div>
        </button>
        <button
          onClick={props.onClickToday}
          className="w-[60px] h-8 rounded-md text-sm font-medium hover:brightness-95 text-[rgba(0,0,0,0.9)]"
        >
          Today
        </button>
        <button
          onClick={props.onClickNextDay}
          className="w-8 h-8 bg-[rgba(0,0,0,0.05)] flex justify-center items-center rounded-full"
        >
          <div className="w-4 h-4 flex justify-center items-center">
            <Image src={require("../../../../public/calendar_right.png")} />
          </div>
        </button>
      </div>
    </div>
  );
};

export default CalendarHeader;
