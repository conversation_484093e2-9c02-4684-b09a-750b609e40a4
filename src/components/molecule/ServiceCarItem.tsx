import React from "react";
import { Car } from "../../types/Car";
import Text14 from "../atom/Texts/base/Text14";
import GrayText14 from "../atom/Texts/GrayText14";
import { DURATION_UNIT } from "../../values";

interface ServiceCarItemProps {
  service: {
    name: string;
    duration: number;
    pictureUri: string;
    price: number;
    cars: Car[];
  };
  index: number;
  serviceLength: number;
}

const ServiceCarItem = ({ service, index, serviceLength }: ServiceCarItemProps) => {
  return (
    <div
      className="flex items-center justify-between"
      style={{
        marginBottom: index === serviceLength - 1 ? 24 : 12,
      }}
    >
      <div className="flex items-center">
        <img
          src={service.pictureUri}
          style={{
            width: 40,
            height: 40,
            borderRadius: 8,
            marginRight: 12,
          }}
        />
        <div className="flex flex-col mr-4">
          <Text14 style={{ marginBottom: 4 }}>{service.name}</Text14>
          <GrayText14>
            {service.cars.map((car) => `${car.year} ${car.make} ${car.model}`).join(", ")}
          </GrayText14>
        </div>
      </div>
      <Text14>{(service.duration * service.cars.length * DURATION_UNIT) / 60} h</Text14>
    </div>
  );
};

export default ServiceCarItem;
