import React from "react";
import { Color } from "../../../types/Color";
import Title32 from "../../atom/Texts/base/Title32"

interface AdminSubHeaderProps {
  title: string;
  onClickCreate: () => void;
}

const AdminSubHeader = (props: AdminSubHeaderProps) => {
  return (
    <div className="flex justify-between mb-10 items-center">
      <Title32>{props.title}</Title32>
      <button
        onClick={props.onClickCreate}
        style={{
          color: Color.ACCENT,
        }}
      >
        Create
      </button>
    </div>
  );
};

export default AdminSubHeader;
