import { Color } from "../../types/Color";

interface StepHeaderIndigoBlueProps {
  title: string;
  description: string;
  style?: React.CSSProperties;
}

const StepHeaderIndigoBlue = (props: StepHeaderIndigoBlueProps) => {
  return (
    <div className="flex flex-col justify-start">
      <span
        className="mb-2"
        style={{
          fontWeight: 600,
          fontSize: 24,
          lineHeight: "135%",
          color: Color.INDIGO_BLUE,
        }}
      >
        {props.title}
      </span>
      <span
        style={{
          fontWeight: 400,
          fontSize: 14,
          lineHeight: "135%",
          color: Color.INDIGO_BLUE_06,
        }}
      >
        {props.description}
      </span>
    </div>
  );
};

export default StepHeaderIndigoBlue;
