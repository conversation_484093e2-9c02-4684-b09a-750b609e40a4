import React, { useEffect } from "react";
import { hasCookie, setCookie } from "cookies-next";
import { Mixpanel } from "../../functions/mixpanel";

interface CookieConsentProps {}

const CookieConsent = (props: CookieConsentProps) => {
  const [showConsent, setShowConsent] = React.useState(false);

  useEffect(() => {
    setShowConsent(!hasCookie("localConsent"));
  }, []);

  function acceptCookie() {
    setShowConsent(false);
    setCookie("localConsent", "true", {});
    Mixpanel.track("Page View", {
      url: window.location.pathname,
    })
  };

  function declineCookie() {
    setShowConsent(false);
    setCookie("localConsent", "false", {});
  }

  if (!showConsent) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-[999]">
      <div className="fixed bottom-0 left-0 right-0 flex flex-col lg:flex-row items-start lg:items-center justify-between p-8 bg-gray-100">
        <div className="flex flex-col">
          <span className="text-xl font-medium text-dark mb-4">Your Privacy</span>
          <span className="text-dark text-base">
            We use cookies and similar technologies to track and analyze user behaviour using
            Mixpanel and Google Analytics. By clicking OK, you agree to this as outlined in our
            Cookie Policy.
          </span>
        </div>
        <div className="flex flex-row mt-5 lg:ml-8 lg:mt-0">
          <button
            className="py-2 px-4 rounded text-black border-black border min-w-[180px]"
            onClick={() => declineCookie()}
          >
            Don't Allow
          </button>
          <button
            className="py-2 px-4 rounded text-white bg-black min-w-[100px] ml-4"
            onClick={() => acceptCookie()}
          >
            OK
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
