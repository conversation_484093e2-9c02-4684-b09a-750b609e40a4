import IndigoBlueText16 from "../../atom/Texts/IndigoBlueText16";
import IndigoBlueTitle24 from "../../atom/Texts/IndigoBlueTitle24";

interface ProfileBlockHeaderProps {
  title: string;
  onClick?: () => void;
  buttonIcon?: React.ReactElement;
  buttonText?: string;
  style?: React.CSSProperties;
}

const ProfileBlockHeader = (props: ProfileBlockHeaderProps) => {
  return (
    <div
      className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-0 px-5 py-6 pb-0 mb-1 md:mb-0 md:px-6 md:py-8"
      style={{
        ...props.style,
      }}
    >
      <IndigoBlueTitle24>{props.title}</IndigoBlueTitle24>
      {props.onClick ? (
        <button
          className="flex flex-col justify-center items-start w-[162px] h-[48px] md:items-center"
          onClick={props.onClick}
        >
          <div className="flex flex-row justify-center items-center gap-3">
            {props.buttonIcon}
            <IndigoBlueText16 style={{ fontWeight: 600 }}>{props.buttonText}</IndigoBlueText16>
          </div>
        </button>
      ) : (
        <div style={{ height: 4 }} />
      )}
    </div>
  );
};

export default ProfileBlockHeader;
