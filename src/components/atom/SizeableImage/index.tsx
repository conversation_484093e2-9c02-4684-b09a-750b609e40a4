import Image from "next/image";
import { CSSProperties } from "react";

interface SizeableImageProps {
  src: any;
  size: number | string;
  style?: CSSProperties;
}

const SizeableImage = (props: SizeableImageProps) => {
  return (
    <div
      className="flex flex-col items-center justify-center"
      style={{ width: props.size, height: props.size, ...props.style }}
    >
      <Image src={props.src} />
    </div>
  );
};

export default SizeableImage;
