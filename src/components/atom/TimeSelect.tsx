import React from "react";
import BorderSelect from "./BorderSelect";

interface TimeSelectProps {
  value: any;
  onChange: (val: string) => void;
  values: string[];
  placeHolderName: string;
  style?: React.CSSProperties;
}

const TimeSelect = (props: TimeSelectProps) => {
  function renderOptions(values: string[], placeHolderName: string) {
    return [
      <option value="none" key="none">
        {placeHolderName}
      </option>,
      ...values.map((item) => (
        <option value={item} key={item}>
          {item}
        </option>
      )),
    ];
  }

  return (
    <BorderSelect
      value={props.value}
      onChange={props.onChange}
      style={props.style}
      options={renderOptions(props.values, props.placeHolderName)}
    />
  );
};

export default TimeSelect;
