import React from "react";
import { CustomTextProps } from "../../../../types/interfaces";
import { Color } from "../../../../types/Color";

interface ProfileFieldValueProps extends CustomTextProps {}

const ProfileFieldValue = (props: ProfileFieldValueProps) => {
  return (
    <span
      className="text-sm"
      style={{
        color: Color.BLACK_09,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default ProfileFieldValue;
