import React from "react";
import { Color } from "../../../../types/Color";
import { CustomTextProps } from "../../../../types/interfaces";

interface Title32Props extends CustomTextProps {}

const Title32 = (props: Title32Props) => {
  return (
    <h1
      style={{
        fontSize: 32,
        fontWeight: "bold",
        color: Color.NORMAL_BLACK,
        ...props.style
      }}
    >
      {props.children}
    </h1>
  );
};

export default Title32;
