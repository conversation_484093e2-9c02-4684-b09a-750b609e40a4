import React from "react";
import { Color } from "../../../../types/Color";
import { CustomTextProps } from "../../../../types/interfaces";

interface Text16Props extends CustomTextProps {}

const Text16 = (props: Text16Props) => {
  return (
    <span
      className="font-medium"
      style={{
        color: Color.NORMAL_BLACK,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default Text16;
