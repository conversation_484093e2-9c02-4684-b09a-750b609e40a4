import React from "react";
import { Color } from "../../../../types/Color";
import { CustomTextProps } from "../../../../types/interfaces";

interface Text12Props extends CustomTextProps {}

const Text12 = (props: Text12Props) => {
  return (
    <span className="text-xs" style={{
      color: Color.NORMAL_BLACK,
      ...props.style
    }}>
      {props.children}
    </span>
  );
};

export default Text12;
