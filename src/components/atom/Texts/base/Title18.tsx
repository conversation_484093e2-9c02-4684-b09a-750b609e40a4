import React from "react"
import { Color } from "../../../../types/Color"
import { CustomTextProps } from "../../../../types/interfaces"

interface Title18Props extends CustomTextProps {}

const Title18 = (props: Title18Props) => {
  return (
    <h6 className="text-lg font-semibold" style={{
      color: Color.NORMAL_BLACK,
      ...props.style
    }}>
      {props.children}
    </h6>
  )
}

export default Title18