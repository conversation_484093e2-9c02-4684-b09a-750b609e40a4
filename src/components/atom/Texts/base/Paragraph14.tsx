import React from "react";
import { Color } from "../../../../types/Color";
import { CustomTextProps } from "../../../../types/interfaces";

interface Paragraph14Props extends CustomTextProps {}

const Paragraph14 = (props: Paragraph14Props) => {
  return (
    <p className="text-sm" style={{
      color: Color.NORMAL_BLACK,
      ...props.style
    }}>
      {props.children}
    </p>
  );
};

export default Paragraph14;
