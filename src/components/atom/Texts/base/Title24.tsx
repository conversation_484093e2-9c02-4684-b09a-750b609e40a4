import React from "react"
import { Color } from "../../../../types/Color"
import { CustomTextProps } from "../../../../types/interfaces"

interface Title24Props extends CustomTextProps {}

const Title24 = (props: Title24Props) => {
  return (
    <h3 className="text-2xl font-semibold" style={{
      color: Color.NORMAL_BLACK,
      ...props.style
    }}>
      {props.children}
    </h3>
  )
}

export default Title24