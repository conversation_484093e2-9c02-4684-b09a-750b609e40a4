import React from "react";
import { Color } from "../../../../types/Color";
import { CustomTextProps } from "../../../../types/interfaces";

interface Title20Props extends CustomTextProps {}

const Title20 = (props: Title20Props) => {
  return (
    <h5
      className="text-xl font-semibold"
      style={{
        color: Color.NORMAL_BLACK,
        ...props.style,
      }}
    >
      {props.children}
    </h5>
  );
};

export default Title20;
