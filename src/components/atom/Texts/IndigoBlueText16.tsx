import { Color } from "../../../types/Color";
import { CustomTextProps } from "../../../types/interfaces";

interface IndigoBlueText16Props extends CustomTextProps {
  className?: string;
}

const IndigoBlueText16 = (props: IndigoBlueText16Props) => {
  return (
    <span
      className={`font-normal ${props.className}`}
      style={{
        color: Color.INDIGO_BLUE,
        fontSize: 16,
        lineHeight: 1,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default IndigoBlueText16;
