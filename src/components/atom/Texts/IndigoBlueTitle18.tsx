import { Color } from "../../../types/Color";
import { CustomTextProps } from "../../../types/interfaces";

interface IndigoBlueTitle18Props extends CustomTextProps {}

const IndigoBlueTitle18 = (props: IndigoBlueTitle18Props) => {
  return (
    <h3
      className="font-semibold"
      style={{
        fontSize: 18,
        color: Color.INDIGO_BLUE,
        ...props.style,
      }}
    >
      {props.children}
    </h3>
  );
};

export default IndigoBlueTitle18;
