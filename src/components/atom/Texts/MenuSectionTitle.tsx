import React from "react";
import { CustomTextProps } from "../../../types/interfaces";
import Text14 from "./base/Text14";

interface MenuSectionTitleProps extends CustomTextProps {
  style?: React.CSSProperties;
}

const MenuSectionTitle = (props: MenuSectionTitleProps) => {
  return <Text14 className="mb-3 font-semibold" style={props.style}>{props.children}</Text14>;
};

export default MenuSectionTitle;
