import { Color } from "../../../types/Color";
import { CustomTextProps } from "../../../types/interfaces";

interface IndigoBlueTitle24Props extends CustomTextProps {}

const IndigoBlueTitle24 = (props: IndigoBlueTitle24Props) => {
  return (
    <h3
      className="text-2xl font-semibold"
      style={{
        color: Color.INDIGO_BLUE,
        ...props.style,
      }}
    >
      {props.children}
    </h3>
  );
};

export default IndigoBlueTitle24;
