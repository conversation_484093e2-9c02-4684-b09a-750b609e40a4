import { Color } from "../../../types/Color";
import { CustomTextProps } from "../../../types/interfaces";

interface IndigoBlueText20Props extends CustomTextProps {}

const IndigoBlueText20 = (props: IndigoBlueText20Props) => {
  return (
    <span
      className="font-normal"
      style={{
        color: Color.INDIGO_BLUE,
        fontSize: 20,
        lineHeight: 1,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default IndigoBlueText20;
