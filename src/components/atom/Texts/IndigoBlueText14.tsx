import { Color } from "../../../types/Color";
import { CustomTextProps } from "../../../types/interfaces";

interface IndigoBlueText16Props extends CustomTextProps {}

const IndigoBlueText14 = (props: IndigoBlueText16Props) => {
  return (
    <span
      className="font-normal"
      style={{
        color: Color.INDIGO_BLUE,
        fontSize: 14,
        lineHeight: 1,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default IndigoBlueText14;
