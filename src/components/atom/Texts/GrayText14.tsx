import React from "react";
import { Color } from "../../../types/Color";
import { CustomTextProps } from "../../../types/interfaces";
import Text14 from "./base/Text14";

interface GrayText14Props extends CustomTextProps {}

const GrayText14 = (props: GrayText14Props) => {
  return (
    <Text14
      style={{
        color: Color.GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </Text14>
  );
};

export default GrayText14;
