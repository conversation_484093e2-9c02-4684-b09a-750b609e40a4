import SizeableImage from "./SizeableImage";
import Text16 from "./Texts/base/Text16";

interface TitleWithIconProps {
  title: string;
  iconSrc: any;
  style?: React.CSSProperties;
}

const TitleWithIcon = (props: TitleWithIconProps) => {
  return (
    <div className="flex items-center" style={props.style}>
      <SizeableImage size={20} src={props.iconSrc} style={{ marginRight: 12 }} />
      <Text16 style={{ fontWeight: "600" }}>{props.title}</Text16>
    </div>
  );
};

export default TitleWithIcon;
