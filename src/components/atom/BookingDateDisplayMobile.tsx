import moment from "moment";
import { Color } from "../../types/Color";
import Text12 from "./Texts/base/Text12";
import Text14 from "./Texts/base/Text14";

interface BookingDateDisplayMobileProps {
  startDateTime: string;
  endDateTime: string;
}

const BookingDateDisplayMobile = (props: BookingDateDisplayMobileProps) => {
  return (
    <div className="flex flex-col" style={{ backgroundColor: Color.LIGHT_BACKGROUND }}>
      <DateTime title="Start" dateTime={props.startDateTime} />
      <DateTime title="End" dateTime={props.endDateTime} />
    </div>
  );
};

interface DateTimeProps {
  title: string;
  dateTime: string;
  style?: React.CSSProperties;
}

const DateTime = (props: DateTimeProps) => {
  return (
    <div
      className="flex py-3 px-4 items-center border"
      style={{
        ...props.style,
      }}
    >
      <Text12 style={{ marginRight: 12, color: Color.BLACK_04, width: 28 }}>{props.title}</Text12>
      <Text14 style={{ fontWeight: 500 }}>
        {moment(props.dateTime).format("YYYY-MM-DD [at] h:mm a")}
      </Text14>
    </div>
  );
};

export default BookingDateDisplayMobile;
