import Image from "next/image";
import Text14 from "./Texts/base/Text14";

interface SelectDateTechButtonProps {
  onClick: () => void;
  style?: React.CSSProperties;
}

const SelectDateTechButton = (props: SelectDateTechButtonProps) => {
  return (
    <button
      className="flex flex-1 justify-center self-stretch p-3 border border-black"
      onClick={props.onClick}
      style={props.style}
    >
      <Image src={require("../../../public/plus.png")} width={20} height={20} />
      <Text14 style={{ fontWeight: 600, marginLeft: 12 }}>Select Date and Time</Text14>
    </button>
  );
};

export default SelectDateTechButton;
