import React from "react";

interface PaperProps {
  children: React.ReactNode;
  borders?: string;
  background?: string;
  style?: React.CSSProperties;
}

const Paper = (props: PaperProps) => {
  return (
    <div
      className={`shadow-[0_5px_15px_rgba(0,0,0,0.03)] ${props.background || "bg-white"} ${props.borders || "rounded-xl"
        } border flex flex-col`}
      style={{
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

export default Paper;
