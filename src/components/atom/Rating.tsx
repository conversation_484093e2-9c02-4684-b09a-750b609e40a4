import SizeableImage from "./SizeableImage";
import Text14 from "./Texts/base/Text14";

interface RatingProps {
  rating: number;
  includeText?: boolean;
}

const Rating = ({ rating, includeText = true }: RatingProps) => {
  return (
    <div className="flex items-center">
      <Star isHighlighted={rating > 0} />
      <Star isHighlighted={rating > 1} />
      <Star isHighlighted={rating > 2} />
      <Star isHighlighted={rating > 3} />
      <Star isHighlighted={rating > 4} />
      {includeText && <Text14 style={{ marginLeft: 8 }}>{rating}</Text14>}
    </div>
  );
};

interface StarProps {
  isHighlighted: boolean;
}

const Star = (props: StarProps) => {
  if (props.isHighlighted) {
    return (
      <SizeableImage size={20} src={require("../../../public/yellow_star.png")} />
    )
  }

  return (
    <SizeableImage size={20} src={require("../../../public/gray_star.png")} />
  )
}

export default Rating;
