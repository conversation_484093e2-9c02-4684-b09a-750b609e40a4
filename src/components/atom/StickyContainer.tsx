interface StickyContainerProps {
  children: React.ReactNode;
  isConditionalSticky?: boolean;
}

const StickyContainer = ({ children, isConditionalSticky = false }: StickyContainerProps) => {
  return (
    <div
      className={`flex flex-col md:mt-5 mt-3 ${
        isConditionalSticky ? "xl:sticky static" : "sticky"
      }`}
      style={{ top: 32 }}
    >
      {children}
    </div>
  );
};

export default StickyContainer;
