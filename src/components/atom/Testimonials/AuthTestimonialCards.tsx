import TestimonialCard from "./TestimonialCard";
import data from "./testimonials.json";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import useReducedMotion from "../../../hooks/useReducedMotion";
import { Color } from "../../../types/Color";

const AuthTestimonialCards = (props: { style?: React.CSSProperties }) => {
  const reducedMotion = useReducedMotion(false);
  const testimonials = data.testimonials.concat(data.testimonials).concat(data.testimonials);
  const rootRef = useRef<HTMLDivElement | null>(null);
  const refs = useRef<(HTMLDivElement | null)[]>([]);
  const x = useRef(0);

  function scrollLeft() {
    if (x.current - 1 === -1) {
      x.current = testimonials.length - 1;
    } else {
      x.current = x.current - 1;
    }

    horizontalScrollToXItem();
  }

  function scrollRight() {
    if (x.current === testimonials.length - 1) {
      x.current = 0;
    } else {
      x.current = x.current + 1;
    }

    horizontalScrollToXItem();
  }

  function horizontalScrollToXItem() {
    const standardRect = refs.current[0]?.getBoundingClientRect();
    const rect = refs.current[x.current]?.getBoundingClientRect();

    if (standardRect && rect) {
      rootRef.current?.scrollTo({
        left: rect.left - standardRect.left,
        behavior: !reducedMotion ? "smooth" : "auto",
      });
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      scrollRight();
    }, 5000);

    return () => {
      clearInterval(timer);
    };
  }, [x]);

  return (
    <div
      className="flex flex-col justify-between h-full gap-1 text-white"
      style={{ width: 560, background: Color.BLACK_PEARL, ...props.style }}
    >
      <div>
        <div className="flex basis-auto ml-6 mt-6 place-self-start" style={{ width: 120 }}>
          <Image src={require("../../../../public/white_logo.png")} alt="Wheel Easy Logo" />
        </div>
        <p className="white ml-6 mt-4">Have any questions? Call us at <a href={`tel:${+15198704546}`} className="underline">(*************</a></p>
      </div>
      <div className="flex basis-10/12 flex-col justify-center">
        <div className="flex flex-row justify-between mx-6">
          <div>
            <h1 className="font-light">Client Testimonials</h1>
            <h1 className="text-3xl font-medium -mb-5">Our Happy Customers</h1>
          </div>
        </div>
        <div className="overflow-hidden w-full" ref={rootRef}>
          <div className={`flex flex-row`} style={{ marginTop: 52 }}>
            {testimonials.map((t, index) => {
              return (
                <div
                  key={index}
                  ref={(ref) => {
                    refs.current.push(ref);
                  }}
                  className="pl-5 min-w-[calc(100vw-20px)] md:min-w-[540px]"
                >
                  <TestimonialCard
                    name={t.name}
                    testimonial={t.testimonial}
                    date={t.date}
                    time={t.time}
                    stars={5}
                    image={t.image}
                  />
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex flex-row ml-6 gap-3">
          <svg
            width="52"
            height="52"
            viewBox="0 0 52 52"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            onClick={scrollLeft}
            className="cursor-pointer"
          >
            <path
              d="M23.165 18.9183L16.0834 26L23.165 33.0817"
              stroke="white"
              strokeWidth="2.5"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M35.916 26H16.281"
              stroke="white"
              strokeWidth="2.5"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <rect
              x="0.25"
              y="0.25"
              width="51.5"
              height="51.5"
              rx="25.75"
              stroke="white"
              strokeWidth="0.5"
            />
          </svg>
          <svg
            width="52"
            height="52"
            viewBox="0 0 52 52"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            onClick={scrollRight}
            className="cursor-pointer"
          >
            <path
              d="M28.835 18.9183L35.9166 26L28.835 33.0817"
              stroke="white"
              strokeWidth="2.5"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16.084 26H35.719"
              stroke="white"
              strokeWidth="2.5"
              strokeMiterlimit="10"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <rect
              x="0.25"
              y="0.25"
              width="51.5"
              height="51.5"
              rx="25.75"
              stroke="white"
              strokeWidth="0.5"
            />
          </svg>
        </div>
        <div style={{ height: 40 }} />
        <div className="flex xl:hidden mx-6">
          <div className="flex flex-row place-self-start">
            <p className="text-[#FFFFFF] text-sm">&copy;WheelEasy {new Date().getFullYear()}</p>
          </div>
        </div>
        <div style={{ height: 64 }} />
      </div>
    </div>
  );
};

export default AuthTestimonialCards;
