import Image from "next/image";
import ProfilePlaceholder from "../ProfilePlaceholder";

interface TestimonialProps {
  name: string;
  testimonial: string;
  date: string;
  time: string;
  stars: number;
  image?: string;
}

const TestimonialCard = (props: TestimonialProps) => {
  return (
    <div className="border-[0.5px] border-[#9DB4B7] p-4 mb-6 text-white rounded-[40px] pb-6 pl-5">
      <div className="flex flex-row gap-2 my-2">
        {props.stars &&
          Array(props.stars)
            .fill(0)
            .map((_, index) => {
              return (
                <svg
                  key={index}
                  width="16"
                  height="17"
                  viewBox="0 0 16 17"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.4297 6.00001L8.95971 1.16001C8.66971 0.21001 7.32971 0.21001 7.04971 1.16001L5.56971 6.00001H1.11971C0.149715 6.00001 -0.250285 7.25001 0.539715 7.81001L4.17971 10.41L2.74971 15.02C2.45971 15.95 3.53972 16.7 4.30971 16.11L7.99971 13.31L11.6897 16.12C12.4597 16.71 13.5397 15.96 13.2497 15.03L11.8197 10.42L15.4597 7.82001C16.2497 7.25001 15.8497 6.01001 14.8797 6.01001H10.4297V6.00001Z"
                    fill="#E3B100"
                  />
                </svg>
              );
            })}
      </div>
      <p className="text-sm my-4">
        {'"'}
        {props.testimonial}
        {'"'}
      </p>
      <div className="flex flex-row justify-start items-center">
        {props.image ? (
          <Image
            width="40"
            height="40"
            className="rounded-full"
            src={require(`../../../../public/review_${props.name.toLowerCase().replace(" ", "_")}.${
              props.image
            }`)}
            alt=""
          />
        ) : (
          <ProfilePlaceholder size={40} style={{ borderRadius: 24 }} />
        )}
        <div className="ml-4">
          <p className="font-medium">{props.name}</p>
          <p style={{ fontSize: 10 }}>{props.date}</p>
        </div>
      </div>
    </div>
  );
};

export default TestimonialCard;
