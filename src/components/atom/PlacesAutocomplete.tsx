import { useEffect } from "react";
import useOnClickOutside from "react-cool-onclickoutside";
import usePlacesAutocomplete, { getGeocode } from "use-places-autocomplete";

interface PlacesAutocompleteProps {
  isOn: boolean;
  initialValue: string;
  populateForms: (
    addressLine1: string,
    addressLine2: string,
    city: string,
    province: string,
    postal: string,
    country: string
  ) => void;
  style?: React.CSSProperties;
}

const PlacesAutocomplete = (props: PlacesAutocompleteProps) => {
  useEffect(() => {
    if (props.initialValue) {
      setValue(props.initialValue, false);
    }
  }, [props.isOn]);

  const {
    ready,
    value,
    suggestions: { status, data },
    setValue,
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      /* Define search scope here */
    },
    debounce: 300,
  });

  const ref = useOnClickOutside(() => {
    clearSuggestions();
  });

  const handleInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  };

  const handleSelect =
    ({ description }: { description: string }) =>
    () => {
      clearSuggestions();

      getGeocode({ address: description })
        .then((results) => {
          // console.log(results[0]);
          const addresses = results[0].formatted_address.split(",");
          console.log(addresses);
          console.log(addresses[1]);
          console.log(addresses[1].trim());
          const addressLine1 = addresses[0].trim();
          const addressLine2 = "";
          const city = addresses[1].trim();
          const provinceAndPostal = addresses[2].trim();
          const province = provinceAndPostal.split(" ")[0];
          const postal =
            provinceAndPostal.split(" ")[1] +
            (provinceAndPostal.split(" ").length > 2 ? " " + provinceAndPostal.split(" ")[2] : "");
          const country = addresses[3].trim();

          setValue(addressLine1, false);
          props.populateForms(addressLine1, addressLine2, city, province, postal, country);
        })
        .catch((error) => {
          console.log("😱 Error: ", error);
        });
    };

  const renderSuggestions = () =>
    data.map((suggestion) => {
      const {
        place_id,
        structured_formatting: { main_text, secondary_text },
      } = suggestion;

      const completeText = main_text + " " + secondary_text;
      const matchingLastIndex = completeText.toLowerCase().indexOf(value.toLowerCase());

      let matchingText = "";
      let remainingText = completeText;

      if (matchingLastIndex === 0) {
        matchingText = completeText.substring(0, value.length);
        remainingText = completeText.substring(value.length);
      }

      return (
        <li
          className="text-gray-900 sm:text-sm px-2.5 py-2 cursor-pointer hover:bg-gray-100"
          key={place_id}
          onClick={handleSelect(suggestion)}
        >
          <span className="font-bold">
            {matchingText}
            <span className="font-normal">{remainingText}</span>
          </span>
        </li>
      );
    });

  return (
    <div ref={ref} style={props.style}>
      <input
        className="bg-white border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-orange-400 focus:border-orange-400 block w-full p-2.5 outline-none transition-all"
        value={value}
        onChange={handleInput}
        disabled={!ready}
        placeholder="Enter your address"
      />
      {/* We can use the "status" to decide whether we should display the dropdown or not */}
      {status === "OK" && (
        <ul className="bg-white border border-gray-300 rounded-lg block w-full outline-none transition-all">
          {renderSuggestions()}
        </ul>
      )}
    </div>
  );
};

export default PlacesAutocomplete;
