import React from "react"

interface BorderSelectProps {
  value: any;
  onChange: (val: string) => void;
  options: React.ReactNode;
  style?: React.CSSProperties;
}

const BorderSelect = (props: BorderSelectProps) => {
  return (
    <select
      value={props.value}
      onChange={event => props.onChange(event.target.value)}
      className="border border-[rgba(0,0,0,0.15)] px-2 py-2 text-sm outline-none"
      style={props.style}
    >
      {props.options}
      {/* {props.options.map((option, index) => (
        <option key={index} value={option.value}>
          {option.label}
        </option>
      ))} */}
    </select>
  )
}

export default BorderSelect