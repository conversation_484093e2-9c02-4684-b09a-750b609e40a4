import moment from "moment";
import { Color } from "../../types/Color";
import Text12 from "./Texts/base/Text12";
import Text14 from "./Texts/base/Text14";

interface BookingDateDisplayProps {
  startDateTime: string;
  endDateTime: string;
}

const BookingDateDisplay = (props: BookingDateDisplayProps) => {
  return (
    <div className="flex border rounded-md" style={{ backgroundColor: Color.LIGHT_BACKGROUND }}>
      <DateTime title="Start" dateTime={props.startDateTime} showDivider={true} />
      <DateTime title="End" dateTime={props.endDateTime} showDivider={false} />
    </div>
  );
};

interface DateTimeProps {
  title: string;
  dateTime: string;
  showDivider: boolean;
}

const DateTime = (props: DateTimeProps) => {
  return (
    <div
      className="flex py-3 px-4 items-center"
      style={{ borderRightWidth: props.showDivider ? 1 : 0, borderColor: Color.BORDER_GRAY }}
    >
      <Text12 style={{ marginRight: 12 }}>{props.title}</Text12>
      <Text14>{moment(props.dateTime).format("YYYY-MM-DD [at] h:mm a")}</Text14>
    </div>
  );
};

export default BookingDateDisplay;
