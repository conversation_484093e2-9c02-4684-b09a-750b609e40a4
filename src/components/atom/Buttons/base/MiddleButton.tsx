import React from "react"
import { Rings } from "react-loader-spinner"
import { Color } from "../../../../types/Color"
import { CustomButtonProps } from "../../../../types/interfaces"

interface MiddleButtonProps extends CustomButtonProps {
  
}

const MiddleButton = (props: MiddleButtonProps) => {
  return (
    <button
      onClick={props.onClick}
      className="px-5 transition-all hover:brightness-90 relative"
      style={{
        paddingTop: 10,
        paddingBottom: 10,
        ...props.style
      }}
      disabled={props.disabled || props.isLoading}
    >
      <span
        style={{
          visibility: props.isLoading ? "hidden" : "visible",
        }}
      >
        {props.title}
      </span>
      <div
        className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
        style={{
          visibility: props.isLoading ? "visible" : "hidden",
        }}
      >
        <Rings width="32" height="32" color={Color.DISABLED_GRAY} />
      </div>
    </button>
  )
}

export default MiddleButton