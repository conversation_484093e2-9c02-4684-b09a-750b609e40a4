import React from "react"
import { Color } from "../../../../types/Color"
import { CustomButtonProps } from "../../../../types/interfaces"

interface TextButtonProps extends CustomButtonProps {}

const TextButton = (props: TextButtonProps) => {
  return (
    <button
      onClick={props.onClick}
      className="text-sm font-medium"
      disabled={props.disabled}
      style={{
        color: props.disabled ? Color.DISABLED_GRAY : Color.NORMAL_BLACK,
        ...props.style
      }}
    >
      {props.title}
    </button>
  )
}

export default TextButton