import { CustomButtonProps } from "../../../../types/interfaces";

interface SmallButtonProps extends CustomButtonProps {}

const SmallButton = (props: SmallButtonProps) => {
  return (
    <button
      onClick={props.onClick}
      className="text-sm px-4 py-2 transition-all relative duration-300 font-medium"
      style={props.style}
      disabled={props.disabled || props.isLoading}
    >
      <span
        style={{
          visibility: props.isLoading ? "hidden" : "visible",
        }}
      >
        {props.title}
      </span>
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
        <div
          style={{
            display: props.isLoading ? "block" : "none",
          }}
          className="sbl-circ-path"
        />
      </div>
    </button>
  );
};

export default SmallButton;
