import React from "react";
import { CustomButtonProps } from "../../../../types/interfaces";
import SmallButton from "../base/SmallButton";
import { Color } from "../../../../types/Color";

interface BorderSmallButton extends CustomButtonProps {
  isRed?: boolean;
}

const BorderSmallButton = (props: BorderSmallButton) => {
  return (
    <SmallButton
      title={props.title}
      onClick={props.onClick}
      disabled={props.disabled}
      isLoading={props.isLoading}
      style={{
        width: 94,
        color: !props.isRed ? Color.BLACK_06 : "red",
        border: `1px solid ${!props.isRed ? Color.BLACK_03 : "red"}`,
        borderRadius: "8px",
        ...props.style,
      }}
    />
  );
};

export default BorderSmallButton;
