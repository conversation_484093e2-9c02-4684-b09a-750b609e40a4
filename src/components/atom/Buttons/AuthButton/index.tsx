import { Color } from "../../../../types/Color";

interface AuthButtonProps {
  title: string;
  onClick: () => void;
  isFilled: boolean;
  disabled?: boolean;
  isLoading?: boolean;
}

const AuthButton = (props: AuthButtonProps) => {
  return props.disabled ? (
    <button
      disabled={props.disabled}
      style={{
        paddingTop: props.isLoading ? 15 : 12,
        paddingBottom: props.isLoading ? 15 : 12,
      }}
      className="text-white bg-[rgba(0,0,0,0.4)] font-medium text-base self-stretch flex justify-center items-center rounded-lg drop-shadow-sm"
    >
      {props.isLoading ? <div className="sbl-circ-path" /> : props.title}
    </button>
  ) : props.isFilled ? (
    <button
      className="text-white border py-3 hover:bg-[#444444] font-medium text-base self-stretch rounded-lg drop-shadow-sm transition-colors duration-200"
      onClick={props.onClick}
      style={{
        background: Color.BLACK_PEARL,
      }}
    >
      {props.title}
    </button>
  ) : (
    <button
      className="border border-[rgba(208, 213, 221, 0)] hover:bg-[rgba(0,0,0,0.1)] py-3 text-base font-medium self-stretch rounded-lg drop-shadow-sm"
      onClick={props.onClick}
    >
      {props.title}
    </button>
  );
};

export default AuthButton;
