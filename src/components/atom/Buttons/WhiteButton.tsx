import { Color } from "../../../types/Color";

interface WhiteButtonProps {
  title: string;
  onClick: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
}

const WhiteButton = (props: WhiteButtonProps) => {
  return (
    <button
      className="flex items-center rounded-lg bg-white transition-all relative duration-300"
      style={{
        border: "1px solid #D0D5DD",
        boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
        ...props.style,
      }}
      onClick={props.onClick}
      disabled={props.disabled || props.isLoading}
    >
      <WhiteButtonTitle isLoading={props.isLoading} style={props.titleStyle}>
        {props.title}
      </WhiteButtonTitle>
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
        <div
          style={{
            display: props.isLoading ? "block" : "none",
          }}
          className="sbl-circ-path"
        />
      </div>
    </button>
  );
};

const WhiteButtonTitle = (props: {
  isLoading?: boolean;
  children: React.ReactNode;
  style?: React.CSSProperties;
}) => {
  return (
    <span
      className="flex items-center"
      style={{
        color: Color.INDIGO_BLUE,
        fontWeight: 600,
        fontSize: 14,
        lineHeight: "143%",
        letterSpacing: "0.02em",
        visibility: props.isLoading ? "hidden" : "visible",
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default WhiteButton;
