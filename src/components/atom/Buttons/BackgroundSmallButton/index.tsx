import React from "react";
import { Color } from "../../../../types/Color";
import { CustomButtonProps } from "../../../../types/interfaces";
import SmallButton from "../base/SmallButton";

interface BackgroundSmallButtonProps extends CustomButtonProps {
  backgroundColor: "ACCENT" | "DARK_BLUE" | "DANGER" | "REMOVEABLE" | "BLACK_PEARL";
}

const BackgroundSmallButton = (props: BackgroundSmallButtonProps) => {
  function getBackgroundColor() {
    if (props.disabled || props.isLoading) {
      return Color.DISABLED_GRAY;
    } else if (props.backgroundColor === "ACCENT") {
      return Color.ACCENT;
    } else if (props.backgroundColor === "DARK_BLUE") {
      return Color.DARK_BLUE;
    } else if (props.backgroundColor === "DANGER") {
      return Color.CORAL_MID_RED;
    } else if (props.backgroundColor === "REMOVEABLE") {
      return Color.CORAL_RED_005;
    } else if (props.backgroundColor === "BLACK_PEARL") {
      return Color.BLACK_PEARL;
    }
  }

  return (
    <SmallButton
      title={props.title}
      onClick={props.onClick}
      style={{
        backgroundColor: getBackgroundColor(),
        color: props.backgroundColor === "REMOVEABLE" ? Color.CORAL_MID_RED : Color.WHITE,
        borderRadius: 8,
        ...props.style,
      }}
      disabled={props.disabled}
      isLoading={props.isLoading}
    />
  );
};

export default BackgroundSmallButton;
