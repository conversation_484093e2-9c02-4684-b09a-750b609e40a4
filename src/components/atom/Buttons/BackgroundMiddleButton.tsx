import React from "react";
import { Color } from "../../../types/Color";
import { CustomButtonProps } from "../../../types/interfaces";
import MiddleButton from "./base/MiddleButton";

interface BackgroundMiddleButtonProps extends CustomButtonProps {
  backgroundColor: "ACCENT" | "DARK_BLUE";
}

const BackgroundMiddleButton = (props: BackgroundMiddleButtonProps) => {
  function getBackgroundColor() {
    if (props.disabled || props.isLoading) {
      return Color.DISABLED_GRAY;
    } else if (props.backgroundColor === "ACCENT") {
      return Color.ACCENT;
    } else if (props.backgroundColor === "DARK_BLUE") {
      return Color.DARK_BLUE;
    }
  }

  return (
    <MiddleButton
      title={props.title}
      onClick={props.onClick}
      style={{
        backgroundColor: getBackgroundColor(),
        color: Color.WHITE,
        borderRadius: 4,
        ...props.style,
      }}
      disabled={props.disabled}
      isLoading={props.isLoading}
    />
  );
};

export default BackgroundMiddleButton;
