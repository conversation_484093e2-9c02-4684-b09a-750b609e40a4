import { CustomButtonProps } from "../../../../types/interfaces";
import SmallButton from "../base/SmallButton";

interface BorderButton extends CustomButtonProps {
  style?: React.CSSProperties
}

const BorderButton = (props: BorderButton) => {
  return (
    <SmallButton 
      title={props.title}
      onClick={props.onClick}
      disabled={props.disabled}
      isLoading={props.isLoading}
      style={{
        border: `1px solid rgba(0,0,0,0.2)`,
        borderRadius: "8px",
        height: 40,
        ...props.style
      }}
    />
  )
}

export default BorderButton;