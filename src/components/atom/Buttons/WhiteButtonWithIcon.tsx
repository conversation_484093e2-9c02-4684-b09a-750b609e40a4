import { Color } from "../../../types/Color";
import SizeableImage from "../SizeableImage";

interface WhiteButtonWithIconProps {
  title: string;
  iconSrc: any;
  iconSize: number;
  onClick: () => void;
  style?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
}

const WhiteButtonWithIcon = (props: WhiteButtonWithIconProps) => {
  return (
    <button
      className="flex items-center rounded-lg bg-white py-[13.5px] px-5"
      style={{
        border: "1px solid #D0D5DD",
        boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
        ...props.style,
      }}
      onClick={props.onClick}
    >
      <SizeableImage size={props.iconSize} src={props.iconSrc} style={{ marginRight: 8 }} />
      <WhiteButtonTitle style={props.titleStyle}>{props.title}</WhiteButtonTitle>
    </button>
  );
};

const WhiteButtonTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-center"
      style={{
        color: Color.INDIGO_BLUE,
        fontWeight: 600,
        fontSize: 14,
        lineHeight: "150%",
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

export default WhiteButtonWithIcon;
