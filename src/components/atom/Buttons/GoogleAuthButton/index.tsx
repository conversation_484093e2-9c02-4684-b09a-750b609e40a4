import Image from "next/image";
import { Rings } from "react-loader-spinner";

interface GoogleAuthButtonProps {
  title: string;
  onClick: () => void;
  isFilled: boolean;
  disabled?: boolean;
  isLoading?: boolean;
}

const GoogleAuthButton = (props: GoogleAuthButtonProps) => {
  return props.disabled ? (
    <button
      disabled={props.disabled}
      style={{
        paddingTop: props.isLoading ? 3 : 12,
        paddingBottom: props.isLoading ? 3 : 12,
      }}
      className="border border-[rgba(208, 213, 221, 0)] font-semibold self-stretch flex justify-center items-center drop-shadow-sm rounded-lg"
    >
      {props.isLoading ? (
        <Rings width="40" height="40" color="rgba(0,0,0,0.4)" />
      ) : (
        <div className="flex flex-row items-center justify-center">
          <Image width={24} height={24} src={require("../../../../../public/google_icon.svg")} />
          <span className="font-medium" style={{ height: 24, marginLeft: 8 }}>
            {props.title}
          </span>
        </div>
      )}
    </button>
  ) : props.isFilled ? (
    <button
      className="text-white bg-[#ffffff] py-4 hover:bg-[#000000] font-semibold self-stretch drop-shadow-sm"
      onClick={props.onClick}
    >
      <div className="flex flex-row items-center justify-center">
        <Image width={24} height={24} src={require("../../../../../public/google_icon.svg")} />
        <span className="font-medium" style={{ height: 24, marginLeft: 8 }}>
          {props.title}
        </span>
      </div>
    </button>
  ) : (
    <button
      className="border border-[rgba(208, 213, 221, 0)] hover:bg-[rgba(0,0,0,0.1)] py-3 font-semibold self-stretch rounded-lg drop-shadow-sm"
      onClick={props.onClick}
    >
      <div className="flex flex-row items-center justify-center">
        <Image width={24} height={24} src={require("../../../../../public/google_icon.svg")} />
        <span className="font-medium" style={{ height: 24, marginLeft: 8 }}>
          {props.title}
        </span>
      </div>
    </button>
  );
};

export default GoogleAuthButton;
