import React from "react";
import { Color } from "../../../types/Color";
import { CustomButtonProps } from "../../../types/interfaces";
import { PencilIcon, PeopleIcon } from "../Icons";

interface MenuButtonProps extends CustomButtonProps {
  isSelected: boolean;
  icon?: React.ReactElement;
}

const Icon = () => {
  return <PencilIcon />;
};

const MenuButton = (props: MenuButtonProps) => {
  const MenuIcon = () => {
    return props.icon ? (
      React.cloneElement(props.icon, { colour: props.isSelected ? Color.ACCENT : "#000000" }, null)
    ) : (
      <></>
    );
  };

  return (
    <>
      <button
        disabled={props.disabled}
        onClick={props.onClick}
        className={`w-full text-sm text-left h-8 py-6 flex flex-col self-stretch justify-center rounded-lg px-3 ${
          props.isSelected
            ? `hover:font-semibold text-[${Color.ACCENT}] bg-[#F89A000D]`
            : "text-[#8A8B96] hover:text-black "
        }`}
        style={{
          ...props.style,
        }}
      >
        <div className="flex flex-row gap-3 items-center">
          <MenuIcon />
          <p>{props.title}</p>
        </div>
      </button>
    </>
  );
};

export default MenuButton;
