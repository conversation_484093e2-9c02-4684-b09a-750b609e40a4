import Image from "next/image";
import { Color } from "../../../types/Color";

const BookAppointmentButtonMobile = (props: {
  onClick: () => void;
  style?: React.CSSProperties;
  disabled?: boolean;
  isLoading?: boolean;
}) => {
  return (
    <button
      onClick={props.onClick}
      className="px-4 py-3 transition-all relative duration-300 font-medium items-stretch h-[42px]"
      style={{
        backgroundColor: props.disabled || props.isLoading ? Color.DISABLED_GRAY : Color.ACCENT,
        color: Color.WHITE,
        borderRadius: 4,
        fontSize: 12,
        minWidth: 50,
        ...props.style,
      }}
      disabled={props.disabled || props.isLoading}
    >
      <div
        className="flex flex-row items-start"
        style={{
          visibility: props.isLoading ? "hidden" : "visible",
        }}
      >
        <Image src={require("../../../../public/calendar_plus.png")} />
        <span className="hidden ml-2 xs:flex">Book an Appointment</span>
      </div>
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
        <div
          style={{
            display: props.isLoading ? "block" : "none",
          }}
          className="sbl-circ-path"
        />
      </div>
    </button>
  );
};

export default BookAppointmentButtonMobile;
