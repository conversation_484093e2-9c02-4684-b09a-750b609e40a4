import { Color } from "../../../types/Color";
import SizeableImage from "../SizeableImage";

interface NoBorderButtonWithIconProps {
  title: string;
  iconSrc: any;
  iconSize: number;
  onClick: () => void;
  style?: React.CSSProperties;
  isTextRed?: boolean;
}

const NoBorderButtonWithIcon = (props: NoBorderButtonWithIconProps) => {
  return (
    <button className="flex items-center" style={props.style} onClick={props.onClick}>
      <SizeableImage size={props.iconSize} src={props.iconSrc} style={{ marginRight: 8 }} />
      <ButtonTitle isTextRed={props.isTextRed}>{props.title}</ButtonTitle>
    </button>
  );
};

const ButtonTitle = (props: { children: React.ReactNode; isTextRed?: boolean }) => {
  return (
    <span
      className="flex items-center"
      style={{
        color: props.isTextRed ? Color.ALIZARIN_CRIMSON : Color.INDIGO_BLUE,
        fontWeight: 600,
        fontSize: 14,
        lineHeight: 1,
      }}
    >
      {props.children}
    </span>
  );
};

export default NoBorderButtonWithIcon;
