import { Color } from "../../../types/Color";
import { CustomButtonProps } from "../../../types/interfaces";
import TextButton from "./base/TextButton";

interface AccentTextButtonProps extends CustomButtonProps {}

const AccentTextButton = (props: AccentTextButtonProps) => {
  return (
    <TextButton
      title={props.title}
      onClick={props.onClick}
      disabled={props.disabled}
      style={{
        color: props.disabled ? Color.DISABLED_GRAY : Color.ACCENT,
        ...props.style,
      }}
    />
  );
};

export default AccentTextButton;
