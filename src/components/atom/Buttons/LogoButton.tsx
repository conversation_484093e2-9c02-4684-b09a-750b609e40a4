import Image from "next/image";

const LogoButton = (props: { style?: React.CSSProperties; onClick: () => void }) => {
  return (
    <button
      className="flex items-stretch"
      style={{
        width: 70,
        height: 28,
        ...props.style,
      }}
      onClick={props.onClick}
    >
      <Image src={require("../../../../public/white_logo.png")} />
    </button>
  );
};

export default LogoButton;
