import SizeableImage from "../SizeableImage";

interface CloseButtonProps {
  onClick: () => void;
  style?: React.CSSProperties;
}

const CloseButton = (props: CloseButtonProps) => {
  return (
    <button className="flex items-center justify-center rounded-full w-10 h-10"
      style={{
        border: "1px solid #D0D5DD",
        background: "#FAFAFA",
        ...props.style,
      }}
      onClick={props.onClick}
    >
      <SizeableImage src={require("../../../../public/close_modal.png")} size={24} />
    </button>
  );
};

export default CloseButton;