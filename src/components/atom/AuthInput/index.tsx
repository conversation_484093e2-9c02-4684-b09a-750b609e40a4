import { useState } from "react";
import SizeableImage from "../SizeableImage";
import { Color } from "../../../types/Color";

interface AuthInputProps {
  placeholder: string;
  value: string;
  onChange: (val: any) => void;
  type?: string;
  error?: boolean;
  errorMessage?: string;
  onKeyDown?: (e: any) => void;
  style?: React.CSSProperties;
}

const AuthInput = (props: AuthInputProps) => {
  const [inputType, setInputType] = useState<string>("password");

  const handleToggleVisibility = () => {
    setInputType((prev) => (prev === "password" ? "text" : "password"));
  };

  return (
    <div className="mb-3" style={props.style}>
      <div className="relative flex flex-col items-stretch justify-center">
        <input
          value={props.value}
          onChange={props.onChange}
          type={
            props.type === undefined ? "text" : props.type === "password" ? inputType : props.type
          }
          placeholder={props.placeholder}
          className={`border px-3.5 py-2.5 outline-[#D0D5DD] placeholder-[#667085] placeholder:text-base placeholder:font-light focus:outline-[#FCA311] truncate rounded-lg w-full drop-shadow-sm ${
            props.error ? "border-[#FDA29B]" : ""
          }`}
          onKeyDown={props.onKeyDown}
        />
        {props.type === "password" && (
          <span
            className="absolute flex items-center right-0 mr-3 font-normal text-base"
            style={{
              color: Color.COD_GRAY,
            }}
            onClick={handleToggleVisibility}
          >
            <SizeableImage
              size={20}
              src={
                inputType === "password"
                  ? require("../../../../public/eye-off.png")
                  : require("../../../../public/eye-on.png")
              }
            />
            {props.error && (
              <SizeableImage
                size={16}
                src={require("../../../../public/alert-circle.png")}
                style={{
                  marginLeft: 8,
                }}
              />
            )}
          </span>
        )}
      </div>

      {props.error && props.errorMessage && (
        <p className="text-[#F04438] text-sm mt-1">{props.errorMessage}</p>
      )}
    </div>
  );
};

export default AuthInput;
