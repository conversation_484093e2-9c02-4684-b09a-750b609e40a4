export const EmailIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.25 4.25H3.75C2.92157 4.25 2.25 4.92157 2.25 5.75V13.25C2.25 14.0784 2.92157 14.75 3.75 14.75H14.25C15.0784 14.75 15.75 14.0784 15.75 13.25V5.75C15.75 4.92157 15.0784 4.25 14.25 4.25Z"
        stroke="#344054"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.25 5.75L9 10.25L15.75 5.75"
        stroke="#344054"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PencilIcon = (props: { color?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.33325 16.6668H6.66659L15.4166 7.91676C15.8586 7.47473 16.1069 6.87521 16.1069 6.25009C16.1069 5.62497 15.8586 5.02545 15.4166 4.58342C14.9746 4.14139 14.375 3.89307 13.7499 3.89307C13.1248 3.89307 12.5253 4.1414 12.0833 4.58342L3.33325 13.3334V16.6668Z"
        stroke={props.color || "#344054"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 5.41675L14.5833 8.75008"
        stroke={props.color || "#344054"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CheckmarkIcon = (props: { color?: string }) => {
  return (
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.00002 0.166626C4.40002 0.166626 0.666687 3.89996 0.666687 8.49996C0.666687 13.1 4.40002 16.8333 9.00002 16.8333C13.6 16.8333 17.3334 13.1 17.3334 8.49996C17.3334 3.89996 13.6 0.166626 9.00002 0.166626ZM6.74169 12.075L3.75002 9.08329C3.67287 9.00614 3.61167 8.91455 3.56991 8.81374C3.52816 8.71294 3.50667 8.6049 3.50667 8.49579C3.50667 8.38668 3.52816 8.27864 3.56991 8.17784C3.61167 8.07704 3.67287 7.98544 3.75002 7.90829C3.82717 7.83114 3.91876 7.76994 4.01957 7.72819C4.12037 7.68643 4.22841 7.66494 4.33752 7.66494C4.44663 7.66494 4.55467 7.68643 4.65547 7.72819C4.75628 7.76994 4.84787 7.83114 4.92502 7.90829L7.33335 10.3083L13.0667 4.57496C13.2225 4.41914 13.4338 4.33161 13.6542 4.33161C13.8745 4.33161 14.0859 4.41914 14.2417 4.57496C14.3975 4.73077 14.485 4.9421 14.485 5.16246C14.485 5.38281 14.3975 5.59414 14.2417 5.74996L7.91669 12.075C7.83959 12.1522 7.74802 12.2135 7.64721 12.2553C7.54639 12.2971 7.43833 12.3187 7.32919 12.3187C7.22005 12.3187 7.11198 12.2971 7.01117 12.2553C6.91035 12.2135 6.81878 12.1522 6.74169 12.075Z"
        fill={props.color || "#344054"}
      />
    </svg>
  );
};

export const DownArrowIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5 7.5L10 12.5L15 7.5"
        stroke="#394649"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PhoneIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.75 3.5H6.75L8.25 7.25L6.375 8.375C7.17822 10.0036 8.49635 11.3218 10.125 12.125L11.25 10.25L15 11.75V14.75C15 15.1478 14.842 15.5294 14.5607 15.8107C14.2794 16.092 13.8978 16.25 13.5 16.25C10.5744 16.0722 7.81512 14.8299 5.74262 12.7574C3.67013 10.6849 2.42779 7.92555 2.25 5C2.25 4.60218 2.40804 4.22064 2.68934 3.93934C2.97064 3.65804 3.35218 3.5 3.75 3.5Z"
        stroke="#344054"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CloseIcon = () => {
  return (
    <svg width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.99999 5.08599L11.95 0.135986L13.364 1.54999L8.41399 6.49999L13.364 11.45L11.95 12.864L6.99999 7.91399L2.04999 12.864L0.635986 11.45L5.58599 6.49999L0.635986 1.54999L2.04999 0.135986L6.99999 5.08599Z"
        fill="#171717"
      />
    </svg>
  );
};

export const GarbageIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M3.3335 5.83337H16.6668"
        stroke="#FF4242"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.3335 9.16663V14.1666"
        stroke="#FF4242"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.6665 9.16663V14.1666"
        stroke="#FF4242"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.1665 5.83337L4.99984 15.8334C4.99984 16.2754 5.17543 16.6993 5.48799 17.0119C5.80055 17.3244 6.22448 17.5 6.6665 17.5H13.3332C13.7752 17.5 14.1991 17.3244 14.5117 17.0119C14.8242 16.6993 14.9998 16.2754 14.9998 15.8334L15.8332 5.83337"
        stroke="#FF4242"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 5.83333V3.33333C7.5 3.11232 7.5878 2.90036 7.74408 2.74408C7.90036 2.5878 8.11232 2.5 8.33333 2.5H11.6667C11.8877 2.5 12.0996 2.5878 12.2559 2.74408C12.4122 2.90036 12.5 3.11232 12.5 3.33333V5.83333"
        stroke="#FF4242"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const InfoIcon = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
        stroke="#FF8F6C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 8H12.01"
        stroke="#FF8F6C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 12H12V16H13"
        stroke="#FF8F6C"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PlusIcon = (props: { color?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 4.16663V15.8333"
        stroke={props.color || "#344054"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.16675 10H15.8334"
        stroke={props.color || "#344054"}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ArrowLeftIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.5 5L12.5 10L7.5 15"
        stroke="#171717"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const LocationIcon = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 14C13.6569 14 15 12.6569 15 11C15 9.34315 13.6569 8 12 8C10.3431 8 9 9.34315 9 11C9 12.6569 10.3431 14 12 14Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.657 16.657L13.414 20.9C13.039 21.2746 12.5306 21.485 12.0005 21.485C11.4704 21.485 10.962 21.2746 10.587 20.9L6.343 16.657C5.22422 15.5381 4.46234 14.1127 4.15369 12.5608C3.84504 11.009 4.00349 9.40047 4.60901 7.93868C5.21452 6.4769 6.2399 5.22749 7.55548 4.34846C8.87107 3.46943 10.4178 3.00024 12 3.00024C13.5822 3.00024 15.1289 3.46943 16.4445 4.34846C17.7601 5.22749 18.7855 6.4769 19.391 7.93868C19.9965 9.40047 20.155 11.009 19.8463 12.5608C19.5377 14.1127 18.7758 15.5381 17.657 16.657Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ClipboardIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.49984 4.16675H5.83317C5.39114 4.16675 4.96722 4.34234 4.65466 4.6549C4.3421 4.96746 4.1665 5.39139 4.1665 5.83341V15.8334C4.1665 16.2754 4.3421 16.6994 4.65466 17.0119C4.96722 17.3245 5.39114 17.5001 5.83317 17.5001H14.1665C14.6085 17.5001 15.0325 17.3245 15.345 17.0119C15.6576 16.6994 15.8332 16.2754 15.8332 15.8334V5.83341C15.8332 5.39139 15.6576 4.96746 15.345 4.6549C15.0325 4.34234 14.6085 4.16675 14.1665 4.16675H12.4998"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.8333 2.5H9.16667C8.24619 2.5 7.5 3.24619 7.5 4.16667C7.5 5.08714 8.24619 5.83333 9.16667 5.83333H10.8333C11.7538 5.83333 12.5 5.08714 12.5 4.16667C12.5 3.24619 11.7538 2.5 10.8333 2.5Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 10H12.5"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 13.3333H12.5"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const StarIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.99986 14.7915L4.85653 17.4957L5.83903 11.7682L1.67236 7.71238L7.42236 6.87905L9.99403 1.66821L12.5657 6.87905L18.3157 7.71238L14.149 11.7682L15.1315 17.4957L9.99986 14.7915Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PeopleIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7.49984 9.16667C9.34079 9.16667 10.8332 7.67428 10.8332 5.83333C10.8332 3.99238 9.34079 2.5 7.49984 2.5C5.65889 2.5 4.1665 3.99238 4.1665 5.83333C4.1665 7.67428 5.65889 9.16667 7.49984 9.16667Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.5 17.5V15.8333C2.5 14.9493 2.85119 14.1014 3.47631 13.4763C4.10143 12.8512 4.94928 12.5 5.83333 12.5H9.16667C10.0507 12.5 10.8986 12.8512 11.5237 13.4763C12.1488 14.1014 12.5 14.9493 12.5 15.8333V17.5"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3335 2.6084C14.0505 2.79198 14.686 3.20898 15.1399 3.79366C15.5937 4.37833 15.84 5.09742 15.84 5.83757C15.84 6.57771 15.5937 7.2968 15.1399 7.88147C14.686 8.46615 14.0505 8.88315 13.3335 9.06673"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5 17.5V15.8333C17.4958 15.0976 17.2483 14.384 16.7961 13.8037C16.3439 13.2233 15.7124 12.8089 15 12.625"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const KeyIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      stroke={colour}
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
    </svg>
  );
};

export const GearIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8.60417 3.5975C8.95917 2.13417 11.0408 2.13417 11.3958 3.5975C11.4491 3.81733 11.5535 4.02148 11.7006 4.19333C11.8477 4.36518 12.0332 4.49988 12.2422 4.58645C12.4512 4.67303 12.6776 4.70904 12.9032 4.69156C13.1287 4.67407 13.3469 4.60359 13.54 4.48583C14.8258 3.7025 16.2983 5.17417 15.515 6.46083C15.3974 6.65388 15.327 6.87195 15.3096 7.09731C15.2922 7.32267 15.3281 7.54897 15.4146 7.75782C15.5011 7.96666 15.6356 8.15215 15.8073 8.29921C15.9789 8.44627 16.1829 8.55075 16.4025 8.60417C17.8658 8.95917 17.8658 11.0408 16.4025 11.3958C16.1827 11.4491 15.9785 11.5535 15.8067 11.7006C15.6348 11.8477 15.5001 12.0332 15.4135 12.2422C15.327 12.4512 15.291 12.6776 15.3084 12.9032C15.3259 13.1287 15.3964 13.3469 15.5142 13.54C16.2975 14.8258 14.8258 16.2983 13.5392 15.515C13.3461 15.3974 13.1281 15.327 12.9027 15.3096C12.6773 15.2922 12.451 15.3281 12.2422 15.4146C12.0333 15.5011 11.8479 15.6356 11.7008 15.8073C11.5537 15.9789 11.4492 16.1829 11.3958 16.4025C11.0408 17.8658 8.95917 17.8658 8.60417 16.4025C8.5509 16.1827 8.44648 15.9785 8.29941 15.8067C8.15233 15.6348 7.96676 15.5001 7.75779 15.4135C7.54882 15.327 7.32236 15.291 7.09685 15.3084C6.87133 15.3259 6.65313 15.3964 6.46 15.5142C5.17417 16.2975 3.70167 14.8258 4.485 13.5392C4.60258 13.3461 4.67296 13.1281 4.6904 12.9027C4.70785 12.6773 4.67187 12.451 4.58539 12.2422C4.49892 12.0333 4.36438 11.8479 4.19273 11.7008C4.02107 11.5537 3.81714 11.4492 3.5975 11.3958C2.13417 11.0408 2.13417 8.95917 3.5975 8.60417C3.81733 8.5509 4.02148 8.44648 4.19333 8.29941C4.36518 8.15233 4.49988 7.96676 4.58645 7.75779C4.67303 7.54882 4.70904 7.32236 4.69156 7.09685C4.67407 6.87133 4.60359 6.65313 4.48583 6.46C3.7025 5.17417 5.17417 3.70167 6.46083 4.485C7.29417 4.99167 8.37417 4.54333 8.60417 3.5975Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const LockIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.4998 9.16663H7.49984C7.0396 9.16663 6.6665 9.53972 6.6665 9.99996V12.5C6.6665 12.9602 7.0396 13.3333 7.49984 13.3333H12.4998C12.9601 13.3333 13.3332 12.9602 13.3332 12.5V9.99996C13.3332 9.53972 12.9601 9.16663 12.4998 9.16663Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.3335 9.16671V7.50004C8.3335 7.05801 8.50909 6.63409 8.82165 6.32153C9.13421 6.00897 9.55814 5.83337 10.0002 5.83337C10.4422 5.83337 10.8661 6.00897 11.1787 6.32153C11.4912 6.63409 11.6668 7.05801 11.6668 7.50004V9.16671"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.0002 3.33337H5.00016C4.07969 3.33337 3.3335 4.07957 3.3335 5.00004V15C3.3335 15.9205 4.07969 16.6667 5.00016 16.6667H15.0002C15.9206 16.6667 16.6668 15.9205 16.6668 15V5.00004C16.6668 4.07957 15.9206 3.33337 15.0002 3.33337Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const HeadphoneIcon = ({ colour = "#000000" }: { colour?: string }) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.66683 12.5C6.66683 11.5796 5.92064 10.8334 5.00016 10.8334C4.07969 10.8334 3.3335 11.5796 3.3335 12.5V14.1667C3.3335 15.0872 4.07969 15.8334 5.00016 15.8334C5.92064 15.8334 6.66683 15.0872 6.66683 14.1667V12.5Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.6668 12.5C16.6668 11.5796 15.9206 10.8334 15.0002 10.8334C14.0797 10.8334 13.3335 11.5796 13.3335 12.5V14.1667C13.3335 15.0872 14.0797 15.8334 15.0002 15.8334C15.9206 15.8334 16.6668 15.0872 16.6668 14.1667V12.5Z"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.3335 12.5V10C3.3335 8.23193 4.03588 6.53624 5.28612 5.286C6.53636 4.03575 8.23205 3.33337 10.0002 3.33337C11.7683 3.33337 13.464 4.03575 14.7142 5.286C15.9645 6.53624 16.6668 8.23193 16.6668 10V12.5"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 15.8334C15 16.4964 14.4732 17.1323 13.5355 17.6011C12.5979 18.07 11.3261 18.3334 10 18.3334"
        stroke={colour}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const EyeOnIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M7 4.50001C8 4.16668 9.25683 4.16405 10 4.16669C13.3333 4.16669 16.1108 6.11085 18.3333 10C17.685 11.1342 16.99 12.1034 16.2475 12.9067M15 14C13.6408 14.9167 11.6183 15.8334 10 15.8334C6.66667 15.8334 3.88917 13.8892 1.66667 10C2.80751 8.00419 4.0675 6.47168 5.5 5.50001"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx="10"
        cy="10"
        r="2"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const EyeOffIcon = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.5 2.5L17.5 17.5"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.82 8.82251C8.50727 9.13502 8.33149 9.55897 8.33133 10.0011C8.33117 10.4432 8.50665 10.8673 8.81917 11.18C9.13168 11.4927 9.55563 11.6685 9.99774 11.6687C10.4399 11.6688 10.8639 11.4934 11.1767 11.1808"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.80249 4.47084C8.517 4.26644 9.25682 4.16404 9.99999 4.16668C13.3333 4.16668 16.1108 6.11084 18.3333 10C17.685 11.1342 16.99 12.1033 16.2475 12.9067M14.4642 14.4575C13.105 15.3742 11.6183 15.8333 9.99999 15.8333C6.66666 15.8333 3.88916 13.8892 1.66666 10C2.80749 8.00418 4.09416 6.52084 5.52666 5.54918"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
