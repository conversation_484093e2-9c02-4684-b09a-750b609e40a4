import Image from "next/image";
import React, {useMemo} from "react";

interface ProfilePlaceholderProps {
  size: 128 | 40 | 32 | 64 | 116;
  style?: React.CSSProperties;
}

const ProfilePlaceholder = (props: ProfilePlaceholderProps) => {
  const padding = useMemo(() => {
    if(props.size === 128) return 0;
    else if(props.size === 40) return 6;
    else if(props.size === 32) return 4;
    else if(props.size === 64) return 8;
    else if(props.size === 116) return 16;
  },[props.size])
  
  return (
    <div className="rounded-full bg-gray-300 flex items-center justify-center relative overflow-h" style={{
      width: props.size,
      height: props.size,
      ...props.style
    }}>
      <div className="opacity-50 flex items-center justify-center" style={{
        padding
      }}>
        <Image src={require("../../../public/profile_placeholder.png")} />
      </div>
    </div>
  );
};

export default ProfilePlaceholder;
