import React from "react";

interface TextInputProps {
  value: string;
  onChange: (val: string) => void;
  placeholder: string;
  disabled?: boolean;
  isPassword?: boolean;
  hasSearchBadge?: boolean;
  style?: React.CSSProperties;
  styleInput?: React.CSSProperties;
}

const TextInput = (props: TextInputProps) => {
  return (
    <div className="flex relative" style={props.style}>
      {props.hasSearchBadge ? (
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg
            className="w-5 h-5 text-gray-500"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
              clipRule="evenodd"
            ></path>
          </svg>
        </div>
      ) : undefined}
      <input
        type={props.isPassword ? "password" : "text"}
        className="bg-white border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-orange-400 focus:border-orange-400 block w-full p-2.5 outline-none transition-all"
        placeholder={props.placeholder}
        value={props.value}
        onChange={(event) => props.onChange(event.target.value)}
        style={{
          paddingLeft: props.hasSearchBadge ? 38 : 12,
          ...props.styleInput,
        }}
        disabled={props.disabled ? true : false}
      />
    </div>
  );
};

export default TextInput;
