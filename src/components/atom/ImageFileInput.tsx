import React, { useEffect, useRef, useState } from "react";
import imageCompression from "browser-image-compression";
import { Color } from "../../types/Color";

interface ImageFileInputProps {
  title: string;
  uploadedFileName?: string;
  setUploadedImage: (val: string) => void;
  setUploadedFileName: (val: string) => void;
  label?: React.ReactNode;
  style?: React.CSSProperties;
}

const ImageFileInput = (props: ImageFileInputProps) => {
  const [beforeImageFileName, setBeforeImageFileName] = useState<string>("");
  const inputRef = useRef<any>(null);

  useEffect(() => {
    if (props.uploadedFileName === "") {
      const files = new DataTransfer().files;
      inputRef.current.files = files;
    }
  }, [props.uploadedFileName]);

  async function handleImageUpload(event: React.ChangeEvent<HTMLInputElement>) {
    const imageFile = event.target.files![0];
    props.setUploadedFileName(
      inputRef.current.files.length !== 0 ? inputRef.current.files[0].name : beforeImageFileName
    );

    try {
      console.log("Beginning file size:", imageFile.size);
      const compressedFile = await imageCompression(imageFile, {
        maxSizeMB: 0.05,
        fileType: "image/jpeg",
        maxWidthOrHeight: 200,
        useWebWorker: true,
      });
      console.log("compressedFile size:", compressedFile.size);

      props.setUploadedImage(await imageCompression.getDataUrlFromFile(compressedFile));
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <div>
      {props.label}
      <input
        id="file-input"
        ref={inputRef}
        type="file"
        accept="image/*"
        style={{
          display: "none",
        }}
        onChange={handleImageUpload}
      />
      <button
        className="border rounded-lg w-[126px] h-[41px]"
        onClick={() => {
          inputRef.current.click();
          setBeforeImageFileName(
            inputRef.current.files.length !== 0 ? inputRef.current.files[0].name : ""
          );
        }}
        style={{
          borderColor: Color.BLACK_PEARL,
        }}
      >
        <span
          className="font-semibold"
          style={{
            color: Color.BLACK_PEARL,
            fontSize: 14,
          }}
        >
          {props.title}
        </span>
      </button>
    </div>
  );
};

export default ImageFileInput;
