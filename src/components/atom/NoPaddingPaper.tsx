import React from "react"
import Paper from "./Paper";

interface NoPaddingPaperProps {
  children: React.ReactNode;
  borders?: string;
  style?: React.CSSProperties;
}

const NoPaddingPaper = (props: NoPaddingPaperProps) => {
  return (
    <Paper
      borders={props.borders}
      style={{
        padding: 0,
        ...props.style,
      }}
    >
      {props.children}
    </Paper>
  );
}

export default NoPaddingPaper