import { useEffect, useMemo, useRef, useState } from "react";
import ResizingFullModal from "./ResizingFullModal";
import { GarbageIcon, PlusIcon } from "../atom/Icons";
import SizeableImage from "../atom/SizeableImage";
import Text14 from "../atom/Texts/base/Text14";
import Title18 from "../atom/Texts/base/Title18";
import { convertCentToDollar } from "../../functions/local";
import { Addon } from "../../types/Addon";
import { CarService } from "../../types/CarService";
import { Color } from "../../types/Color";

interface AddonSelectionModalProps {
  isOn: boolean;
  onOff: () => void;
  carService: CarService;
  onClickAddAddon: (item: Addon) => void;
  onClickDeleteAddon: (item: Addon) => void;
  addons: Addon[] | undefined;
}

const AddonSelectionModal = (props: AddonSelectionModalProps) => {
  const contentRef = useRef<null | HTMLSpanElement[]>([]);
  const [isClamped, setIsClamped] = useState<Boolean[]>([]);
  const [isExpanded, setIsExpanded] = useState<Boolean[]>([]);

  const WEB_MODAL_WIDTH = 800;
  const WEB_MODAL_HEIGHT = 650;

  let newArrExpanded = [...isExpanded];

  const carText = useMemo(() => {
    if (props.carService?.car === undefined) {
      return "No car selected";
    } else {
      return `${props.carService?.car.make} ${props.carService?.car.model} ${
        props.carService?.car.year
      } ${props.carService?.car.color ? "(" + props.carService?.car.color + ")" : ""}`;
    }
  }, [props.carService?.car]);

  useEffect(() => {
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  if (props.carService?.car === undefined) {
    return null;
  }

  function handleResize() {
    let newArr = [...isClamped];
    if (contentRef && contentRef.current) {
      for (var i = 0; i < contentRef.current.length; i++) {
        if (contentRef.current[i].scrollHeight > contentRef.current[i].clientHeight) {
          newArr[i] = true;
        } else {
          newArr[i] = false;
        }
        setIsClamped(newArr);
      }
    }
  }

  function isAddonSelected(item: Addon) {
    return (
      props.carService?.addons &&
      props.carService.addons.find((addon) => addon.id === item.id) !== undefined
    );
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="bg-white w-full flex flex-col h-full -mt-[0.5px] -ml-[0.2px] md:rounded-xl md:shadow-[0_5px_15px_rgba(0,0,0,0.03)] md:border overflow-hidden">
        <div className="relative flex items-center justify-center py-4 border-b border-b-gray">
          <div className="flex flex-col items-center">
            <Title18 style={{ marginBottom: 4 }}>Add-ons</Title18>
            <Text14 style={{ color: Color.GRAY }}>{carText}</Text14>
          </div>
          <button className="absolute right-5" onClick={props.onOff}>
            <SizeableImage src={require("../../../public/close.png")} size={24} />
          </button>
        </div>
        <div className="overflow-y-scroll h-full xl:h-[600px] xl:rounded-b-xl">
          {props.addons?.map((item, index) => (
            <div
              className="flex flex-col items-start border-b py-5 px-4 xl:flex-row xl:items-center xl:space-x-8 xl:px-6 xl:py-4"
              style={{
                backgroundColor: isAddonSelected(item)
                  ? Color.LIGHT_BACKGROUND
                  : Color.NORMAL_WHITE,
              }}
            >
              <div className="flex flex-row items-center justify-center xl:flex-none">
                <div
                  className="rounded-md overflow-hidden w-[40px] h-[40px] xl:w-[80px] xl:h-[80px]"
                  style={{
                    backgroundColor: Color.LIGHT_BACKGROUND,
                    borderWidth: 1,
                    borderColor: Color.BORDER_GRAY,
                  }}
                >
                  {item.pictureUri ? <img src={item.pictureUri} /> : null}
                </div>
                <div className="flex flex-col ml-4 xl:hidden">
                  <span className="font-medium" style={{ color: Color.NORMAL_BLACK }}>
                    {item.name}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-start justify-between w-full space-y-5 xl:space-y-2">
                <span className="hidden font-medium xl:block" style={{ color: Color.NORMAL_BLACK }}>
                  {item.name}
                </span>
                <span
                  className="hidden text-left font-medium xl:block"
                  style={{ color: Color.GRAY }}
                >
                  {item.description}
                </span>
                <div className="flex flex-col xl:hidden">
                  <span
                    ref={(element) => (contentRef.current[index] = element)}
                    className={`${!isExpanded[index] ? "line-clamp" : "line-clamp-none"}`}
                    style={{
                      color: Color.GRAY,
                    }}
                  >
                    {item.description}
                  </span>
                  <div className="flex flex-row items-end justify-end mt-1">
                    {isClamped[index] && (
                      <button
                        className={`${!isExpanded[index] ? "flex" : "hidden"} text-[#F89A00]`}
                        onClick={() => {
                          newArrExpanded[index] = true;
                          setIsExpanded(newArrExpanded);
                        }}
                      >
                        See More
                      </button>
                    )}
                  </div>
                </div>
                <div className="w-full flex flex-row items-center justify-between">
                  <span style={{ fontSize: 20, fontWeight: "500" }}>
                    ${convertCentToDollar(item.cost)}
                  </span>
                  <button
                    className="flex py-3 px-4 rounded-sm xl:hidden"
                    style={{
                      backgroundColor: isAddonSelected(item) ? Color.BRIDESMAID : Color.ACCENT,
                    }}
                    onClick={() => {
                      if (isAddonSelected(item)) {
                        props.onClickDeleteAddon(item);
                      } else {
                        props.onClickAddAddon(item);
                      }
                    }}
                  >
                    {isAddonSelected(item) ? <GarbageIcon /> : <PlusIcon color="white" />}
                  </button>
                </div>
              </div>
              <button
                className="hidden mr-2 py-3 px-4 rounded-sm xl:block"
                style={{
                  backgroundColor: isAddonSelected(item) ? Color.BRIDESMAID : Color.ACCENT,
                }}
                onClick={() => {
                  if (isAddonSelected(item)) {
                    props.onClickDeleteAddon(item);
                  } else {
                    props.onClickAddAddon(item);
                  }
                }}
              >
                {isAddonSelected(item) ? <GarbageIcon /> : <PlusIcon color="white" />}
              </button>
            </div>
          ))}
        </div>
      </div>
    </ResizingFullModal>
  );
};

export default AddonSelectionModal;
