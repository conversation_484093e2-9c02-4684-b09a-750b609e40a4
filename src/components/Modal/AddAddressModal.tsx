import { useEffect } from "react";
import { useAddressModal } from "../../hooks/useAddressModal";
import AddressAutocomplete from "../../pageComponents/booking_address/AddressAutocomplete";
import ClassifyContent from "../../pageComponents/booking_address/ClassifyContent";
import ContentLabel from "../../pageComponents/booking_address/ContentLabel";
import FormOfContent from "../../pageComponents/booking_address/FormOfContent";
import PhoneSelect from "../../pageComponents/booking_address/PhoneSelect";
import ErrorAlertModal from "../../pageComponents/booking_date/ErrorAlertModal";
import { AddressService } from "../../services/AddressService";
import { Color } from "../../types/Color";
import { PhoneNumber } from "../../types/PhoneNumber";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import CloseButton from "../atom/Buttons/CloseButton";
import ResizingFullModal from "./ResizingFullModal";

interface AddAddressModalProps {
  isOn: boolean;
  onOff: () => void;
  onCreate: () => void;
}

const AddAddressModal = (props: AddAddressModalProps) => {
  const WEB_MODAL_WIDTH = 672;
  const WEB_MODAL_HEIGHT = 641;

  const addressModalHook = useAddressModal();

  async function handleOnClickConfirm() {
    try {
      if (!addressModalHook.isValidatePhone()) {
        return;
      }
      // if (!addressModalHook.isValidatePostalCode()) {
      //   return;
      // }

      addressModalHook.setIsLoading(true);
      await AddressService.create(
        addressModalHook.street1,
        addressModalHook.street2,
        addressModalHook.city,
        addressModalHook.province,
        addressModalHook.postal,
        addressModalHook.country,
        addressModalHook.name,
        new PhoneNumber(
          addressModalHook.phoneRegion,
          addressModalHook.phone
        ).getFullPhoneNumberForAPI()
      );
      addressModalHook.resetVal();
      props.onOff();
      props.onCreate();
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.message) {
        if (error.response.data.message.includes("Missing address component types")) {
          addressModalHook.setErrorMessage({
            title: "Invalid Address",
            description: `Missing ${error.response.data.message
              .split("types: ")[1]
              .split(",")
              .map((type: any) => type.trim())
              .join(", ")}.
              Please enter all required values and try again!`,
          });
        } else if (
          error.response.data.message.includes("Missing the following unconfirmed component types")
        ) {
          addressModalHook.setErrorMessage({
            title: "Invalid Address",
            description: `Not valid ${error.response.data.message
              .split("types: ")[1]
              .split(",")
              .map((type: any) => type.trim())
              .join(", ")}.
              Please enter a valid value and try again!`,
          });
        }
      }
      console.log(error);
    } finally {
      addressModalHook.setIsLoading(false);
    }
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={() => {
        addressModalHook.resetVal();
        props.onOff();
      }}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <ErrorAlertModal
        isOn={addressModalHook.errorMessage !== null}
        onOff={() => addressModalHook.setErrorMessage(null)}
        title={addressModalHook.errorMessage ? addressModalHook.errorMessage.title : ""}
        description={addressModalHook.errorMessage ? addressModalHook.errorMessage.description : ""}
      />
      <div className="flex flex-col flex-1 h-full items-stretch">
        <HeadLine
          title="Add New Address"
          subTitle=""
          onClickCloseButton={() => {
            addressModalHook.resetVal();
            props.onOff();
          }}
        />
        <Content>
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0">
            <FormOfContent>
              <ContentLabel isContentEssential>Customer Name</ContentLabel>
              <ClassifyContent
                placeholder="Input your name here..."
                type="INPUT"
                onChange={(target) => addressModalHook.setName(target)}
                value={addressModalHook.name}
                options={[]}
              />
            </FormOfContent>
            <div style={{ width: 16 }} />
            <FormOfContent>
              <ContentLabel isContentEssential>Phone Number</ContentLabel>
              <div className="flex flex-col space-y-3 xs:flex-row xs:space-y-0">
                <PhoneSelect
                  imageSrc={require("../../../public/flag_US.png")}
                  regionCode="1"
                  style={{
                    flex: "1 1 0%",
                    marginRight: 12,
                  }}
                  onChange={(target) => addressModalHook.setPhoneRegion(target)}
                  value={addressModalHook.phoneRegion}
                />
                <ClassifyContent
                  placeholder="Your phone number..."
                  type="INPUT"
                  style={{
                    flex: "1 1 0%",
                  }}
                  onChange={(target) => addressModalHook.setFormattedPhoneNumber(target)}
                  value={addressModalHook.phone}
                  options={[]}
                />
              </div>
            </FormOfContent>
          </div>
          <div>
            <FormOfContent style={{ position: "relative" }}>
              <ContentLabel isContentEssential>Street Address</ContentLabel>
              <AddressAutocomplete
                onValueChange={(value: string) => addressModalHook.setStreet1(value)}
                isOn={props.isOn}
                initialValue={addressModalHook.street1}
                populateForms={addressModalHook.populateForms}
                disabled={addressModalHook.isAutoCompleteDisabled}
                onClick={() => {
                  addressModalHook.populateFormsRef.current = false;
                  addressModalHook.resetPopulateForms();
                }}
              />
            </FormOfContent>
          </div>
          <div>
            <FormOfContent>
              <ContentLabel>Apartment, Suite, etc. (optional)</ContentLabel>
              <ClassifyContent
                placeholder="Input your street address here..."
                type="INPUT"
                onChange={(target) => addressModalHook.setStreet2(target)}
                value={addressModalHook.street2}
                options={[]}
              />
            </FormOfContent>
          </div>
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0">
            <FormOfContent>
              <ContentLabel isContentEssential>City</ContentLabel>
              <ClassifyContent
                placeholder="Auto complete"
                type="INPUT"
                onChange={(target) => addressModalHook.setCity(target)}
                value={addressModalHook.city}
                disabled={true}
                options={[]}
              />
            </FormOfContent>
            <div style={{ width: 24 }} />
            <FormOfContent>
              <ContentLabel isContentEssential>Province</ContentLabel>
              <ClassifyContent
                placeholder="Auto complete"
                type="INPUT"
                onChange={(target) => addressModalHook.setProvince(target)}
                value={addressModalHook.province}
                disabled={true}
                options={[]}
              />
            </FormOfContent>
          </div>
          <div className="flex flex-col space-y-2 md:flex-row md:space-y-0">
            <FormOfContent>
              <ContentLabel isContentEssential>Country</ContentLabel>
              <ClassifyContent
                placeholder="Auto complete"
                type="INPUT"
                onChange={(target) => addressModalHook.setCountry(target)}
                value={addressModalHook.country}
                disabled={true}
                options={[]}
              />
            </FormOfContent>
            <div style={{ width: 24 }} />
            <FormOfContent>
              <ContentLabel isContentEssential>Postal Code</ContentLabel>
              <ClassifyContent
                placeholder="Auto complete"
                type="INPUT"
                onChange={(target) => {
                  addressModalHook.onChangePostalCodeText(target);
                }}
                value={addressModalHook.postal}
                disabled={true}
                options={[]}
              />
            </FormOfContent>
          </div>
        </Content>
        {/* Web */}
        <EndLine>
          <button
            className="px-3"
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: 1,
              letterSpacing: "0.02em",
              color: Color.COD_GRAY,
            }}
            onClick={() => {
              addressModalHook.resetVal();
              props.onOff();
            }}
          >
            Cancel
          </button>
          <BackgroundSmallButton
            title="Confirm"
            onClick={handleOnClickConfirm}
            isLoading={addressModalHook.isLoading}
            disabled={!addressModalHook.isContinuable}
            backgroundColor="BLACK_PEARL"
            style={{
              width: 80,
              height: 48,
              fontWeight: 500,
              fontSize: 14,
              lineHeight: 1,
              letterSpacing: "0.02em",
              borderRadius: 8,
              paddingLeft: 12,
              paddingRight: 12,
              paddingTop: 14.5,
              paddingBottom: 14.5,
            }}
          />
        </EndLine>
        {/* Mobile */}
        <EndLineMobile>
          <BackgroundSmallButton
            title="Confirm"
            onClick={handleOnClickConfirm}
            isLoading={addressModalHook.isLoading}
            disabled={!addressModalHook.isContinuable}
            backgroundColor="BLACK_PEARL"
            style={{
              width: "100%",
              paddingTop: 14.5,
              paddingBottom: 14.5,
              paddingLeft: 12,
              paddingRight: 12,
              fontWeight: 500,
              fontSize: 14,
              borderRadius: 8,
            }}
          />
          <button
            className="text-center"
            style={{
              width: "100%",
              paddingTop: 14.5,
              paddingBottom: 14.5,
              paddingLeft: 12,
              paddingRight: 12,
              fontWeight: 600,
              fontSize: 14,
              color: Color.COD_GRAY,
            }}
            onClick={() => {
              addressModalHook.resetVal();
              props.onOff();
            }}
          >
            Cancel
          </button>
        </EndLineMobile>
      </div>
    </ResizingFullModal>
  );
};

const Title = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        color: Color.COD_GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const SubTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 400,
        fontSize: 16,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE_06,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const HeadLine = (props: { onClickCloseButton: () => void; title: string; subTitle: string }) => {
  return (
    <div
      className="flex justify-between items-center px-6 py-4"
      style={{
        borderBottom: "0.5px solid #E7E6EB",
      }}
    >
      <div className="flex flex-col gap-1">
        <Title>{props.title}</Title>
        <SubTitle>{props.subTitle}</SubTitle>
      </div>
      <CloseButton
        onClick={props.onClickCloseButton}
        style={{
          minWidth: 40,
          minHeight: 40,
        }}
      />
    </div>
  );
};

const EndLine = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="hidden flex-row justify-between px-6 py-4 md:flex"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const EndLineMobile = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-col justify-between px-6 py-4 gap-4 md:hidden"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const Content = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-col items-stretch px-6 py-4 gap-6 h-full overflow-y-scroll"
      style={props.style}
    >
      {props.children}
    </div>
  );
};

export default AddAddressModal;
