import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../@redux";
import { Color } from "../../types/Color";
import { CloseIcon } from "../atom/Icons";
import Title18 from "../atom/Texts/base/Title18";
import ModalConfirmCancelBar from "../molecule/ModalConfirmCancelBar";
import ResizingFullModal from "./ResizingFullModal";
import {
  apiFetchTechnicianPriority,
  apiFetchTechnicianEfficiency,
  apiUpdateTechEfficiency,
  apiUpdateTechPriority,
} from "../../functions/api/technician";
import { apiListTechServices, apiUpdateTechServices } from "../../functions/api/service";
import { apiListTechZones, apiUpdateTechZones } from "../../functions/api/zones";

interface EditTechnicianModalProps {
  isOn: boolean;
  onOff: () => void;
  technicianId: number;
}

const EditTechnicianModal = (props: EditTechnicianModalProps) => {
  const isLoading = useSelector((state: RootState) => state.auth.isLoading);
  const [services, setServices] = useState<any[]>([]);
  const [zones, setZones] = useState<any[]>([]);
  const [priority, setPriority] = useState<number>();
  const [isEfficient, setIsEfficient] = useState<boolean>(false);

  const MOBILE_MODAL_WIDTH = 398;
  const WEB_MODAL_WIDTH = 672;
  const MOBILE_MODAL_HEIGHT = 582;
  const WEB_MODAL_HEIGHT = 509;

  useEffect(() => {
    console.log("EditTechnicianModal useEffect");
    if (props.isOn) {
      console.log("EditTechnicianModal useEffect isOn");
      fetchTechServices(props.technicianId);
      fetchTechZones(props.technicianId);
      fetchPriority(props.technicianId);
      fetchEfficiency(props.technicianId);
    }
  }, [props.isOn]);

  async function fetchTechZones(technicianId: number) {
    console.log("fetchTechZones");
    const result = await apiListTechZones(technicianId);
    console.log("fetchTechZones result", result);
    setZones(result || []);
  }

  async function fetchTechServices(technicianId: number) {
    console.log("fetchTechServices");
    const result = await apiListTechServices(technicianId);
    console.log("fetchTechServices result", result);
    setServices(result || []);
  }

  async function fetchPriority(technicianId: number) {
    console.log("fetchPriority");
    const result = await apiFetchTechnicianPriority(technicianId);
    console.log("fetchPriority result", result);
    setPriority(result.priority || 0);
  }

  async function fetchEfficiency(technicianId: number) {
    console.log("fetchEfficiency");
    try {
      const result = await apiFetchTechnicianEfficiency(technicianId);
      console.log("fetchEfficiency result", result);
      setIsEfficient(result.isEfficient || false);
    } catch (error) {
      console.log("fetchEfficiency error", error);
      // Default to false if there's an error or no data
      setIsEfficient(false);
    }
  }

  async function handleOnClickConfirm() {
    console.log("handleOnClickConfirm");
    try {
      await apiUpdateTechServices(props.technicianId, services);
      await apiUpdateTechZones(props.technicianId, zones);
      await apiUpdateTechPriority(props.technicianId, priority || 0);
      await apiUpdateTechEfficiency(props.technicianId, isEfficient);
      props.onOff();
    } catch (error) {
      console.log("error", error);
    }
  }

  const handleServiceCheckboxChange = (index: number) => {
    const updatedServices = services.map((service, idx) =>
      idx === index ? { ...service, selected: service.selected === 1 ? 0 : 1 } : service,
    );
    setServices(updatedServices);
  };

  const handleZoneCheckboxChange = (index: number) => {
    const updatedZones = zones.map((zone, idx) =>
      idx === index ? { ...zone, selected: zone.selected === 1 ? 0 : 1 } : zone,
    );
    setZones(updatedZones);
  };

  const handlePriorityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const numericValue = Number(value);

    if (isNaN(numericValue)) {
      console.log("Please enter a valid number");
    } else {
      // setPriorityError(null);
      setPriority(numericValue);
    }
  };

  const handleEfficiencyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsEfficient(e.target.checked);
  };

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col flex-1 h-full items-stretch">
        <div className="text-center flex justify-center items-center p-5 border-b-[0.5px] border-gray-200">
          <Title18>Edit Technician</Title18>
          <button className="absolute top-7 right-8" onClick={props.onOff}>
            <CloseIcon />
          </button>
        </div>
        <div className="h-full overflow-y-scroll p-5">
          {/* Priority and Efficient fields row */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex flex-col">
              <h6
                className="text-lg font-semibold ml-2"
                style={{
                  color: Color.NORMAL_BLACK,
                }}
              >
                Priority
              </h6>
              <input
                type="text"
                placeholder="Enter priority"
                className="p-2 border border-gray-300 rounded-lg mt-1"
                style={{ width: "100%" }}
                value={priority}
                onChange={handlePriorityChange}
              />
            </div>
            <div className="flex flex-col">
              <h6
                className="text-lg font-semibold ml-2"
                style={{
                  color: Color.NORMAL_BLACK,
                }}
              >
                Efficient
              </h6>
              <div className="flex items-center mt-1 ml-2">
                <input
                  type="checkbox"
                  checked={isEfficient}
                  onChange={handleEfficiencyChange}
                  className="mr-2 flex-shrink-0"
                  style={{ width: "20px", height: "20px" }}
                />
                <span>Reduces service time by 15min after the first vehicle of an appointment</span>
              </div>
            </div>
          </div>

          {/* Services and Zones row */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col">
              <h6
                className="text-lg font-semibold ml-2"
                style={{
                  color: Color.NORMAL_BLACK,
                }}
              >
                Allowed Services
              </h6>
              {Array.isArray(services) && services.length !== 0
                ? services.map((service, index) => (
                    <div key={index} className={`flex items-center p-2 border-t border-gray-30`}>
                      <input
                        type="checkbox"
                        checked={service.selected || false}
                        onChange={() => handleServiceCheckboxChange(index)}
                        className="mr-2 flex-shrink-0"
                        style={{ width: "20px", height: "20px" }}
                      />
                      <span>{service.name}</span>
                    </div>
                  ))
                : null}
            </div>

            {/* Second column */}
            <div className="flex flex-col">
              <h6
                className="text-lg font-semibold ml-2"
                style={{
                  color: Color.NORMAL_BLACK,
                }}
              >
                Allowed Zones
              </h6>
              {Array.isArray(zones) && zones.length !== 0
                ? zones.map((zone, index) => (
                    <div key={index} className={`flex items-center p-2 border-t border-gray-30`}>
                      <input
                        type="checkbox"
                        checked={zone.selected || false}
                        onChange={() => handleZoneCheckboxChange(index)}
                        className="mr-2 flex-shrink-0"
                        style={{ width: "20px", height: "20px" }}
                      />
                      <span>{`${zone.name} - ${zone.description}`}</span>
                    </div>
                  ))
                : null}
            </div>
          </div>
        </div>

        <ModalConfirmCancelBar
          confirmButtonTitle="Save"
          onClickConfirmButton={handleOnClickConfirm}
          cancelButtonTitle="Cancel"
          onClickCancelButton={props.onOff}
          isConfirmDisabled={false}
          isLoading={isLoading}
          styleCancelButton={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
          styleConfirmButton={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            width: 79,
            height: 48,
            fontWeight: 500,
          }}
          styleConfirmButtonMobile={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            fontWeight: 500,
          }}
          styleCancelButtonMobile={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
        />
      </div>
    </ResizingFullModal>
  );
};

export default EditTechnicianModal;
