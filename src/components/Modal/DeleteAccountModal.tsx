import { useState } from "react";
import ResizingFullModal from "../../components/Modal/ResizingFullModal";
import { CloseIcon } from "../../components/atom/Icons";
import TextInput from "../../components/atom/TextInput";
import Text14 from "../../components/atom/Texts/base/Text14";
import Text16 from "../../components/atom/Texts/base/Text16";
import Title20 from "../../components/atom/Texts/base/Title20";
import ModalConfirmCancelBar from "../../components/molecule/ModalConfirmCancelBar";
import { MODAL_PADDING } from "../../theme";
import { Color } from "../../types/Color";

interface DeleteAccountModalProps {
  isOn: boolean;
  onOff: () => void;
  isLoading: boolean;
  disableBackground?: boolean;
}

const DeleteAccountModal = (props: DeleteAccountModalProps) => {
  const MOBILE_MODAL_WIDTH = 398;
  const WEB_MODAL_WIDTH = 672;
  const MOBILE_MODAL_HEIGHT = 454;
  const WEB_MODAL_HEIGHT = 360;

  const [deleteMessage, setDeleteMessage] = useState<string>("");

  function handleOnClickConfirm() {
    alert("not implemented");
  }

  function resetVal() {
    setDeleteMessage("");
    props.onOff();
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={resetVal}
      disableBackground={props.disableBackground}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col flex-1 items-stretch h-full">
        <div className="text-center flex justify-center items-center p-5 border-b-[0.5px] border-gray-200">
          <Title20
            style={{
              color: Color.COD_GRAY,
            }}
          >
            Delete Account
          </Title20>
          <button className="absolute top-7 right-8" onClick={resetVal}>
            <CloseIcon />
          </button>
        </div>
        <div className="h-full overflow-y-scroll p-5">
          <div className="flex flex-col gap-4">
            <Text16
              style={{
                color: Color.COD_GRAY,
              }}
            >
              Are you sure want to delete your account? After you confirm to delete this account the
              data will permanently deleted too. There’s no going back.
            </Text16>
            <Text14
              style={{
                color: Color.COD_GRAY,
              }}
            >
              Please type <span className="font-medium text-sm">“Delete Me”</span> to confirm
            </Text14>
            <div className="flex flex-col gap-2">
              <Text14
                className="font-medium"
                style={{
                  color: Color.BLACK_PEARL,
                }}
              >
                Description
              </Text14>
              <TextInput
                value={deleteMessage}
                onChange={(val) => setDeleteMessage(val)}
                placeholder="Delete Me"
                styleInput={{
                  color: Color.PALE_SKY,
                }}
              />
            </div>
          </div>
        </div>
        <ModalConfirmCancelBar
          confirmButtonTitle="Delete Account"
          onClickConfirmButton={handleOnClickConfirm}
          cancelButtonTitle="Cancel"
          onClickCancelButton={resetVal}
          isConfirmDisabled={deleteMessage !== "Delete Me"}
          isLoading={props.isLoading}
          styleCancelButton={{
            color: Color.COD_GRAY,
            fontWeight: 600,
          }}
          styleConfirmButton={{
            paddingLeft: 12,
            paddingRight: 12,
            paddingTop: 14.5,
            paddingBottom: 14.5,
            width: 128,
            height: 48,
            fontWeight: 500,
          }}
          styleConfirmButtonMobile={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            fontWeight: 500,
          }}
          styleCancelButtonMobile={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
        />
      </div>
    </ResizingFullModal>
  );
};

export default DeleteAccountModal;
