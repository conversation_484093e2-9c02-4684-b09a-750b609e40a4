import { useContext } from "react";
import Modal from ".";
import { globalContext } from "../../contexts/GlobalContext";

interface ResizingFullModalProps {
  className?: string;
  children?: React.ReactNode;
  isOn: boolean;
  onOff: () => void;
  disableBackground?: boolean;
  modalSize: {
    web: {
      width: number;
      height: number;
    };
  };
}

const ResizingFullModal = (props: ResizingFullModalProps) => {
  const context = useContext(globalContext);

  return (
    <Modal isOn={props.isOn} onOff={props.onOff} disableBackground={props.disableBackground}>
      <div
        className={`flex flex-col w-screen h-screen bg-white justify-between md:rounded-lg xl:m-0 min-w-[320px] min-h-[200px] md:w-auto md:h-auto ${
          props.className !== undefined ? props.className : ""
        }`}
        style={{
          width: context?.getResponsiveModalFullWidthByDeviceSize(props.modalSize.web.width),
          height: context?.getResponsiveModalFullHeightByDeviceSize(props.modalSize.web.height),
        }}
      >
        {props.children}
      </div>
    </Modal>
  );
};

export default ResizingFullModal;
