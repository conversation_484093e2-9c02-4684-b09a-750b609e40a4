import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../@redux";
import { thunkUpdateUserNamePhone, thunkUpdateUserProfile } from "../../@redux/modules/auth/thunks";
import PhoneSelect from "../../pageComponents/booking_address/PhoneSelect";
import { Color } from "../../types/Color";
import { Input } from "../../types/Input";
import { PhoneNumber } from "../../types/PhoneNumber";
import { CloseIcon } from "../atom/Icons";
import ImageFileInput from "../atom/ImageFileInput";
import TextInput from "../atom/TextInput";
import GrayParagraph from "../atom/Texts/GrayParagraph";
import Text14 from "../atom/Texts/base/Text14";
import Title18 from "../atom/Texts/base/Title18";
import ModalConfirmCancelBar from "../molecule/ModalConfirmCancelBar";
import ResizingFullModal from "./ResizingFullModal";

interface EditProfileModalProps {
  isOn: boolean;
  onOff: () => void;
  profilePictureUri: string;
  firstName: string;
  lastName: string;
  phone: string;
}

const EditProfileModal = (props: EditProfileModalProps) => {
  const dispatch = useDispatch<any>();
  const isLoading = useSelector((state: RootState) => state.auth.isLoading);
  const [firstNameInput, setFirstNameInput] = useState<string>(props.firstName);
  const [lastNameInput, setLastNameInput] = useState<string>(props.lastName);
  const [phoneRegion, setPhoneRegion] = useState<string>("+1");
  const [phoneInput, setPhoneInput] = useState<string>(props.phone);

  const [uploadedFileName, setUploadedFileName] = useState<string | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string>(props.profilePictureUri);
  const isFirstCalled = useRef<boolean>(true);

  const MOBILE_MODAL_WIDTH = 398;
  const WEB_MODAL_WIDTH = 672;
  const MOBILE_MODAL_HEIGHT = 582;
  const WEB_MODAL_HEIGHT = 509;

  useEffect(() => {
    if (isFirstCalled.current) {
      isFirstCalled.current = false;
    } else if (!isLoading) {
      props.onOff();
    }
  }, [isLoading]);

  useEffect(() => {
    if (props.isOn) {
      setupVal();
    }
  }, [props.isOn]);

  function setupVal() {
    setFirstNameInput(props.firstName);
    setLastNameInput(props.lastName);

    const existingPhoneNumber = PhoneNumber.getPhoneNumberFromAPI(props.phone);

    setPhoneRegion(existingPhoneNumber.getPhoneRegion());
    setPhoneInput(existingPhoneNumber.getFormattedPhoneNumber());

    setUploadedImage(props.profilePictureUri);
    setUploadedFileName(null);
  }

  function setFormattedPhoneNumber(value: string) {
    const isBackspace = value.length < phoneInput.length;
    const newValue = Input.formatNewInputIntoPhoneNumber(value, phoneInput, isBackspace);
    setPhoneInput(newValue || "");
  }

  async function handleOnClickConfirm() {
    try {
      console.log("handleOnClickConfirm");
      await dispatch(
        thunkUpdateUserNamePhone({
          firstName: firstNameInput,
          lastName: lastNameInput,
          phone: new PhoneNumber(phoneRegion, phoneInput).getFullPhoneNumberForAPI(),
        })
      );
      // console.log("uploadedFileName", uploadedFileName);
      // console.log("uploadedImage", uploadedImage);
      if (uploadedFileName !== null && uploadedImage !== "") {
        await dispatch(thunkUpdateUserProfile(uploadedImage));
      }
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col flex-1 h-full items-stretch">
        <div className="text-center flex justify-center items-center p-5 border-b-[0.5px] border-gray-200">
          <Title18>Edit Profile</Title18>
          <button className="absolute top-7 right-8" onClick={props.onOff}>
            <CloseIcon />
          </button>
        </div>
        <div className="h-full overflow-y-scroll p-5">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6 mb-4">
            <div className="w-20 h-20 rounded-lg overflow-hidden">
              {uploadedImage ? (
                <img className="object-cover w-20 h-20" src={uploadedImage} />
              ) : (
                <Image src={require("../../../public/upload_photo_placeholder.png")} />
              )}
            </div>
            <div className="flex flex-col gap-3">
              <ImageFileInput
                title="Change Photo"
                setUploadedFileName={(val) => setUploadedFileName(val)}
                setUploadedImage={(val) => setUploadedImage(val)}
              />
              <GrayParagraph
                style={{
                  fontWeight: 300,
                  fontSize: 16,
                  color: Color.DOVE_GRAY,
                }}
              >
                PNG or JPG max. 10 MB
              </GrayParagraph>
            </div>
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-[6px]">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                First Name
              </Text14>
              <TextInput
                value={firstNameInput}
                onChange={(val) => setFirstNameInput(val)}
                placeholder="First Name"
                styleInput={{
                  fontSize: 16,
                }}
              />
            </div>
            <div className="flex flex-col gap-[6px]">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                Last Name
              </Text14>
              <TextInput
                value={lastNameInput}
                onChange={(val) => setLastNameInput(val)}
                placeholder="Last Name"
                styleInput={{
                  fontSize: 16,
                }}
              />
            </div>
            <div className="flex flex-col gap-[6px] w-full">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                Phone
              </Text14>
              <div className="flex w-full flex-col space-y-2 xs:flex-row xs:space-y-0">
                <PhoneSelect
                  imageSrc={require("../../../public/flag_US.png")}
                  regionCode="1"
                  style={{
                    marginRight: 12,
                  }}
                  onChange={(target) => setPhoneRegion(target)}
                  value={phoneRegion}
                />
                <TextInput
                  value={phoneInput}
                  onChange={(val) => setFormattedPhoneNumber(val)}
                  placeholder="Phone"
                  style={{
                    flex: 1,
                  }}
                  styleInput={{
                    fontSize: 16,
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        <ModalConfirmCancelBar
          confirmButtonTitle="Confirm"
          onClickConfirmButton={handleOnClickConfirm}
          cancelButtonTitle="Cancel"
          onClickCancelButton={props.onOff}
          isConfirmDisabled={firstNameInput === "" || lastNameInput === "" || phoneInput === ""}
          isLoading={isLoading}
          styleCancelButton={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
          styleConfirmButton={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            width: 79,
            height: 48,
            fontWeight: 500,
          }}
          styleConfirmButtonMobile={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            fontWeight: 500,
          }}
          styleCancelButtonMobile={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
        />
      </div>
    </ResizingFullModal>
  );
};

export default EditProfileModal;
