import { useContext, useEffect, useMemo } from "react";
import ResizingFullModal from "./ResizingFullModal";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import CloseButton from "../atom/Buttons/CloseButton";
import { addonContext } from "../../contexts/AddonContext";
import { Color } from "../../types/Color";
import SelectAddonBlock from "../../pageComponents/addons_information/SelectAddonBlock";
import { MD, XXS } from "../../values";
import { globalContext } from "../../contexts/GlobalContext";
import ClearAddonButton from "../../pageComponents/addons_information/ClearAddonButton";

const SelectAddonModal = () => {
  const context = useContext(addonContext)!;
  const addons = context!.remoteAddons;
  const isOn = context!.isSelectAddonModalOn;

  const deviceWidth = useContext(globalContext)?.dimensionWidth;
  const WEB_MODAL_WIDTH = 650;
  const WEB_MODAL_HEIGHT = 704;

  useEffect(() => {
    initSelectedAddons();
  }, [context.carServiceExtensionHook.carServices, isOn]);

  const subTitle = useMemo(() => {
    if (context.carServiceExtensionHook.carServices && context.selectedCarAddonIndex !== -1) {
      return context.carServiceExtensionHook.carServices[context.selectedCarAddonIndex]
        ? context?.carServiceExtensionHook.carServices[context.selectedCarAddonIndex].car.make +
            " " +
            context?.carServiceExtensionHook.carServices[context.selectedCarAddonIndex].car.model +
            " " +
            context?.carServiceExtensionHook.carServices[context.selectedCarAddonIndex].car.year
        : "";
    } else return "";
  }, [context.carServiceExtensionHook.carServices, context.selectedCarAddonIndex]);

  function onOff() {
    context?.setIsSelectAddonModalOn(false);
  }

  function initSelectedAddons() {
    if (context.carServiceExtensionHook.carServices) {
      context.setSelectedAddons(
        context.carServiceExtensionHook.carServices[
          context.carServiceExtensionHook.selectedCarAddonIndex
        ]?.addons
      );
    }
  }

  return (
    <ResizingFullModal
      isOn={isOn}
      onOff={onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col bg-white rounded-lg w-full h-full md:w-[650px] md:h-[704px]">
        <div className="flex flex-col flex-1 h-full items-stretch">
          <HeadLine title="Add-ons" subTitle={subTitle} onClickCloseButton={onOff} />
          <Content>
            {addons !== undefined && addons.length !== 0 ? (
              <>
                <div className="grid grid-cols-1 xxs:max-md:grid-cols-2 md:grid-cols-3 grid-flow-row gap-4">
                  {addons.map((addon, index) => (
                    <SelectAddonBlock
                      addon={addon}
                      key={index.toString()}
                      onClick={() => context?.handleOnClickAddonBlock(addon)}
                      isSelected={context!.getIsAddonInArray(addon)}
                      isRightEnd={
                        deviceWidth && deviceWidth > MD
                          ? index % 3 === 2
                          : deviceWidth && deviceWidth > XXS
                          ? index % 2 === 1
                          : index % 1 === 0
                      }
                      isLeftEnd={
                        deviceWidth && deviceWidth > MD
                          ? index % 3 === 0
                          : deviceWidth && deviceWidth > XXS
                          ? index % 2 === 0
                          : false
                      }
                      isFirstRow={
                        deviceWidth && deviceWidth > MD
                          ? index <= 2
                          : deviceWidth && deviceWidth > XXS
                          ? index <= 1
                          : index <= 0
                      }
                    />
                  ))}
                </div>
              </>
            ) : null}
          </Content>
          {/* web */}
          <EndLine>
            <div className="flex flex-col items-center justify-center">
              <ClearAddonButton
                onClick={() => {
                  if (context) {
                    context.handleOnClickClearAddon();
                  }
                }}
              />
            </div>
            <div className="flex flex-row">
              <div style={{ marginRight: 16 }} />
              <BackgroundSmallButton
                title={
                  context!.selectedAddons.length === 0
                    ? `Add No Add-on`
                    : context!.selectedAddons.length === 1
                    ? `Add ${context!.selectedAddons.length} Add-on`
                    : `Add ${context!.selectedAddons.length} Add-ons`
                }
                onClick={() => {
                  if (context) {
                    context.handleOnClickAddAddon(context!.selectedAddons);
                    context.setIsSelectAddonModalOn(false);
                  }
                }}
                backgroundColor="DARK_BLUE"
                style={{
                  width: 135,
                  borderRadius: 8,
                  paddingTop: 10,
                  paddingBottom: 10,
                  paddingLeft: 17.5,
                  paddingRight: 17.5,
                }}
                disabled={context!.selectedAddons.length < 0}
              />
            </div>
          </EndLine>
          {/* mobile */}
          <EndLineMobile>
            <div className="flex flex-col items-center justify-center flex-1">
              <ClearAddonButton
                onClick={() => {
                  if (context) {
                    context.handleOnClickClearAddon();
                  }
                }}
              />
            </div>
            <div className="flex flex-row flex-1">
              <div style={{ marginRight: 16 }} />
              <BackgroundSmallButton
                title={
                  context!.selectedAddons.length === 0
                    ? `Add No Add-on`
                    : context!.selectedAddons.length === 1
                    ? `Add ${context!.selectedAddons.length} Add-on`
                    : `Add ${context!.selectedAddons.length} Add-ons`
                }
                onClick={() => {
                  if (context) {
                    context.handleOnClickAddAddon(context!.selectedAddons);
                    context.setIsSelectAddonModalOn(false);
                  }
                }}
                backgroundColor="DARK_BLUE"
                style={{
                  width: "100%",
                  borderRadius: 8,
                  paddingTop: 10,
                  paddingBottom: 10,
                  paddingLeft: 17.5,
                  paddingRight: 17.5,
                }}
                disabled={context!.selectedAddons.length < 0}
              />
            </div>
          </EndLineMobile>
        </div>
      </div>
    </ResizingFullModal>
  );
};

const Title = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        color: Color.COD_GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const SubTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 400,
        fontSize: 16,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE_06,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const HeadLine = (props: { onClickCloseButton: () => void; title: string; subTitle: string }) => {
  return (
    <div
      className="flex justify-between items-center px-6 py-4"
      style={{
        borderBottom: "0.5px solid #E7E6EB",
      }}
    >
      <div className="flex flex-col gap-1">
        <Title>{props.title}</Title>
        <SubTitle>{props.subTitle}</SubTitle>
      </div>
      <CloseButton onClick={props.onClickCloseButton} />
    </div>
  );
};

const EndLine = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="hidden md:flex flex-row justify-between px-6 py-4"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const EndLineMobile = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex md:hidden flex-row justify-between p-4 pt-3"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const Content = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-col p-4 md:px-6 md:py-4 h-full overflow-x-hidden overflow-y-scroll"
      style={props.style}
    >
      {props.children}
    </div>
  );
};

export default SelectAddonModal;
