import { useContext, useMemo } from "react";
import { calendarContext } from "../../contexts/CalendarContext";
import { globalContext } from "../../contexts/GlobalContext";
import CalendarDatePicker from "../../pageComponents/availability/CalendarDatePicker";
import { Color } from "../../types/Color";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import BorderButton from "../atom/Buttons/BorderButton";
import SizeableImage from "../atom/SizeableImage";
import Spinner from "../atom/Spinner";
import Title24 from "../atom/Texts/base/Title24";
import TechnicianReviewModal from "../organism/TechnicianReviewModal";
import ResizingFullModal from "./ResizingFullModal";
import { Moment } from "moment";
import { useTechnicianSearch } from "../../hooks/useTechnicianSearch";

type SelectDateTechnicianModalProps = {
  isOn: boolean;
  onOff: () => void;
  showFirstStep: boolean;
  showSecondStep: boolean;
  modalTitle: string;
  modalButtonText: string;
  selectedMonth: Moment | null;
  selectedDate: Moment | null;
  selectedTime: Moment | null;
  getIsDateAvailable: (day: Moment) => boolean;
  isWideModal: boolean;
  isLoading: boolean;
  onClickDate: (date: number) => void;
  onClickTime: (timeOption: string) => void;
  isConfirmButtonDisabled: boolean;
  onClickConfirmButton: () => void;
  showBackButton: boolean;
  onClickBackButton: () => void;
  showCalendarPicker: boolean;
  weekDays: string[];
  generatedWeeks: (Moment | null)[][];
  isPreviousCalendarMonthDisabled: boolean;
  addMonthToSelectedTime: () => void;
  minusMonthToSelectedTime: () => void;
  availableHours: string[];
  isConfirmButtonLoading?: boolean;
};

const SelectDateTechnicianModal = ({
  isOn,
  onOff,
  showFirstStep,
  showSecondStep,
  modalTitle,
  modalButtonText,
  selectedMonth,
  selectedDate,
  selectedTime,
  getIsDateAvailable,
  isWideModal,
  isLoading,
  onClickDate,
  onClickTime,
  isConfirmButtonDisabled,
  onClickConfirmButton,
  showBackButton,
  onClickBackButton,
  showCalendarPicker,
  weekDays,
  generatedWeeks,
  isPreviousCalendarMonthDisabled,
  addMonthToSelectedTime,
  minusMonthToSelectedTime,
  availableHours,
  isConfirmButtonLoading
}: SelectDateTechnicianModalProps) => {
  const gContext = useContext(globalContext);
  const isMobile = useMemo(() => gContext?.isMobile || false, [gContext?.isMobile]);

  return (
    <>
      <ResizingFullModal
        isOn={isOn}
        onOff={onOff}
        modalSize={{
          web: {
            width: isWideModal ? 1054 : 448,
            height: 650,
          },
        }}
        className="md:transition-width md:duration-500 md:ease-out"
      >
        <div
          className={`relative flex flex-col bg-white h-full rounded-none md:rounded-lg overflow-hidden`}
          style={{
            border: "#E7E6EB",
            boxShadow: "0px 5px 15px rgba(0,0,0,0.05)",
          }}
        >
          <div className="flex items-center justify-start py-5 px-6 border-b border-b-gray">
            <div className="flex flex-col items-center">
              <Title24>{modalTitle}</Title24>
            </div>
            <button
              className="absolute flex items-center justify-center rounded-full right-6 border"
              style={{
                width: 40,
                height: 40,
                borderColor: Color.MISCHKA,
                backgroundColor: Color.ALABASTER,
              }}
              onClick={onOff}
            >
              <SizeableImage src={require("../../../public/modal_close.png")} size={24} />
            </button>
          </div>
          {isLoading ? (
            <div className="flex flex-col flex-1 items-center justify-center h-full self-stretch p-6">
              <Spinner />
            </div>
          ) : (
            <div className="flex items-stretch justify-between self-stretch h-full overflow-hidden">
              {showCalendarPicker ? (
                <CalendarDatePicker
                  showFirstStep={showFirstStep}
                  showSecondStep={showSecondStep}
                  getIsDateAvailable={getIsDateAvailable}
                  getDateBackgroundColor={(day: Moment | null) => {
                    let result;
                    if (day) {
                      if (day.isSame(selectedDate, "day"))
                        result = Color.ACCENT;
                      else if (getIsDateAvailable(day))
                        result = Color.BACKGROUND_ACCENT;
                      else result = "transparent";
                    } else {
                      result = "transparent";
                    }

                    return result;
                  }}
                  getDateFontColor={(day: Moment | null) => {
                    let result;
                    if (day) {
                      if (day.isSame(selectedDate, "day")) result = "white";
                      else if (getIsDateAvailable(day)) result = Color.ACCENT;
                      else result = "gray";
                    } else {
                      result = "transparent";
                    }

                    return result;
                  }}
                  selectedMonth={selectedMonth}
                  isPreviousCalendarMonthDisabled={isPreviousCalendarMonthDisabled}
                  minusMonthToSelectedTime={minusMonthToSelectedTime}
                  addMonthToSelectedTime={addMonthToSelectedTime}
                  weekDays={weekDays}
                  generatedWeeks={generatedWeeks}
                  availableHours={availableHours}
                  selectedTime={selectedTime}
                  selectedDate={selectedDate}
                  onClickDate={onClickDate}
                  onClickTime={onClickTime}
                />
              ) : null}
              {/* {(!isMobile || context.step === 2) && context.technicianSearchHook.isTimeSelected ? (
                <TechnicianList
                  onClickReviewButton={(index) => context.setReviewTechIndex(index)}
                />
              ) : null} */}
            </div>
          )}
          <div className="flex flex-col md:flex-row p-4 border-t border-t-gray justify-end">
            <BackgroundSmallButton
              isLoading={isConfirmButtonLoading || false}
              disabled={isConfirmButtonLoading || isConfirmButtonDisabled}
              backgroundColor="DARK_BLUE"
              title={modalButtonText || ""}
              onClick={onClickConfirmButton}
              style={{
                height: 50,
                width: isMobile ? "100%" : 100,
                borderRadius: 8,
              }}
            />
            {showBackButton ? (
              <BorderButton
                title="Back"
                onClick={onClickBackButton}
                style={{
                  height: 50,
                  width: isMobile ? "100%" : 100,
                  borderRadius: 8,
                  marginTop: 12,
                }}
              />
            ) : null}
          </div>
        </div>
      </ResizingFullModal>
    </>
  );
};

export default SelectDateTechnicianModal;
