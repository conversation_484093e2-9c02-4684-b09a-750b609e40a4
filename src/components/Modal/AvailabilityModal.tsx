import moment from "moment";
import { SetStateAction, useMemo, useState } from "react";
import { Availability } from "../../@redux/modules/techBooking";
import ResizingFullModal from "./ResizingFullModal";
import GrayParagraph from "../atom/Texts/GrayParagraph";
import ModalTitle from "../atom/Texts/ModalTitle/index";
import Text14 from "../atom/Texts/base/Text14";
import TimeSelect from "../atom/TimeSelect";
import ModalConfirmCancelBar from "../molecule/ModalConfirmCancelBar";
import { MODAL_PADDING } from "../../theme";
import { Color } from "../../types/Color";
import { CustomModalProps } from "../../types/interfaces";

interface AvailabilityModalProps extends CustomModalProps {
  isLoading: boolean;
  availabilityInputData: {
    dayName: string;
    startTimeInput: string;
    endTimeInput: string;
    slots: Availability["slots"];
  }[];
  setAvailabilityInputData: React.Dispatch<
    SetStateAction<
      {
        dayName: string;
        startTimeInput: string;
        endTimeInput: string;
        slots: Availability["slots"];
      }[]
    >
  >;
  handleOnClickAdd: (index: number) => void;
  handleOnClickUpdate: () => void;
  handleOnClickDelete: (dayIndex: number, slotIndex: number) => void;
  getIsAddedTime: (value: string) => boolean;
}

const AvailabilityModal = (props: AvailabilityModalProps) => {
  const [hoveredDayIndex, setHoveredDayIndex] = useState<number>(-1);
  const [hoveredSlotIndex, setHoveredSlotIndex] = useState<number>(-1);

  const WEB_MODAL_WIDTH = 600;
  const WEB_MODAL_HEIGHT = 700;

  const startTimeOptionValues = useMemo(() => {
    let result = [];
    let hour = 8;
    let minute = 0;

    while (!(hour === 21 && minute === 0)) {
      const time = `${hour < 10 ? "0" + hour : hour}:${minute == 0 ? "00" : minute}`;
      result.push(time);
      minute += 15;
      if (minute === 60) {
        hour++;
        minute = 0;
      }
    }

    return result;
  }, []);

  const endTimeOptionValues = useMemo(() => {
    let result = [];
    let hour = 8;
    let minute = 0;

    while (!(hour === 21 && minute === 30)) {
      const time = `${hour < 10 ? "0" + hour : hour}:${minute == 0 ? "00" : minute}`;
      result.push(time);
      minute += 15;
      if (minute === 60) {
        hour++;
        minute = 0;
      }
    }

    return result;
  }, []);

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col bg-white justify-between h-full md:rounded-lg">
        <ModalTitle>Availability Setting</ModalTitle>
        <GrayParagraph
          style={{
            marginLeft: MODAL_PADDING,
            marginBottom: 20,
          }}
        >
          All times are based on EST (Eastern Standard Time).
        </GrayParagraph>
        <div className="flex flex-col flex-1 h-[calc((100vh-133px)/2)] overflow-y-scroll bg-[rgba(0,0,0,0.01)]">
          {props.availabilityInputData.map((item, index) => (
            <div
              key={index}
              className="flex flex-col border-y bg-white"
              style={{
                marginBottom: 6,
                boxShadow: "0px 0px 10px rgba(0,0,0,0.07)",
              }}
            >
              <div className="flex items-center px-8 py-2">
                <Text14
                  style={{
                    fontWeight: 500,
                    width: 110,
                  }}
                >
                  {item.dayName}
                </Text14>
                <TimeSelect
                  value={props.availabilityInputData[index].startTimeInput}
                  onChange={(val) => {
                    props.setAvailabilityInputData((prev) => {
                      if (prev[index].endTimeInput !== undefined) {
                        if (prev[index].endTimeInput! <= val) {
                          return prev;
                        }
                      }
                      prev[index].startTimeInput = val;
                      return [...prev];
                    });
                  }}
                  style={{ width: 140, marginRight: 20 }}
                  values={startTimeOptionValues.filter((option) => option < item.endTimeInput)}
                  placeHolderName="Start Time"
                />
                <TimeSelect
                  value={props.availabilityInputData[index].endTimeInput}
                  onChange={(val) => {
                    props.setAvailabilityInputData((prev) => {
                      if (prev[index].startTimeInput !== undefined) {
                        if (prev[index].startTimeInput! >= val) {
                          return prev;
                        }
                      }
                      prev[index].endTimeInput = val;
                      return [...prev];
                    });
                  }}
                  style={{ width: 140, marginRight: 40 }}
                  values={endTimeOptionValues.filter((option) => option > item.startTimeInput)}
                  placeHolderName="End Time"
                />
                <button
                  className="text-sm font-medium text-[rgba(255,255,255,0.9)] px-3"
                  style={{
                    backgroundColor:
                      props.availabilityInputData[index].startTimeInput === "none" ||
                      props.availabilityInputData[index].endTimeInput === "none"
                        ? Color.BLACK_02
                        : Color.ACCENT,
                    paddingTop: 7,
                    paddingBottom: 5,
                  }}
                  disabled={
                    props.availabilityInputData[index].startTimeInput === "none" ||
                    props.availabilityInputData[index].endTimeInput === "none"
                  }
                  onClick={() => props.handleOnClickAdd(index)}
                >
                  Add
                </button>
              </div>
              {item.slots.length !== 0 ? (
                <div className="flex flex-col bg-[#f6faf8] divide-y border-t">
                  {item.slots.map((slot, slotIndex) => (
                    <div
                      className="flex items-center px-8"
                      style={{
                        paddingTop: 2,
                        paddingBottom: 2,
                      }}
                      onMouseOver={() => {
                        setHoveredDayIndex(index);
                        setHoveredSlotIndex(slotIndex);
                      }}
                      onMouseOut={() => {
                        setHoveredDayIndex(-1);
                        setHoveredSlotIndex(-1);
                      }}
                    >
                      <div
                        style={{
                          width: 110,
                        }}
                      />
                      <span
                        className="mr-5 px-3 py-2 text-sm"
                        style={{
                          width: 140,
                        }}
                      >
                        {!props.getIsAddedTime(slot.startTime)
                          ? moment(slot.startTime).format("HH:mm")
                          : slot.startTime}
                      </span>
                      <span
                        className="mr-10 px-3 py-2 text-sm"
                        style={{
                          width: 140,
                        }}
                      >
                        {!props.getIsAddedTime(slot.endTime)
                          ? moment(slot.endTime).format("HH:mm")
                          : slot.endTime}
                      </span>
                      {index === hoveredDayIndex && slotIndex === hoveredSlotIndex ? (
                        <button
                          className="text-sm font-medium text-[rgba(0,0,0,0.9)]"
                          onClick={() => {
                            props.handleOnClickDelete(index, slotIndex);
                          }}
                        >
                          Delete
                        </button>
                      ) : undefined}
                    </div>
                  ))}
                </div>
              ) : undefined}
            </div>
          ))}
        </div>
        <ModalConfirmCancelBar
          confirmButtonTitle="Update"
          onClickConfirmButton={props.handleOnClickUpdate}
          cancelButtonTitle="Cancel"
          onClickCancelButton={props.onOff}
          isLoading={props.isLoading}
          isConfirmDisabled={false}
        />
      </div>
    </ResizingFullModal>
  );
};

export default AvailabilityModal;
