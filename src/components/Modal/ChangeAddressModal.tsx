import { useEffect } from "react";
import { useAddressModal } from "../../hooks/useAddressModal";
import AddressAutocomplete from "../../pageComponents/booking_address/AddressAutocomplete";
import ClassifyContent from "../../pageComponents/booking_address/ClassifyContent";
import ContentLabel from "../../pageComponents/booking_address/ContentLabel";
import FormOfContent from "../../pageComponents/booking_address/FormOfContent";
import PhoneSelect from "../../pageComponents/booking_address/PhoneSelect";
import ErrorAlertModal from "../../pageComponents/booking_date/ErrorAlertModal";
import { AddressService, NewAddress } from "../../services/AddressService";
import { Color } from "../../types/Color";
import { PhoneNumber } from "../../types/PhoneNumber";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import CloseButton from "../atom/Buttons/CloseButton";
import ResizingFullModal from "./ResizingFullModal";

interface ChangeAddressModalProps {
  isOn: boolean;
  onOff: () => void;
  selectedAddress: NewAddress | null;
  onUpdate: () => void;
}

const ChangeAddressModal = (props: ChangeAddressModalProps) => {
  const addressModalHook = useAddressModal();

  const WEB_MODAL_WIDTH = 672;
  const WEB_MODAL_HEIGHT = 641;

  useEffect(() => {
    if (props.selectedAddress !== null && props.selectedAddress !== undefined) {
      addressModalHook.setStreet1(props.selectedAddress.street1);
      addressModalHook.setStreet2(props.selectedAddress.street2 || "");
      addressModalHook.setCity(props.selectedAddress.city);
      addressModalHook.setProvince(props.selectedAddress.province);
      addressModalHook.setPostal(props.selectedAddress.postal);
      addressModalHook.setCountry(props.selectedAddress.country);
      addressModalHook.setName(props.selectedAddress.customerName);

      const existingPhoneNumber = PhoneNumber.getPhoneNumberFromAPI(
        props.selectedAddress.customerPhone
      );

      addressModalHook.setPhoneRegion(existingPhoneNumber.getPhoneRegion());
      addressModalHook.setPhone(existingPhoneNumber.getFormattedPhoneNumber());
    }
  }, [props.selectedAddress, props.isOn]);

  // useEffect(() => {
  //   if (props.selectedPhone !== null) {
  //     setPhone(props.selectedPhone);
  //   }
  // }, [props.selectedPhone, props.isOn]);

  // useEffect(() => {
  //   if (props.selectedName !== null) {
  //     setName(props.selectedName);
  //   }
  // }, [props.selectedName, props.isOn]);

  async function handleOnClickConfirm() {
    //TO DO
    try {
      if (!addressModalHook.isValidatePhone()) {
        return;
      }
      // if (!addressModalHook.isValidatePostalCode()) {
      //   return;
      // }
      addressModalHook.setIsLoading(true);
      if (props.selectedAddress) {
        await AddressService.update(
          props.selectedAddress?.addressId,
          addressModalHook.street1,
          addressModalHook.street2,
          addressModalHook.city,
          addressModalHook.province,
          addressModalHook.postal,
          addressModalHook.country,
          addressModalHook.name,
          new PhoneNumber(
            addressModalHook.phoneRegion,
            addressModalHook.phone
          ).getFullPhoneNumberForAPI()
        );
        addressModalHook.resetVal();
        props.onOff();
        props.onUpdate();
      }
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.message) {
        if (error.response.data.message.includes("Missing address component types")) {
          addressModalHook.setErrorMessage({
            title: "Invalid Address",
            description: `Missing ${error.response.data.message
              .split("types: ")[1]
              .split(",")
              .map((type: any) => type.trim())
              .join(", ")}.
              Please enter all required values and try again!`,
          });
        } else if (
          error.response.data.message.includes("Missing the following unconfirmed component types")
        ) {
          addressModalHook.setErrorMessage({
            title: "Invalid Address",
            description: `Not valid ${error.response.data.message
              .split("types: ")[1]
              .split(",")
              .map((type: any) => type.trim())
              .join(", ")}.
              Please enter a valid value and try again!`,
          });
        }
      }
      console.log(error);
    } finally {
      addressModalHook.setIsLoading(false);
    }
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <ErrorAlertModal
        isOn={addressModalHook.errorMessage !== null}
        onOff={() => addressModalHook.setErrorMessage(null)}
        title={addressModalHook.errorMessage ? addressModalHook.errorMessage.title : ""}
        description={addressModalHook.errorMessage ? addressModalHook.errorMessage.description : ""}
      />
      <div className="flex flex-col bg-white md:rounded-lg w-full h-full">
        <div className="flex flex-col flex-1 h-full items-stretch">
          <HeadLine title="Change Address" subTitle="" onClickCloseButton={props.onOff} />
          <Content>
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0">
              <FormOfContent>
                <ContentLabel isContentEssential>Customer Name</ContentLabel>
                <ClassifyContent
                  value={addressModalHook.name}
                  options={[]}
                  placeholder="Input your name here..."
                  type="INPUT"
                  onChange={(target) => addressModalHook.setName(target)}
                />
              </FormOfContent>
              <div style={{ width: 16 }} />
              <FormOfContent>
                <ContentLabel isContentEssential>Phone Number</ContentLabel>
                <div className="flex flex-col space-y-3 xs:flex-row xs:space-y-0">
                  <PhoneSelect
                    imageSrc={require("../../../public/flag_US.png")}
                    regionCode="1"
                    value={addressModalHook.phoneRegion}
                    style={{
                      flex: "1 1 0%",
                      marginRight: 12,
                    }}
                    onChange={(target) => addressModalHook.setPhoneRegion(target)}
                  />
                  <ClassifyContent
                    value={addressModalHook.phone}
                    options={[]}
                    placeholder="Your phone number..."
                    type="INPUT"
                    style={{
                      flex: "1 1 0%",
                    }}
                    onChange={(target) => addressModalHook.setFormattedPhoneNumber(target)}
                  />
                </div>
              </FormOfContent>
            </div>
            <div>
              <FormOfContent style={{ position: "relative" }}>
                <ContentLabel isContentEssential>Street Address</ContentLabel>
                <AddressAutocomplete
                  isOn={props.isOn}
                  onValueChange={(value) => addressModalHook.setStreet1(value)}
                  initialValue={addressModalHook.street1}
                  populateForms={addressModalHook.populateForms}
                  disabled={
                    addressModalHook.isAutoCompleteDisabled ||
                    addressModalHook.street1 === props.selectedAddress?.street1
                  }
                  onClick={() => {
                    addressModalHook.populateFormsRef.current = false;
                    addressModalHook.resetPopulateForms();
                  }}
                />
              </FormOfContent>
            </div>
            <div>
              <FormOfContent>
                <ContentLabel>Apartment, Suite, etc. (optional)</ContentLabel>
                <ClassifyContent
                  value={addressModalHook.street2}
                  options={[]}
                  placeholder="Input your street address here..."
                  type="INPUT"
                  onChange={(target) => addressModalHook.setStreet2(target)}
                />
              </FormOfContent>
            </div>
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0">
              <FormOfContent>
                <ContentLabel isContentEssential>City</ContentLabel>
                <ClassifyContent
                  placeholder="Auto complete"
                  type="INPUT"
                  onChange={(target) => addressModalHook.setCity(target)}
                  value={addressModalHook.city}
                  disabled={true}
                  options={[]}
                />
              </FormOfContent>
              <div style={{ width: 24 }} />
              <FormOfContent>
                <ContentLabel isContentEssential>Province</ContentLabel>
                <ClassifyContent
                  value={addressModalHook.province}
                  options={[]}
                  placeholder="Auto complete"
                  type="INPUT"
                  onChange={(target) => addressModalHook.setProvince(target)}
                  disabled={true}
                />
              </FormOfContent>
            </div>
            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0">
              <FormOfContent>
                <ContentLabel isContentEssential>Country</ContentLabel>
                <ClassifyContent
                  value={addressModalHook.country}
                  options={[]}
                  placeholder="Auto complete"
                  type="INPUT"
                  onChange={(target) => addressModalHook.setCountry(target)}
                  disabled={true}
                />
              </FormOfContent>
              <div style={{ width: 24 }} />
              <FormOfContent>
                <ContentLabel isContentEssential>Postal Code</ContentLabel>
                <ClassifyContent
                  value={addressModalHook.postal}
                  options={[]}
                  placeholder="Auto complete"
                  type="INPUT"
                  onChange={(target) => {
                    addressModalHook.onChangePostalCodeText(target);
                  }}
                  disabled={true}
                />
              </FormOfContent>
            </div>
          </Content>
          <EndLine>
            <button
              className="px-3"
              style={{
                fontWeight: 600,
                fontSize: 14,
                lineHeight: 1,
                letterSpacing: "0.02em",
                color: Color.COD_GRAY,
              }}
              onClick={props.onOff}
            >
              Cancel
            </button>
            <BackgroundSmallButton
              title="Confirm"
              onClick={handleOnClickConfirm}
              isLoading={addressModalHook.isLoading}
              disabled={!addressModalHook.isContinuable}
              backgroundColor="BLACK_PEARL"
              style={{
                width: 80,
                height: 48,
                fontWeight: 500,
                fontSize: 14,
                lineHeight: 1,
                letterSpacing: "0.02em",
                borderRadius: 8,
                paddingLeft: 12,
                paddingRight: 12,
                paddingTop: 14.5,
                paddingBottom: 14.5,
              }}
            />
          </EndLine>
        </div>
      </div>
    </ResizingFullModal>
  );
};

const Title = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        color: Color.COD_GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const SubTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 400,
        fontSize: 16,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE_06,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const HeadLine = (props: { onClickCloseButton: () => void; title: string; subTitle: string }) => {
  return (
    <div
      className="flex justify-between items-center px-6 py-4"
      style={{
        borderBottom: "0.5px solid #E7E6EB",
      }}
    >
      <div className="flex flex-col gap-1">
        <Title>{props.title}</Title>
        <SubTitle>{props.subTitle}</SubTitle>
      </div>
      <CloseButton onClick={props.onClickCloseButton} />
    </div>
  );
};

const EndLine = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-row justify-between px-6 py-4"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const Content = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-col items-stretch px-6 py-4 gap-6 h-full overflow-y-scroll"
      style={props.style}
    >
      {props.children}
    </div>
  );
};

export default ChangeAddressModal;
