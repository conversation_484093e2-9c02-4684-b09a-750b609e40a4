import { useContext } from "react";
import Modal from ".";
import { globalContext } from "../../contexts/GlobalContext";

interface ResizingModalProps {
  children?: React.ReactNode;
  isOn: boolean;
  onOff: () => void;
  disableBackground?: boolean;
  modalSize: {
    mobile: {
      width: number;
      height: number;
    };
    web: {
      width: number;
      height: number;
    };
  };
}

const ResizingModal = (props: ResizingModalProps) => {
  const context = useContext(globalContext);

  return (
    <Modal isOn={props.isOn} onOff={props.onOff} disableBackground={props.disableBackground}>
      <div
        className="flex flex-col -mt-[0.5px] -mx-[0.5px] bg-white justify-between rounded-lg xl:m-0 min-w-[320px] transition-all"
        style={{
          width: context?.getResponsiveModalWidthByDeviceSize(
            props.modalSize.web.width,
            props.modalSize.mobile.width
          ),
          height: context?.getResponsiveModalHeightByDeviceSize(
            props.modalSize.web.height,
            props.modalSize.mobile.height
          ),
        }}
      >
        {props.children}
      </div>
    </Modal>
  );
};

export default ResizingModal;
