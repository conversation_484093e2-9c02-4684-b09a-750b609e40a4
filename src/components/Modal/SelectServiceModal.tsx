import Image from "next/image";
import { useContext } from "react";
import { carServiceContext } from "../../contexts/CarServiceContext";
import { globalContext } from "../../contexts/GlobalContext";
import SelectServiceBlock from "../../pageComponents/index/SelectServiceBlock";
import { Color } from "../../types/Color";
import { MD, XXS } from "../../values";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import CloseButton from "../atom/Buttons/CloseButton";
import WhiteButton from "../atom/Buttons/WhiteButton";
import ResizingFullModal from "./ResizingFullModal";

const SelectServiceModal = () => {
  const context = useContext(carServiceContext);
  const services = context!.remoteServices;
  const isOn = context!.isSelectServiceModalOn;

  const deviceWidth = useContext(globalContext)?.dimensionWidth;
  const WEB_MODAL_WIDTH = 650;
  const WEB_MODAL_HEIGHT = 734;

  return (
    <ResizingFullModal
      isOn={isOn}
      onOff={() => context?.setIsSelectServiceModalOn(false)}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col bg-white rounded-lg w-full h-full">
        <div className="flex flex-col flex-1 h-full items-stretch">
          <HeadLine
            title="Select Services"
            subTitle=""
            onClickCloseButton={() => context?.setIsSelectServiceModalOn(false)}
          />
          <Content>
            {services !== undefined && services.length !== 0 ? (
              <>
                <span
                  className=""
                  style={{
                    color: Color.COD_GRAY,
                    fontWeight: 600,
                    fontSize: 20,
                    lineHeight: 1,
                    marginBottom: 16,
                  }}
                >
                  {services[0].categoryName}
                </span>
                <div className="grid grid-cols-1 xxs:max-md:grid-cols-2 md:grid-cols-3 grid-flow-row gap-4">
                  {services.map((service, index) => (
                    <SelectServiceBlock
                      service={service}
                      key={index.toString()}
                      onClick={() => context?.handleOnClickServiceBlock(service)}
                      isSelected={context!.getIsServiceInArray(service)}
                      isDisabled={service.needContact === 1}
                      isRightEnd={
                        deviceWidth && deviceWidth > MD
                          ? index % 3 === 2
                          : deviceWidth && deviceWidth >= XXS
                          ? index % 2 === 1
                          : index % 1 === 0
                      }
                      isFirstRow={
                        deviceWidth && deviceWidth > MD
                          ? index <= 2
                          : deviceWidth && deviceWidth >= XXS
                          ? index <= 1
                          : index <= 0
                      }
                    />
                  ))}
                </div>
              </>
            ) : null}
          </Content>
          {/* Web */}
          <EndLine>
            <div className="flex flex-col items-center justify-center">
              <div>
                <Image src={require("../../../public/progress_bar_select_service.png")} />
              </div>
            </div>
            <div className="flex flex-row">
              <WhiteButton
                title="Go Back"
                onClick={context!.handleOnClickGoBackToSelectCar}
                style={{
                  width: 140,
                  borderRadius: 8,
                  paddingTop: 10,
                  paddingBottom: 10,
                  paddingLeft: 35,
                  paddingRight: 35,
                }}
                titleStyle={{
                  lineHeight: "143%",
                }}
              />
              <div style={{ marginRight: 16 }} />
              <BackgroundSmallButton
                title={
                  context!.selectedServices.length > 1
                    ? `Add ${context!.selectedServices.length} Services`
                    : `Add ${context!.selectedServices.length} Service`
                }
                onClick={() => {
                  if (context) {
                    context.handleOnClickAddService(context!.selectedServices);
                    context.setIsSelectServiceModalOn(false);
                    context.setSelectedServices([]);
                  }
                }}
                backgroundColor="BLACK_PEARL"
                style={{
                  width: 140,
                  borderRadius: 8,
                  paddingTop: 10,
                  paddingBottom: 10,
                  paddingLeft: 17.5,
                  paddingRight: 17.5,
                }}
                disabled={context!.selectedServices.length <= 0}
              />
            </div>
          </EndLine>

          {/* Mobile */}
          <EndLineMobile>
            <div className="flex flex-col items-center justify-center mb-3">
              <div className="flex w-14 h-2">
                <Image src={require("../../../public/progress_bar_select_service_mobile.png")} />
              </div>
            </div>
            <div className="flex flex-row">
              <div className="flex-1">
                <WhiteButton
                  title="Go Back"
                  onClick={context!.handleOnClickGoBackToSelectCar}
                  style={{
                    width: "100%",
                    borderRadius: 8,
                    paddingTop: 10,
                    paddingBottom: 10,
                    paddingLeft: 35,
                    paddingRight: 35,
                    justifyContent: "center",
                  }}
                  titleStyle={{
                    lineHeight: "143%",
                  }}
                />
              </div>

              <div style={{ marginRight: 16 }} />
              <div className="flex-1">
                <BackgroundSmallButton
                  title={
                    context!.selectedServices.length > 1
                      ? `Add ${context!.selectedServices.length} Services`
                      : `Add ${context!.selectedServices.length} Service`
                  }
                  onClick={() => {
                    if (context) {
                      context.handleOnClickAddService(context!.selectedServices);
                      context.setIsSelectServiceModalOn(false);
                      context.setSelectedServices([]);
                    }
                  }}
                  backgroundColor="BLACK_PEARL"
                  style={{
                    width: "100%",
                    borderRadius: 8,
                    paddingTop: 10,
                    paddingBottom: 10,
                    paddingLeft: 17.5,
                    paddingRight: 17.5,
                  }}
                  disabled={context!.selectedServices.length <= 0}
                />
              </div>
            </div>
          </EndLineMobile>
        </div>
      </div>
    </ResizingFullModal>
  );
};

const Title = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        color: Color.COD_GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const SubTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 400,
        fontSize: 16,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE_06,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const HeadLine = (props: { onClickCloseButton: () => void; title: string; subTitle: string }) => {
  return (
    <div
      className="flex justify-between items-center p-4 md:px-6 md:py-4"
      style={{
        borderBottom: "0.5px solid #E7E6EB",
      }}
    >
      <div className="flex flex-col gap-1">
        <Title>{props.title}</Title>
        <SubTitle>{props.subTitle}</SubTitle>
      </div>
      <CloseButton onClick={props.onClickCloseButton} />
    </div>
  );
};

const EndLine = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="hidden md:flex flex-row justify-between px-6 py-4"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const EndLineMobile = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex md:hidden flex-col justify-between p-4 pt-3"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

export const Content = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex flex-col p-4 md:px-6 md:py-4 h-full overflow-x-hidden overflow-y-scroll"
      style={props.style}
    >
      {props.children}
    </div>
  );
};

export default SelectServiceModal;
