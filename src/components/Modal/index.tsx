import React from "react";

const Modal = (props: {
  children?: React.ReactNode;
  isOn: boolean;
  onOff: () => void;
  disableBackground?: boolean;
}) => {
  return (
    <div
      className="absolute w-full h-full top-0 left-0 z-10"
      style={{ display: props.isOn ? undefined : "none" }}
    >
      <div
        className={`fixed w-full h-full ${
          props.disableBackground ? "bg-transparent" : "bg-[rgba(0,0,0,0.4)]"
        }`}
        onClick={props.onOff}
      ></div>
      <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
        {props.children}
      </div>
    </div>
  );
};

export default Modal;
