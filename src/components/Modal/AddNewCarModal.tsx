import Image from "next/image";
import { useContext, useState } from "react";
import ResizingFullModal from "./ResizingFullModal";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import CloseButton from "../atom/Buttons/CloseButton";
import SizeableImage from "../atom/SizeableImage";
import Spinner from "../atom/Spinner";
import VehicleAddonEditModal from "../organism/VehicleAddonEditModal";
import { carServiceContext } from "../../contexts/CarServiceContext";
import { apiDeleteCar } from "../../functions/api/car";
import { Color } from "../../types/Color";
import CarBlock from "../../pageComponents/index/CarBlock";

const AddNewCarModal = () => {
  const [isNewPage, setIsNewPage] = useState<boolean>(false);
  const context = useContext(carServiceContext);
  const isOn = context!.isAddCarModalOn;
  const ownedCarArray = context!.notSelectedOwnedCars;

  const WEB_SELECT_CAR_MODAL_WIDTH = 650;
  const WEB_SELECT_CAR_MODAL_HEIGHT = 734;
  const WEB_ADD_NEW_VEHICLES_MODAL_WIDTH = 672;
  const WEB_ADD_NEW_VEHICLES_MODAL_HEIGHT = 509;

  return (
    <ResizingFullModal
      isOn={isOn}
      onOff={() => context?.setIsAddCarModalOn(false)}
      modalSize={{
        web: {
          width: isNewPage ? WEB_ADD_NEW_VEHICLES_MODAL_WIDTH : WEB_SELECT_CAR_MODAL_WIDTH,
          height: isNewPage ? WEB_ADD_NEW_VEHICLES_MODAL_HEIGHT : WEB_SELECT_CAR_MODAL_HEIGHT,
        },
      }}
    >
      {!isNewPage ? (
        <div className="flex flex-col bg-white md:rounded-lg w-full h-full">
          <div className="flex flex-col flex-1 h-full items-stretch">
            <HeadLine
              title="Select Car"
              subTitle=""
              onClickCloseButton={() => context?.setIsAddCarModalOn(false)}
            />
            <Content>
              {!context?.isLoadingFetchCar ? (
                <>
                  {ownedCarArray !== undefined && ownedCarArray.length !== 0 ? (
                    <div className="grid grid-cols-1 xxs:max-md:grid-cols-2 md:grid-cols-3 grid-flow-row gap-4 ">
                      {ownedCarArray.map((item, index) => (
                        <CarBlock
                          car={item}
                          key={index.toString()}
                          onClick={() => {
                            context?.handleOnClickOwnedCarOnAddNewCarModal(item);
                          }}
                          onClickDelete={async () => {
                            if (context) {
                              await apiDeleteCar(item.id);
                              context.fetchCar();
                            }
                          }}
                          isSelected={context?.selectedCar?.id === item.id}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full">
                      <div
                        className="flex flex-col items-center justify-center rounded-full w-[158px] h-[158px]"
                        style={{
                          border: "24px solid rgba(233, 239, 255, 0.4)",
                          marginBottom: 16,
                        }}
                      >
                        <div
                          className="flex flex-col items-center justify-center rounded-full w-[110px] h-[110px]"
                          style={{
                            backgroundColor: Color.ZUMTHOR,
                          }}
                        >
                          <SizeableImage
                            src={require("../../../public/reading_glasses.png")}
                            size={64}
                          />
                        </div>
                      </div>
                      <span
                        className="items-start text-center w-[324px]"
                        style={{
                          fontWeight: 500,
                          fontSize: 16,
                          lineHeight: "150%",
                          letterSpacing: "-0.016em",
                          color: Color.EBONY_06,
                        }}
                      >
                        No vehicles found. Click{" "}
                        <span style={{ fontWeight: 700 }}>'Add New Vehicle'</span> to add your
                        vehicle into the service list
                      </span>
                      <div className="flex flex-col" style={{ marginTop: 20, marginBottom: 16 }}>
                        <AddNewVehiclesButton
                          src={require("../../../public/plus_codgrey.png")}
                          imageSize={24}
                          onClick={() => setIsNewPage(true)}
                          style={{
                            width: 200,
                            justifyContent: "center",
                            paddingTop: 0,
                            paddingLeft: 0,
                            paddingRight: 0,
                            paddingBottom: 0,
                          }}
                          imageStyle={{
                            paddingTop: 0,
                            paddingBottom: 0,
                          }}
                          textStyle={{
                            fontWeight: 600,
                            fontSize: 18,
                            lineHeight: "133%",
                            color: Color.COD_GRAY,
                          }}
                        />
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="w-full h-full flex flex-col justify-center items-center bg-white">
                  <div className="flex flex-col items-center">
                    <Spinner />
                  </div>
                </div>
              )}
            </Content>
            {ownedCarArray !== undefined && ownedCarArray.length !== 0 ? (
              <AddNewVehiclesButton
                src={require("../../../public/plus_indigo_blue.png")}
                imageSize={20}
                onClick={() => setIsNewPage(true)}
              />
            ) : null}
            {/* Web */}
            <EndLine>
              <div className="flex flex-col items-center justify-center">
                <div>
                  <Image src={require("../../../public/progress_bar_select_car.png")} />
                </div>
              </div>
              <BackgroundSmallButton
                title="Continue"
                onClick={context!.handleOnClickContinueOnAddNewCarModal}
                isLoading={false}
                disabled={!context?.isAddCarContinuable || context?.isLoadingFetchCar}
                backgroundColor="BLACK_PEARL"
                style={{
                  width: 100,
                  height: 40,
                  fontWeight: 500,
                  fontSize: 14,
                  lineHeight: "143%",
                  letterSpacing: "0.02em",
                  borderRadius: 8,
                  paddingLeft: 19,
                  paddingRight: 19,
                  paddingTop: 10,
                  paddingBottom: 10,
                }}
              />
            </EndLine>
            {/* Mobile */}
            <EndLineMobile>
              <div className="flex flex-col items-center justify-center mb-3">
                <div className="flex w-14 h-2">
                  <Image src={require("../../../public/progress_bar_select_car_mobile.png")} />
                </div>
              </div>
              <BackgroundSmallButton
                title="Continue"
                onClick={context!.handleOnClickContinueOnAddNewCarModal}
                isLoading={false}
                disabled={!context?.isAddCarContinuable || context?.isLoadingFetchCar}
                backgroundColor="BLACK_PEARL"
                style={{
                  width: "100%",
                  height: 40,
                  fontWeight: 500,
                  fontSize: 14,
                  lineHeight: "143%",
                  letterSpacing: "0.02em",
                  borderRadius: 8,
                  paddingLeft: 19,
                  paddingRight: 19,
                  paddingTop: 10,
                  paddingBottom: 10,
                }}
              />
            </EndLineMobile>
          </div>
        </div>
      ) : (
        <VehicleAddonEditModal
          isOn={isNewPage}
          onOff={() => setIsNewPage(false)}
          disableBackground
          isEdit={false}
          onCompletion={() => {
            if (context) {
              context.fetchCar();
              setIsNewPage(false);
            }
          }}
        />
      )}
    </ResizingFullModal>
  );
};

const Title = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        color: Color.COD_GRAY,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const SubTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-stretch"
      style={{
        fontWeight: 400,
        fontSize: 16,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE_06,
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const HeadLine = (props: { onClickCloseButton: () => void; title: string; subTitle: string }) => {
  return (
    <div
      className="flex justify-between items-center p-4 md:px-6 md:py-4"
      style={{
        borderBottom: "0.5px solid #E7E6EB",
      }}
    >
      <div className="flex flex-col gap-1">
        <Title>{props.title}</Title>
        <SubTitle>{props.subTitle}</SubTitle>
      </div>
      <CloseButton onClick={props.onClickCloseButton} />
    </div>
  );
};

const EndLine = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="hidden md:flex flex-row justify-between px-6 py-4"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const EndLineMobile = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div
      className="flex md:hidden flex-col justify-between p-4 pt-3"
      style={{
        borderTop: "0.5px solid #E7E6EB",
        ...props.style,
      }}
    >
      {props.children}
    </div>
  );
};

const Content = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <div className="h-full overflow-y-scroll p-4 md:px-6 md:py-4 " style={props.style}>
      {props.children}
    </div>
  );
};

const AddNewVehiclesButton = (props: {
  src: any;
  imageSize: number;
  onClick: () => void;
  style?: React.CSSProperties;
  imageStyle?: React.CSSProperties;
  textStyle?: React.CSSProperties;
}) => {
  return (
    <button
      className="flex justify-start self-stretch px-6 py-4"
      onClick={props.onClick}
      style={{
        width: 220,
        ...props.style,
      }}
    >
      <div className="py-[2px]" style={{ ...props.imageStyle }}>
        <SizeableImage src={props.src} size={props.imageSize} />
      </div>
      <span
        style={{
          fontWeight: 600,
          fontSize: 16,
          lineHeight: "150%",
          color: Color.INDIGO_BLUE,
          marginLeft: 12,
          ...props.textStyle,
        }}
      >
        Add New Vehicles
      </span>
    </button>
  );
};

export default AddNewCarModal;
