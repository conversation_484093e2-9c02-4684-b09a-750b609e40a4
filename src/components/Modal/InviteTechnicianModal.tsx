import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../@redux";
import { thunkUpdateUserNamePhone, thunkUpdateUserProfile } from "../../@redux/modules/auth/thunks";
import PhoneSelect from "../../pageComponents/booking_address/PhoneSelect";
import { Color } from "../../types/Color";
import { Input } from "../../types/Input";
import { PhoneNumber } from "../../types/PhoneNumber";
import { CloseIcon } from "../atom/Icons";
import ImageFileInput from "../atom/ImageFileInput";
import TextInput from "../atom/TextInput";
import GrayParagraph from "../atom/Texts/GrayParagraph";
import Text14 from "../atom/Texts/base/Text14";
import Title18 from "../atom/Texts/base/Title18";
import ModalConfirmCancelBar from "../molecule/ModalConfirmCancelBar";
import ResizingFullModal from "./ResizingFullModal";
import { apiCreateTechnician } from "../../functions/api/technician";
import { apiInviteTechnician } from "../../functions/api/user";

interface InviteTechnicianModalProps {
  isOn: boolean;
  onOff: () => void;
  fetchTechnicians: () => void;
}

const InviteTechnicianModal = (props: InviteTechnicianModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [emailInput, setEmailInput] = useState<string>("");
  const [message, setMessage] = useState<{ type: "error" | "success"; text: string } | null>(null);

  const MOBILE_MODAL_WIDTH = 398;
  const WEB_MODAL_WIDTH = 672;
  const MOBILE_MODAL_HEIGHT = 582;
  const WEB_MODAL_HEIGHT = 509;

  async function handleOnClickConfirm() {
    if (!emailInput.trim()) {
      setMessage({
        type: "error",
        text: "Please enter an email address",
      });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const response = await apiInviteTechnician({ email: emailInput });
      console.log("response", response);

      if (response.status === "error") {
        setMessage({
          type: "error",
          text: response.error.message || "Failed to invite technician",
        });
        return;
      }

      // const sub = user?.User?.Attributes?.find((attr) => attr.Name === "sub")?.Value;
      // const email = user?.User?.Attributes?.find((attr) => attr.Name === "email")?.Value;

      if (response.sub && emailInput) {
        await apiCreateTechnician(response.sub, emailInput);
        setMessage({
          type: "success",
          text: "Technician invited successfully",
        });

        props.fetchTechnicians();
        setTimeout(() => {
          props.onOff();
        }, 2000);
      } else {
        console.error("User sub or email is undefined");
        setMessage({
          type: "error",
          text: "Failed to create technician account",
        });
      }
    } catch (error) {
      console.log("error", error);
      setMessage({
        type: "error",
        text: error instanceof Error ? error.message : "Failed to invite technician",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: 300,
        },
      }}
    >
      <div className="flex flex-col flex-1 h-full items-stretch">
        <div className="text-center flex justify-center items-center p-5 border-b-[0.5px] border-gray-200">
          <Title18>Invite Technician</Title18>
          <button className="absolute top-7 right-8" onClick={props.onOff}>
            <CloseIcon />
          </button>
        </div>
        <div className="h-full overflow-y-scroll p-5">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-[6px]">
              {/* <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                First Name
              </Text14>
              <TextInput
                value={firstNameInput}
                onChange={(val) => setFirstNameInput(val)}
                placeholder="First Name"
                styleInput={{
                  fontSize: 16,
                }}
              /> */}
              <label className="text-sm">Email</label>
              <div className="relative flex flex-col items-stretch justify-center">
                <input
                  placeholder="Input your email here..."
                  value={emailInput}
                  onChange={(event) => setEmailInput(event.target.value)}
                  className={`border px-3.5 py-2.5 placeholder-[rgba(0,0,0,0.3)] focus:outline-[#FCA311] rounded-xl drop-shadow-sm ${
                    message?.type === "error" ? "border-red-500" : ""
                  }`}
                />
              </div>
              {message && (
                <div
                  className={`text-sm mt-1 ${
                    message.type === "error" ? "text-red-500" : "text-green-500"
                  }`}
                >
                  {message.text}
                </div>
              )}
            </div>
          </div>
        </div>

        <ModalConfirmCancelBar
          confirmButtonTitle="Confirm"
          onClickConfirmButton={handleOnClickConfirm}
          cancelButtonTitle="Cancel"
          onClickCancelButton={props.onOff}
          isConfirmDisabled={isLoading}
          isLoading={isLoading}
          styleCancelButton={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
          styleConfirmButton={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            width: 79,
            height: 48,
            fontWeight: 500,
          }}
          styleConfirmButtonMobile={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            fontWeight: 500,
          }}
          styleCancelButtonMobile={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
        />
      </div>
    </ResizingFullModal>
  );
};

export default InviteTechnicianModal;
