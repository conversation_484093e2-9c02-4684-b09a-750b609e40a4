import React from "react";
import Modal from ".";
import { MODAL_PADDING } from "../../theme";
import { CloseIcon } from "../atom/Icons";
import GrayParagraph from "../atom/Texts/GrayParagraph";
import Title20 from "../atom/Texts/base/Title20";
import ModalConfirmCancelBar from "../molecule/ModalConfirmCancelBar";
import { Color } from "../../types/Color";
import ResizingFullModal from "./ResizingFullModal";

interface ConfirmCancelModalProps {
  isOn: boolean;
  onOff: () => void;
  title: string;
  description: string;
  content: React.ReactNode;
  webWidth?: number;
  webHeight?: number;
  confirmButtonTitle: string;
  onClickConfirmButton: () => void;
  cancelButtonTitle: string;
  onClickCancelButton: () => void;
  isConfirmDisabled: boolean;
  isLoading: boolean;
  disableBackground?: boolean;
  styleEndLine?: {
    styleCancelButton?: React.CSSProperties;
    styleConfirmButton?: React.CSSProperties;
  };
}

const ConfirmCancelModal = (props: ConfirmCancelModalProps) => {
  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      disableBackground={props.disableBackground}
      modalSize={{
        web: {
          width: props.webWidth ? props.webWidth : 500,
          height: props.webHeight ? props.webHeight : 600,
        },
      }}
    >
      <div
        className="flex flex-col flex-1 -mt-3 h-full items-stretch"
        style={{
          paddingTop: MODAL_PADDING,
          paddingLeft: MODAL_PADDING,
          paddingRight: MODAL_PADDING,
        }}
      >
        <div className="text-center flex justify-between items-center -mx-8 pb-5 border-b-[0.5px] border-gray-200">
          <div className="ml-8"></div>
          <Title20
            style={{
              color: Color.COD_GRAY,
            }}
          >
            {props.title}
          </Title20>
          <button className="grow-0 mr-8" onClick={props.onClickCancelButton}>
            <CloseIcon />
          </button>
        </div>
        <GrayParagraph>{props.description}</GrayParagraph>
        <div className="h-full overflow-y-scroll pt-5 pb-28 xl:pb-6">{props.content}</div>
      </div>
      <ModalConfirmCancelBar
        confirmButtonTitle={props.confirmButtonTitle}
        onClickConfirmButton={props.onClickConfirmButton}
        cancelButtonTitle={props.cancelButtonTitle}
        onClickCancelButton={props.onClickCancelButton}
        isConfirmDisabled={props.isConfirmDisabled}
        isLoading={props.isLoading}
        styleCancelButton={props.styleEndLine?.styleCancelButton}
        styleConfirmButton={props.styleEndLine?.styleConfirmButton}
      />
    </ResizingFullModal>
  );
};

export default ConfirmCancelModal;
