import { useEffect, useMemo, useState } from "react";
import ResizingFullModal from "./ResizingFullModal";
import ModalTitle from "../atom/Texts/ModalTitle";
import ModalConfirmCancelBar from "../molecule/ModalConfirmCancelBar";
import { apiGetGoogleCalendarList, apiSyncCalendar } from "../../functions/api/calendar";

interface CalendarSyncOptionsModalProps {
  isOn: boolean;
  onOff: () => void;
}

const CalendarSyncOptionsModal = (props: CalendarSyncOptionsModalProps) => {
  const [calendarList, setCalendarList] = useState<
    { id: string; backgroundColor: string; summary: string }[]
  >([]);
  const [selectedCalendars, setSelectedCalendars] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const WEB_MODAL_WIDTH = 600;
  const WEB_MODAL_HEIGHT = 700;

  const isConfirmDisabled = useMemo(() => {
    return selectedCalendars.length === 0;
  }, [selectedCalendars]);

  useEffect(() => {
    if (props.isOn) {
      fetchGoogleCalendarList();
    }
  }, [props.isOn]);

  async function fetchGoogleCalendarList() {
    try {
      const result = await apiGetGoogleCalendarList();
      console.log(result);
      setCalendarList(result);
    } catch (error) {
      console.log(error);
    }
  }

  function handleCalendarSelect(id: string) {
    if (selectedCalendars.includes(id)) {
      setSelectedCalendars(selectedCalendars.filter((item) => item !== id));
    } else {
      setSelectedCalendars([...selectedCalendars, id]);
    }
  }

  async function syncCalendar() {
    setIsLoading(true);
    try {
      const result = await apiSyncCalendar(selectedCalendars);
      props.onOff();
      console.log(result);
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col bg-white justify-between h-full md:rounded-lg overflow-scroll">
        <div>
          <ModalTitle>Sync Google Calendar</ModalTitle>
          <span className="p-8">Select the calendars you'd like to sync.</span>
        </div>
        <div>
          {calendarList.map((item) => {
            return (
              <div className="flex items-center justify-between border border-gray-200 m-8 p-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    className="mr-3"
                    onClick={() => handleCalendarSelect(item.id)}
                  />
                  <span>{item.summary}</span>
                </div>
                <div
                  className="w-5 h-5 rounded-full mr-3"
                  style={{
                    backgroundColor: item.backgroundColor,
                  }}
                />
              </div>
            );
          })}
        </div>
        <ModalConfirmCancelBar
          isConfirmDisabled={isConfirmDisabled}
          isLoading={isLoading}
          confirmButtonTitle="Sync"
          cancelButtonTitle="Cancel"
          onClickConfirmButton={syncCalendar}
          onClickCancelButton={() => props.onOff()}
        />
      </div>
    </ResizingFullModal>
  );
};

export default CalendarSyncOptionsModal;
