import Image from "next/image";
import { useEffect, useMemo, useState } from "react";
import { VehicleService } from "../../../services/VehicleService";
import { MODAL_PADDING } from "../../../theme";
import { Car } from "../../../types/Car";
import { Color } from "../../../types/Color";
import ResizingFullModal from "../../Modal/ResizingFullModal";
import { CloseIcon } from "../../atom/Icons";
import ImageFileInput from "../../atom/ImageFileInput";
import TextInput from "../../atom/TextInput";
import GrayParagraph from "../../atom/Texts/GrayParagraph";
import Text14 from "../../atom/Texts/base/Text14";
import Title20 from "../../atom/Texts/base/Title20";
import ModalConfirmCancelBar from "../../molecule/ModalConfirmCancelBar";

interface VehicleAddonEditModalProps {
  isOn: boolean;
  onOff: () => void;
  car?: Car;
  isEdit: boolean;
  disableBackground?: boolean;
  onCompletion: () => void;
}

const VehicleAddonEditModal = (props: VehicleAddonEditModalProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [carMake, setCarMake] = useState<string>("");
  const [carName, setCarName] = useState<string>("");
  const [carYear, setCarYear] = useState<string>("");
  const [carColor, setCarColor] = useState<string>("");
  const [uploadedFileName, setUploadedFileName] = useState<string>();
  const [uploadedImage, setUploadedImage] = useState<string>();

  const MOBILE_MODAL_WIDTH = 398;
  const WEB_MODAL_WIDTH = 672;
  const MOBILE_MODAL_HEIGHT = 670;
  const WEB_MODAL_HEIGHT = 509;

  useEffect(() => {
    if (props.isOn) {
      if (props.isEdit) {
        setCarMake(props.car?.make || "");
        setCarName(props.car?.model || "");
        setCarYear(props.car?.year || "");
        setCarColor(props.car?.color || "");

        if (props.car && props.car.pictureUri) {
          setUploadedImage(props.car?.pictureUri);
        }
      }
    }
  }, [props.isOn]);

  function clearInputs() {
    setCarMake("");
    setCarName("");
    setCarYear("");
    setCarColor("");
    setUploadedFileName("");
    setUploadedImage("");
  }

  function clearModal() {
    clearInputs();
    props.onOff();
  }

  async function handleConfirm() {
    try {
      setIsLoading(true);
      if (props.isEdit) {
        if (props.car) {
          await VehicleService.update(props.car.id, {
            year: carYear,
            make: carMake,
            model: carName,
            color: carColor,
            pictureUri: uploadedImage,
          });
          props.onCompletion();
        }
      } else {
        await VehicleService.save(carYear, carMake, carName, carColor, uploadedImage);
        props.onCompletion();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoading(false);
      clearInputs();
    }
  }

  const isConfirmable = useMemo(() => {
    return carName !== "" && carYear !== "" && carMake !== "" && carColor !== "";
  }, [carName, carYear, carMake, carColor]);

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={props.onOff}
      disableBackground={props.disableBackground}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col flex-1 items-stretch h-full">
        <div className="text-center flex justify-center p-6 pr-8 border-b-[0.5px] border-gray-200">
          <Title20 style={{ color: Color.COD_GRAY }}>
            {props.isEdit ? "Edit Vehicle" : "Add New Vehicle"}
          </Title20>
          <button className="absolute top-8 right-8" onClick={clearModal}>
            <CloseIcon />
          </button>
        </div>
        <div className="h-full overflow-y-scroll p-6 pr-8">
          {/* <div className="flex flex-col md:flex-row items-start md:items-center gap-6 mb-4">
            <div className="w-20 h-20 rounded-lg overflow-hidden">
              {uploadedImage ? (
                <img className="object-cover w-20 h-20" src={uploadedImage} />
              ) : (
                <Image src={require("../../../../public/upload_photo_placeholder.png")} />
              )}
            </div>
            <div className="flex flex-col gap-3">
              <ImageFileInput
                title="Change Photo"
                uploadedFileName={uploadedFileName}
                setUploadedFileName={(val) => {
                  setUploadedFileName(val);
                }}
                setUploadedImage={(val) => {
                  setUploadedImage(val);
                }}
              />
              <GrayParagraph
                style={{
                  fontWeight: 300,
                  fontSize: 16,
                  color: Color.DOVE_GRAY,
                }}
              >
                PNG or JPG max. 10 MB
              </GrayParagraph>
            </div>
          </div> */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-[6px]">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                Make
              </Text14>
              <TextInput
                value={carMake}
                onChange={(val) => setCarMake(val)}
                placeholder="Ford, Honda, etc."
                styleInput={{
                  fontSize: 16,
                }}
              />
            </div>
            <div className="flex flex-col gap-[6px]">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                Model
              </Text14>
              <TextInput
                value={carName}
                onChange={(val) => setCarName(val)}
                placeholder="Fusion, Civic, etc."
                styleInput={{
                  fontSize: 16,
                }}
              />
            </div>
            <div className="flex flex-col gap-[6px]">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                Model Year
              </Text14>
              <TextInput
                value={carYear}
                onChange={(val) => setCarYear(val)}
                placeholder="2018, 2019, etc."
                styleInput={{
                  fontSize: 16,
                }}
              />
            </div>
            <div className="flex flex-col gap-[6px]">
              <Text14 className="font-medium" style={{ color: Color.BLACK_PEARL }}>
                Colour
              </Text14>
              <TextInput
                value={carColor}
                onChange={(val) => setCarColor(val)}
                placeholder="Blue, Orange, etc."
                styleInput={{
                  fontSize: 16,
                }}
              />
            </div>
          </div>
        </div>
        <ModalConfirmCancelBar
          confirmButtonTitle="Confirm"
          onClickConfirmButton={handleConfirm}
          cancelButtonTitle="Cancel"
          onClickCancelButton={clearModal}
          isConfirmDisabled={!isConfirmable}
          isLoading={isLoading}
          styleCancelButton={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
          styleConfirmButton={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            width: 79,
            height: 48,
            fontWeight: 500,
          }}
          styleConfirmButtonMobile={{
            paddingTop: 14.5,
            paddingBottom: 14.5,
            paddingLeft: 12,
            paddingRight: 12,
            fontWeight: 500,
          }}
          styleCancelButtonMobile={{
            fontWeight: 600,
            color: Color.COD_GRAY,
          }}
        />
      </div>
    </ResizingFullModal>
  );
};

export default VehicleAddonEditModal;
