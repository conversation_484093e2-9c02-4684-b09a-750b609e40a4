import Image from "next/image";
import { AddonCar } from "../../types/AddonCar";
import { BusinessRule } from "../../types/BusinessRule";
import { Color } from "../../types/Color";
import { ServiceCar } from "../../types/ServiceCar";
import { PRICE_PLACEHOLDER, TAX_RATE } from "../../values";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import WhiteButton from "../atom/Buttons/WhiteButton";
import SizeableImage from "../atom/SizeableImage";
import Spinner from "../atom/Spinner";
import { useOrderSummary } from "../../hooks/useOrderSummary";

interface OrderSummaryProps {
  orderSummaryHook: ReturnType<typeof useOrderSummary>;
  serviceCars: ServiceCar[] | undefined;
  isFetchLoading?: boolean;
  style?: React.CSSProperties;
  fourPage?: {
    addonCars: AddonCar[] | undefined;
    onClickPayNow: () => void;
    onClickGoBack: () => void;
    remainingTime: string;
    isLoadingPayment: boolean;
    isDisabledPayment: boolean;
    isDisabledGoback: boolean;
  };
}

const OrderSummary = (props: OrderSummaryProps) => {
  return (
    <div
      className=""
      style={{
        zIndex: 1,
      }}
      ref={props.orderSummaryHook.dynamicHeightRef}
    >
      <div
        className="rounded-lg bg-white p-5 items-stretch min-w-[280px] max-w-[280px]"
        style={{
          border: "0.5px solid #E6E6E8",
          ...props.style,
        }}
      >
        {props.fourPage ? (
          <div className="flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <div className="flex space-x-2 items-center">
                <span
                  className=""
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: Color.INDIGO_BLUE_07,
                  }}
                >
                  Complete your checkout in
                </span>
              </div>
              <div
                className="flex items-center justify-center rounded-full py-1"
                style={{
                  background: Color.CORAL_RED,
                  width: 60,
                }}
              >
                <span
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: Color.WHITE,
                  }}
                >
                  {props.fourPage.remainingTime}
                </span>
              </div>
            </div>
            <div className="border-b mb-3" />
          </div>
        ) : null}

        <OrderSummaryTitle style={{ marginBottom: 16 }}>Order Summary</OrderSummaryTitle>
        <OrderSummarySubTitle style={{ marginBottom: 8 }}>Items</OrderSummarySubTitle>
        {props.isFetchLoading ? (
          <div className="w-full h-full flex flex-col justify-center items-center bg-white">
            <div className="flex flex-col items-center">
              <Spinner />
            </div>
          </div>
        ) : (
          <>
            <ServiceBlock serviceCars={props.serviceCars} />

            {props.fourPage ? (
              <>
                <OrderSummarySubTitle style={{ marginBottom: 8 }}>Add-ons</OrderSummarySubTitle>
                <AddonBlock addonCars={props.fourPage.addonCars} />
              </>
            ) : null}

            {/* <div className="flex flex-row justify-between mb-4">
              <span
                className=""
                style={{
                  color: Color.INDIGO_BLUE_07,
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "135%",
                }}
              >
                Price
              </span>
              <span
                style={{
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "135%",
                  color: Color.INDIGO_BLUE,
                }}
              >
                {props.serviceCars
                  ? `$${Number(props.orderSummaryHook.orderSummaryPrices.servicePrice).toFixed(2)}`
                  : PRICE_PLACEHOLDER}
              </span>
            </div> */}
            {/* <div className="flex flex-row justify-between mb-4">
              <span
                className=""
                style={{
                  color: Color.INDIGO_BLUE_07,
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "135%",
                }}
              >
                Travel Fee Discount
              </span>
              <span
                style={{
                  fontWeight: 400,
                  fontSize: 14,
                  lineHeight: "135%",
                  color: Color.INDIGO_BLUE,
                }}
              >
                {props.orderSummaryHook.orderSummaryPrices.travelFees > 0
                  ? `-$${Number(props.orderSummaryHook.orderSummaryPrices.travelFees).toFixed(2)}`
                  : PRICE_PLACEHOLDER}
              </span>
            </div> */}
            {props.fourPage ? (
              <>
                <div className="flex flex-row justify-between mb-4">
                  <span
                    className=""
                    style={{
                      color: Color.INDIGO_BLUE_07,
                      fontWeight: 400,
                      fontSize: 14,
                      lineHeight: "135%",
                    }}
                  >
                    Add-ons
                  </span>
                  <span
                    style={{
                      fontWeight: 400,
                      fontSize: 14,
                      lineHeight: "135%",
                      color: Color.INDIGO_BLUE,
                    }}
                  >
                    ${Number(props.orderSummaryHook.orderSummaryPrices.addonPrice).toFixed(2)}
                  </span>
                </div>
                <div className="flex flex-row justify-between mb-4">
                  <span
                    className=""
                    style={{
                      color: Color.INDIGO_BLUE_07,
                      fontWeight: 400,
                      fontSize: 14,
                      lineHeight: "135%",
                    }}
                  >
                    Subtotal
                  </span>
                  <span
                    style={{
                      fontWeight: 400,
                      fontSize: 14,
                      lineHeight: "135%",
                      color: Color.INDIGO_BLUE,
                    }}
                  >
                    ${Number(props.orderSummaryHook.orderSummaryPrices.subtotalPrice).toFixed(2)}
                  </span>
                </div>
                <div className="flex flex-row justify-between mb-4">
                  <span
                    className=""
                    style={{
                      color: Color.INDIGO_BLUE_07,
                      fontWeight: 400,
                      fontSize: 14,
                      lineHeight: "135%",
                    }}
                  >
                    Tax
                  </span>
                  <span
                    style={{
                      fontWeight: 400,
                      fontSize: 14,
                      lineHeight: "135%",
                      color: Color.INDIGO_BLUE,
                    }}
                  >
                    ${Number(props.orderSummaryHook.orderSummaryPrices.taxPrice).toFixed(2)}
                  </span>
                </div>
              </>
            ) : null}
            <div className="flex flex-row justify-between">
              <span
                className=""
                style={{
                  color: Color.INDIGO_BLUE,
                  fontWeight: 600,
                  fontSize: 16,
                  lineHeight: "135%",
                }}
              >
                {props.fourPage ? "Total" : "Subtotal"}
              </span>
              <span
                style={{
                  color: Color.INDIGO_BLUE,
                  fontWeight: 600,
                  fontSize: 16,
                  lineHeight: "135%",
                }}
              >
                $
                {Number(
                  props.fourPage
                    ? props.orderSummaryHook.orderSummaryPrices.totalPrice
                    : props.orderSummaryHook.orderSummaryPrices.subtotalPrice,
                ).toFixed(2)}
              </span>
            </div>
            {props.fourPage ? (
              <div className="mt-4">
                <div className="border-b " style={{ marginBottom: 16 }} />
                <EndLine
                  onClickPayNow={props.fourPage.onClickPayNow}
                  onClickGoBack={props.fourPage.onClickGoBack}
                  isLoadingPayment={props.fourPage.isLoadingPayment}
                  isDisabledPayment={props.fourPage.isDisabledPayment}
                  isDisabledGoback={props.fourPage.isDisabledGoback}
                />
              </div>
            ) : null}
          </>
        )}
      </div>
    </div>
  );
};

const OrderSummaryTitle = (props: { children: React.ReactNode; style?: React.CSSProperties }) => {
  return (
    <span
      className="flex items-start"
      style={{
        color: Color.INDIGO_BLUE,
        fontWeight: 600,
        fontSize: 24,
        lineHeight: "135%",
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const OrderSummarySubTitle = (props: {
  children: React.ReactNode;
  style?: React.CSSProperties;
}) => {
  return (
    <span
      className="flex items-start"
      style={{
        color: Color.INDIGO_BLUE_08,
        fontWeight: 600,
        fontSize: 14,
        lineHeight: "135%",
        ...props.style,
      }}
    >
      {props.children}
    </span>
  );
};

const ServiceBlock = (props: { serviceCars: ServiceCar[] | undefined }) => {
  return (
    <div
      className="flex flex-col py-4 mb-4"
      style={{
        border: "0.5px solid #E6E6E8",
        borderLeft: 0,
        borderRight: 0,
      }}
    >
      {props.serviceCars && props.serviceCars.length !== 0 ? (
        <>
          {props.serviceCars.map((serviceCar, index) => (
            <div
              className="flex flex-row justify-between"
              style={{
                marginBottom: index === props.serviceCars!.length - 1 ? 0 : 16,
              }}
            >
              <div className="flex flex-row">
                <span
                  className="flex flex-col mr-3 justify-start mt-1"
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    lineHeight: 1,
                    color: Color.INDIGO_BLUE_06,
                  }}
                >
                  {`${serviceCar.cars.length}x`}
                </span>
                <span
                  className="flex flex-col justify-self-stretch"
                  style={{
                    color: Color.INDIGO_BLUE,
                    fontWeight: 400,
                    fontSize: 14,
                    lineHeight: "150%",
                    maxWidth: 173,
                  }}
                >
                  {serviceCar.service.name}
                </span>
              </div>
              <span
                className="flex flex-col justify-start"
                style={{
                  color: Color.INDIGO_BLUE,
                  fontWeight: 600,
                  fontSize: 14,
                  lineHeight: "150%",
                }}
              >
                {`$${Number(
                  BusinessRule.calculateEachTargetPrice(
                    serviceCar.service.price,
                    serviceCar.cars.length,
                  ),
                ).toFixed(2)}`}
              </span>
            </div>
          ))}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-2">
          <SizeableImage
            src={require("../../../public/no_items_order_summary.png")}
            size={32}
            style={{ marginBottom: 10 }}
          />
          <span
            className="text-center"
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: 1,
              color: Color.BLACK_PEARL,
              marginBottom: 8,
            }}
          >
            You haven’t selected any items for now
          </span>
          <span
            className="items-start text-center px-[27px]"
            style={{
              fontWeight: 400,
              fontSize: 12,
              lineHeight: "150%",
              color: Color.INDIGO_BLUE_06,
            }}
          >
            Please press the <span style={{ fontWeight: 700 }}>add new car</span> button and setup
            your car who want to be service
          </span>
        </div>
      )}
    </div>
  );
};

const AddonBlock = (props: { addonCars: AddonCar[] | undefined }) => {
  return (
    <div
      className="flex flex-col py-4 mb-4"
      style={{
        border: "0.5px solid #E6E6E8",
        borderLeft: 0,
        borderRight: 0,
      }}
    >
      {props.addonCars && props.addonCars.length !== 0 ? (
        <>
          {props.addonCars.map((addonCar, index) => (
            <div
              className="flex flex-row justify-between"
              style={{
                marginBottom: index === props.addonCars!.length - 1 ? 0 : 16,
              }}
            >
              <div className="flex flex-row">
                <span
                  className="flex flex-col mr-3 justify-start mt-1"
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    lineHeight: 1,
                    color: Color.INDIGO_BLUE_06,
                  }}
                >
                  {`${addonCar.cars.length}x`}
                </span>
                <span
                  className="flex flex-col justify-self-stretch"
                  style={{
                    color: Color.INDIGO_BLUE,
                    fontWeight: 400,
                    fontSize: 14,
                    lineHeight: "150%",
                    maxWidth: 173,
                  }}
                >
                  {addonCar.addon.name}
                </span>
              </div>
              <span
                className="flex flex-col justify-start"
                style={{
                  color: Color.INDIGO_BLUE,
                  fontWeight: 600,
                  fontSize: 14,
                  lineHeight: "150%",
                }}
              >
                {`$${Number(
                  BusinessRule.calculateEachTargetAddonPrice(
                    addonCar.addon.cost,
                    addonCar.cars.length,
                  ),
                ).toFixed(2)}`}
              </span>
            </div>
          ))}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center py-2 max-w-[282px]">
          <SizeableImage
            src={require("../../../public/no_items_order_summary.png")}
            size={32}
            style={{ marginBottom: 10 }}
          />
          <span
            className="text-center"
            style={{
              fontWeight: 600,
              fontSize: 14,
              lineHeight: 1,
              color: Color.BLACK_PEARL,
              marginBottom: 8,
            }}
          >
            You haven’t selected any add-ons
          </span>
        </div>
      )}
    </div>
  );
};

const EndLine = (props: {
  onClickPayNow: () => void;
  onClickGoBack: () => void;
  isLoadingPayment: boolean;
  isDisabledPayment: boolean;
  isDisabledGoback: boolean;
}) => {
  return (
    <div className="flex flex-col">
      <div className="flex flex-col gap-3">
        <BackgroundSmallButton
          backgroundColor="BLACK_PEARL"
          title="Pay Now!"
          onClick={() => props.onClickPayNow()}
          disabled={props.isDisabledPayment}
          isLoading={props.isLoadingPayment}
          style={{
            height: 40,
          }}
        />
        <WhiteButton
          title="Go Back"
          onClick={() => props.onClickGoBack()}
          isLoading={false}
          disabled={props.isDisabledGoback}
          style={{
            width: "100%",
            height: 40,
            justifyContent: "center",
          }}
        />
      </div>
    </div>
  );
};
export default OrderSummary;
