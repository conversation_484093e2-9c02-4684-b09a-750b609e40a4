import BackgroundSmallButton from "../../atom/Buttons/BackgroundSmallButton";

const NonMemberContent = (props: {
  onClickBooking: () => void;
  onClickSignIn: () => void;
  onClickBecomeTech: () => void;
}) => (
  <div className="flex items-center">
    <button className="text-white text-sm mr-6" onClick={props.onClickSignIn}>
      Sign In
    </button>
    <BackgroundSmallButton
      title="Become a Technician"
      onClick={props.onClickBecomeTech}
      backgroundColor="ACCENT"
    />
  </div>
);

export default NonMemberContent;
