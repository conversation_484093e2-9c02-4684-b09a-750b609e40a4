import Image from "next/image";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../@redux";
import ProfilePlaceholder from "../../atom/ProfilePlaceholder";

const TechnicianContent = (props: {
  username: string;
  onClickBookings: () => void;
  onClickMyAccount: () => void;
  onClickSignOut: () => void;
  modalIsOn: boolean;
  modalOnoff: () => void;
}) => {
  const user = useSelector((state: RootState) => state.auth.user);

  return (
    <div className="flex items-center">
      <button
        className="flex items-center"
        onClick={(event) => {
          event.stopPropagation();
          props.modalOnoff();
        }}
      >
        <span className="text-sm text-white mr-3">Hello, {props.username}</span>
        <div
          className="w-8 h-8 rounded-full overflow-hidden mr-4 flex items-center"
          style={{
            border: "1px solid rgba(255,255,255,0.1)",
          }}
        >
          {/* <img src={user?.profilePictureUri} className="object-cover w-8 h-8" /> */}
          {user?.profilePictureUri ? (
            <img src={user?.profilePictureUri} className="object-cover w-8 h-8" />
          ) : (
            <ProfilePlaceholder size={32} />
          )}
        </div>
        <Image src={require("../../../../public/white_see_more.png")} />
      </button>
      <div
        className="absolute bg-white px-4 py-3 flex flex-col top-14 right-8 shadow-md border z-10"
        style={{
          display: props.modalIsOn ? "flex" : "none",
        }}
      >
        <button
          className="text-sm h-8 text-left"
          style={{
            width: 80,
          }}
          onClick={props.onClickBookings}
        >
          Bookings
        </button>
        <button
          className="text-sm h-8 text-left"
          style={{
            width: 80,
          }}
          onClick={props.onClickMyAccount}
        >
          Profile
        </button>
        <button
          className="text-sm h-8 text-[#FF3B31] text-left"
          style={{
            width: 80,
          }}
          onClick={props.onClickSignOut}
        >
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default TechnicianContent;
