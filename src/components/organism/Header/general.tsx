import React from "react";
import Header from ".";
import MobileHeader from "./mobile";

interface GlobalHeaderProps {
  isTechnician?: boolean;
  isShowingUserModal: boolean;
  setIsShowingUserModal: (isShowingUserModal: boolean) => void;
}

const GlobalHeader = (props: GlobalHeaderProps) => {
  return (
    <>
      <div className="hidden lg:block w-full">
        <Header
          isTechnician={props.isTechnician}
          userModalisOn={props.isShowingUserModal}
          userModalOnoff={() => props.setIsShowingUserModal(!props.isShowingUserModal)}
        />
      </div>
      <div
        className="visible lg:hidden w-full h-full flex flex-col"
        style={{
          height: 78,
        }}
      >
        <MobileHeader
          isTechnician={props.isTechnician}
          onClickMenuButton={() => props.setIsShowingUserModal(true)}
        />
        <div className="visible md:hidden px-4 h-6 flex flex-col items-center justify-center">
          <p>
            Questions? Call us at{" "}
            <a href={`tel:${+15198704546}`} className="underline">
              (*************
            </a>
          </p>
        </div>
      </div>
    </>
  );
};

export default GlobalHeader;
