import { get<PERSON><PERSON><PERSON> } from "cookies-next";
import { useRouter } from "next/router";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../@redux";
import { thunkSignOutUser } from "../../../@redux/modules/auth/thunks";
import { Mixpanel } from "../../../functions/mixpanel";
import { Color } from "../../../types/Color";
import LogoButton from "../../atom/Buttons/LogoButton";
import CustomerContent from "./CustomerContent";
import NonMemberContent from "./NonMemberContent";
import TechnicianContent from "./TechnicianContent";

const Header = ({
  isTechnician,
  userModalisOn,
  userModalOnoff,
}: {
  isTechnician?: boolean;
  userModalisOn: boolean;
  userModalOnoff: () => void;
}) => {
  const dispatch = useDispatch<any>();
  const router = useRouter();
  const user = useSelector((state: RootState) => state.auth.user);
  const firstName = useSelector((state: RootState) => state.auth.user?.firstName);
  const lastName = useSelector((state: RootState) => state.auth.user?.lastName);

  return (
    <div
      className="w-full justify-center items-center py-3"
      style={{ background: Color.BLACK_PEARL }}
    >
      <div
        style={{
          padding: "0px 40px",
        }}
        className="flex justify-between items-center flex-1"
      >
        <LogoButton
          style={{ width: 96, height: 38 }}
          onClick={
            user === undefined
              ? () => router.push("/")
              : !isTechnician
              ? () => router.push("/customer_booking")
              : () => router.push("/tech_booking")
          }
        />
        <p style={{ color: "white" }}>
          Have any questions? Call us at <a href={`tel:${+15198704546}`} className="underline">(*************</a>
        </p>
        {user === undefined ? (
          <NonMemberContent
            onClickBooking={() => {
              if (getCookie("localConsent") === true) {
                Mixpanel.track("Book Appointment Clicked");
              }
              router.push("/");
            }}
            onClickSignIn={() => router.push("/login")}
            onClickBecomeTech={() => router.push("/new_tech")}
          />
        ) : !isTechnician ? (
          <CustomerContent
            username={firstName!}
            onClickMyBookings={() => router.push("/customer_booking")}
            onClickBookAppointment={() => {
              if (getCookie("localConsent") === true) {
                Mixpanel.track("Book Appointment Clicked");
              }
              router.push("/");
            }}
            onClickMyAccount={() => router.push("/customer_profile")}
            onClickSignOut={() => {
              dispatch(thunkSignOutUser());
              router.replace("/login");
            }}
            modalIsOn={userModalisOn}
            modalOnOff={userModalOnoff}
          />
        ) : (
          <TechnicianContent
            username={firstName!}
            onClickBookings={() => router.push("/tech_booking")}
            onClickMyAccount={() => router.push("/account")}
            onClickSignOut={() => {
              dispatch(thunkSignOutUser());
              router.replace("/login");
            }}
            modalIsOn={userModalisOn}
            modalOnoff={userModalOnoff}
          />
        )}
      </div>
    </div>
  );
};

export default Header;
