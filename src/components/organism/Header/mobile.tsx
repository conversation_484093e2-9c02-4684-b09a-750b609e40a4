import Image from "next/image";
import { useRouter } from "next/router";
import { useSelector } from "react-redux";
import { RootState } from "../../../@redux";
import BookAppointmentButtonMobile from "../../atom/Buttons/BookAppointmentButtonMobile";
import LogoButton from "../../atom/Buttons/LogoButton";
import { Mixpanel } from "../../../functions/mixpanel";
import { getCookie } from "cookies-next";
import { Color } from "../../../types/Color";

interface MobileHeaderProps {
  isTechnician?: boolean;
  onClickMenuButton?: () => void;
}

const MobileHeader = (props: MobileHeaderProps) => {
  const router = useRouter();
  const user = useSelector((state: RootState) => state.auth.user);

  return (
    <div
      className="flex justify-between items-center w-full h-full px-4"
      style={{
        height: 54,
        background: Color.BLACK_PEARL,
      }}
    >
      <LogoButton
        onClick={
          user === undefined
            ? () => router.push("/")
            : !props.isTechnician
            ? () => router.push("/customer_booking")
            : () => router.push("/tech_booking")
        }
      />
      <p className="white hidden md:block" style={{ color: "white" }}>
        Have any questions? Call us at{" "}
        <a href={`tel:${+15198704546}`} className="underline">
          (*************
        </a>
      </p>
      {
        <div className="flex items-center ">
          {!props.isTechnician && (
            <BookAppointmentButtonMobile
              onClick={() => {
                if (getCookie("localConsent") === true) {
                  Mixpanel.track("Book Appointment Clicked");
                }
                router.push("/");
              }}
            />
          )}
          <div
            style={{
              height: 24,
              borderRight: "1px solid rgba(255,255,255,0.2)",
              marginRight: 20,
            }}
          />
          <button
            className="flex justify-center items-center"
            style={{
              marginRight: 10,
            }}
            onClick={props.onClickMenuButton}
          >
            <Image src={require("../../../../public/menu_gray.png")} />
          </button>
        </div>
      }

      {/* <div className="flex items-center ">
        <button
          onClick={() => router.push("/")}
          className="text-white"
          style={{
            fontSize: 13,
            marginRight: 16,
          }}
        >
          Appointment
        </button>
        <div className="w-8 h-8 rounded-full overflow-hidden flex items-center">
          <img src={user?.profilePictureUri} />
        </div>
      </div> */}
    </div>
  );
};

export default MobileHeader;
