import React from "react"
import { useSelector } from "react-redux";
import { RootState } from "../../../@redux";
import BookAppointmentButton from "../../atom/Buttons/BookAppointmentButton";
import ProfilePlaceholder from "../../atom/ProfilePlaceholder";
import Image from "next/image";

const CustomerContent = (props: {
  username: string;
  onClickMyBookings: () => void;
  onClickBookAppointment: () => void;
  onClickMyAccount: () => void;
  onClickSignOut: () => void;
  modalIsOn: boolean;
  modalOnOff: () => void;
}) => {
  const user = useSelector((state: RootState) => state.auth.user);

  return (
    <div className="flex items-center">
      <BookAppointmentButton onClick={props.onClickBookAppointment} style={{ marginRight: 20 }} />
      <div
        style={{
          height: 24,
          borderRight: "1px solid rgba(255,255,255,0.2)",
          marginRight: 20,
        }}
      />
      <button className="flex items-center py-1" onClick={props.modalOnOff}>
        <span className="text-sm text-white mr-3">Hello, {props.username}</span>
        <div className="w-8 h-8 rounded-full overflow-hidden mr-4 flex items-center">
          {user?.profilePictureUri ? (
            <img src={user?.profilePictureUri} className="object-cover w-8 h-8" />
          ) : (
            <ProfilePlaceholder size={32} />
          )}
        </div>
        <Image src={require("../../../../public/white_see_more.png")} />
      </button>
      <div
        className="hidden lg:flex absolute bg-white px-4 py-3 flex-col top-14 right-8 shadow-md border z-10"
        style={{
          display: props.modalIsOn ? "flex" : "none",
        }}
      >
        <button
          className="text-sm h-8 text-left"
          style={{
            width: 80,
          }}
          onClick={props.onClickMyBookings}
        >
          Bookings
        </button>
        <button
          className="text-sm h-8 text-left"
          style={{
            width: 80,
          }}
          onClick={props.onClickMyAccount}
        >
          Profile
        </button>
        <button
          className="text-sm h-8 text-[#FF3B31] text-left"
          style={{
            width: 80,
          }}
          onClick={props.onClickSignOut}
        >
          Sign Out
        </button>
      </div>
    </div>
  );
};

export default CustomerContent