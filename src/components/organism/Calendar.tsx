import moment from "moment";
import { useState } from "react";
import { CalendarBlock, CalendarBlockType } from "../../types/CalendarBlock";
import { Color } from "../../types/Color";
import { GoogleCalendarBlock } from "../../types/GoogleCalendarBlock";
import NoPaddingPaper from "../atom/NoPaddingPaper";
import { DURATION_UNIT } from "../../values";

export type CustomDate = {
  year: number;
  month: number;
  date: number;
  day: string;
};

interface CalendarProps {
  onClickPrevWeek: () => void;
  onClickNextWeek: () => void;
  shownDateItems: CustomDate[];
  hourItems: string[];
  onClickCalendarBlock: (date: CustomDate, hour: string) => void;
  bookingData: CalendarBlock[];
}

const Calendar = (props: CalendarProps) => {
  const [clickedBookingDate, setClickedBookingDate] = useState<CustomDate | null>(null);
  const [clickedBookingIndex, setClickedBookingIndex] = useState<number | null>(null);
  const [isGoogleCalendarShown, setIsGoogleCalendarShown] = useState<boolean>(true);
  const [isWheelEasyCalendarShown, setIsWheelEasyCalendarShown] = useState<boolean>(true);

  function getTopDistanceOfBlock(hour: string) {
    const hourIndex = props.hourItems.indexOf(hour);
    return hourIndex * 40;
  }

  function getStackLevel(date: number, indexInDate: number) {
    let result = 0;
    const dayBooking = props.bookingData.filter(
      (booking) => moment(booking.startDateTime).date() === date
    );
    const targetBooking = dayBooking[indexInDate];

    for (let i = 0; i < indexInDate; i++) {
      const booking = dayBooking[i];
      if (checkIfTwoBlockOverlap(booking, targetBooking)) {
        result++;
      }
    }

    return result;
  }

  function checkIfTwoBlockOverlap(aBlock: CalendarBlock, bBlock: CalendarBlock) {
    const aBlockMoment = moment(aBlock.startDateTime);
    const bBlockMoment = moment(bBlock.startDateTime);

    if (
      aBlockMoment.year() !== bBlockMoment.year() ||
      aBlockMoment.month() !== bBlockMoment.month() ||
      aBlockMoment.date() !== bBlockMoment.date()
    )
      return false;
    return checkIfTwoBlockOverlapByTime(
      aBlockMoment.format("HH:mm"),
      aBlock.duration,
      bBlockMoment.format("HH:mm"),
      bBlock.duration
    );
  }

  function checkIfTwoBlockOverlapByTime(
    aStartHour: string,
    aDuration: number,
    bStartHour: string,
    bDuration: number
  ) {
    const aStartHourIndex = props.hourItems.indexOf(aStartHour);
    const aEndHourIndex = aStartHourIndex + aDuration;

    const bStartHourIndex = props.hourItems.indexOf(bStartHour);
    const bEndHourIndex = bStartHourIndex + bDuration;

    return !(aStartHourIndex >= bEndHourIndex || aEndHourIndex <= bStartHourIndex);
  }

  // function checkIfNewBlockOverlap(newBlock: CalendarBlock) {
  //   const result = bookingData.some((booking) => {
  //     const overlapFlag = checkIfTwoBlockOverlap(newBlock, booking);
  //     // If overlapped by added block, ignore.
  //     if (
  //       overlapFlag &&
  //       booking.year === selectedTmpBlockTime?.year &&
  //       booking.month === selectedTmpBlockTime.month &&
  //       booking.date === selectedTmpBlockTime.date
  //     ) {
  //       return false;
  //     } else {
  //       return overlapFlag;
  //     }
  //   });
  //   return result;
  // }

  function isSelected(startDateTime: string, index: number) {
    if (!clickedBookingDate) return false;
    const clickedDate = moment(startDateTime);
    return (
      clickedBookingDate.year === clickedDate.year() &&
      clickedBookingDate.month === clickedDate.month() + 1 &&
      clickedBookingDate.date === clickedDate.date() &&
      clickedBookingIndex === index
    );
  }

  function getCarAddons(
    carId: number,
    addons: {
      id: number;
      cars: {
        id: number;
        make: string;
        model: string;
        year: string;
      }[];
    }[]
  ) {
    let result = [];
    for (const addon of addons) {
      const containedAddon = addon.cars.find((car) => car.id === carId);
      if (containedAddon) {
        result.push(addon);
      }
    }

    return result;
  }

  return (
    <NoPaddingPaper>
      <div>
        <div className="flex justify-between mr-6">
          <div className="flex items-center ml-5">
            <div className="flex items-center mr-4">
              <input
                id="wheeleasy-calendar-checkbox"
                type="checkbox"
                checked={isWheelEasyCalendarShown}
                onChange={(e) => setIsWheelEasyCalendarShown(e.target.checked)}
              />
              <label className="text-sm ml-1 select-none" htmlFor="wheeleasy-calendar-checkbox">
                Wheeleasy
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="google-calendar-checkbox"
                type="checkbox"
                checked={isGoogleCalendarShown}
                onChange={(e) => setIsGoogleCalendarShown(e.target.checked)}
              />
              <label className="text-sm ml-1 select-none" htmlFor="google-calendar-checkbox">
                Google
              </label>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <button
              className="flex items-center justify-center border-b border-b-gray"
              style={{
                height: 65,
              }}
              onClick={props.onClickPrevWeek}
            >
              <div
                className="flex items-center justify-center rounded-full -scale-x-[1]"
                style={{
                  width: 32,
                  height: 32,
                  backgroundColor: "#f0f0f0",
                  paddingRight: 4,
                }}
              >
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M6 0L12 6L6 12L6 0Z" fill="#BDBDBD" />
                </svg>
              </div>
            </button>

            <button
              className="flex items-center justify-center border-b border-b-gray"
              style={{
                height: 65,
              }}
              onClick={props.onClickNextWeek}
            >
              <div
                className="flex items-center justify-center rounded-full"
                style={{
                  width: 32,
                  height: 32,
                  backgroundColor: "#f0f0f0",
                  paddingRight: 3,
                }}
              >
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M6 0L12 6L6 12L6 0Z" fill="#BDBDBD" />
                </svg>
              </div>
            </button>
          </div>
        </div>
        <div className="flex">
          <div className="flex flex-col ">
            <div
              className="border-t"
              style={{
                height: 65,
              }}
            />
            {props.hourItems.map((hour, index) => (
              <div
                key={`hour-${index}`}
                className="flex items-center border-t justify-center px-3"
                style={{
                  height: 40,
                }}
              >
                <span
                  className="text-xs"
                  style={{
                    color: "gray",
                  }}
                >
                  {hour}
                </span>
              </div>
            ))}
          </div>
          <div className="border flex w-full">
            {props.shownDateItems.map((date, dateIndex) => (
              <div key={`date-${dateIndex}`} className="flex flex-col flex-1">
                <div className="flex items-center border-b justify-center py-4">
                  <span
                    className="text-2xl font-medium mr-3"
                    style={{
                      color:
                        new Date().getFullYear() === date.year &&
                        new Date().getMonth() + 1 === date.month &&
                        new Date().getDate() === date.date
                          ? Color.ACCENT
                          : "black",
                    }}
                  >
                    {date.month}/{date.date}
                  </span>
                  <span
                    className="text-xs"
                    style={{
                      color:
                        new Date().getFullYear() === date.year &&
                        new Date().getMonth() + 1 === date.month &&
                        new Date().getDate() === date.date
                          ? Color.ACCENT
                          : "gray",
                    }}
                  >
                    {date.day}
                  </span>
                </div>
                <div className="relative flex flex-col border-r">
                  {props.hourItems.map((hour, index) => (
                    <button
                      key={index}
                      className="border-b hover:bg-[rgba(0,0,0,0.05)] min-h-[40px]"
                      onClick={() => {
                        props.onClickCalendarBlock(date, hour);
                      }}
                    ></button>
                  ))}
                  {props.bookingData
                    .filter(
                      (booking) =>
                        moment(booking.startDateTime).year() === date.year &&
                        moment(booking.startDateTime).month() === date.month - 1 &&
                        moment(booking.startDateTime).date() === date.date
                    )
                    .map((booking, index) =>
                      false ? (
                        <button
                          className="absolute top-[40px] left-0 w-full bg-red-300 border"
                          style={{
                            top: getTopDistanceOfBlock(
                              moment(booking.startDateTime).format("HH:mm")
                            ),
                            height: booking.duration * 40,
                            left: getStackLevel(date.date, index) * 10,
                            zIndex:
                              getStackLevel(date.date, index) +
                              5 +
                              (clickedBookingIndex === index ? 9999 : 0),
                            width: `calc(100% - ${getStackLevel(date.date, index) * 10}px)`,
                          }}
                        >
                          <span>{moment(booking.startDateTime).format("HH:mm")}</span>
                        </button>
                      ) : booking.type === CalendarBlockType.NOT_AVAILABLE ? (
                        <button
                          className="absolute top-[40px] left-0 w-full border cursor-default"
                          style={{
                            top: getTopDistanceOfBlock(
                              moment(booking.startDateTime).format("HH:mm")
                            ),
                            height: booking.duration * 40,
                            zIndex: 1,
                            backgroundColor: "rgba(0,0,0,0.05)",
                          }}
                        ></button>
                      ) : (
                        <button
                          className="absolute top-[40px] left-0 w-full cursor-default bg-white text-left"
                          style={{
                            paddingLeft: 12,
                            top: getTopDistanceOfBlock(
                              moment(booking.startDateTime).format("HH:mm")
                            ),
                            height: booking.duration * 40,
                            left: getStackLevel(date.date, index) * 10,
                            zIndex:
                              getStackLevel(date.date, index) +
                              5 +
                              (clickedBookingIndex === index &&
                              clickedBookingDate?.date === date.date
                                ? 9999
                                : 0),
                            width: `calc(100% - ${getStackLevel(date.date, index) * 10}px)`,
                            border:
                              "2px solid " + (booking.type === "GOOGLE" ? "#7EA172" : Color.ACCENT),
                            display:
                              (booking.type === CalendarBlockType.GOOGLE &&
                                isGoogleCalendarShown) ||
                              ((booking.type === CalendarBlockType.BOOKING ||
                                booking.type === CalendarBlockType.MANUAL) &&
                                isWheelEasyCalendarShown)
                                ? "block"
                                : "none",
                            fontFamily: "Roboto",
                          }}
                          onClick={() => {
                            if (clickedBookingIndex !== index || clickedBookingDate !== date) {
                              setClickedBookingDate(date);
                              setClickedBookingIndex(index);
                            } else {
                              setClickedBookingDate(null);
                              setClickedBookingIndex(null);
                            }
                          }}
                        >
                          {booking.type === CalendarBlockType.GOOGLE ? (
                            <div
                              style={{
                                display:
                                  clickedBookingIndex === index && clickedBookingDate === date
                                    ? "flex"
                                    : "none",
                                fontFamily: "Roboto",
                                boxShadow:
                                  "0px -5px 15px rgba(0,0,0,0.15), 0px 5px 15px rgba(0,0,0,0.15)",
                                right:
                                  dateIndex === 0 || dateIndex === 1 || dateIndex === 2
                                    ? -390
                                    : undefined,
                                left:
                                  dateIndex === 0 || dateIndex === 1 || dateIndex === 2
                                    ? undefined
                                    : -390,
                              }}
                              className="w-96 rounded-md border shadow-xl bg-white absolute top-0 p-6 flex-col z-50"
                            >
                              <div className="flex items-center ">
                                <span className="text-sm text-[rgba(0,0,0,0.6)] mr-3">
                                  Google event
                                </span>
                                <div className="flex items-center ">
                                  <div
                                    style={{
                                      width: 14,
                                      height: 14,
                                      backgroundColor: (booking as GoogleCalendarBlock)
                                        .calendarColor,
                                    }}
                                    className="rounded-full mr-1"
                                  />
                                  <span
                                    style={{
                                      fontSize: 13,
                                    }}
                                  >
                                    {(booking as GoogleCalendarBlock).calendarName}
                                  </span>
                                </div>
                              </div>
                              <span
                                className="font-medium"
                                style={{
                                  fontSize: 22,
                                  marginBottom: 2,
                                }}
                              >
                                {booking.previewTitle}
                              </span>
                              <div className="flex items-center">
                                <div className="flex items-center ">
                                  <span className="text-sm text-[rgba(0,0,0,0.6)] mr-5">
                                    {moment(booking.startDateTime).format("HH:mm")} -{" "}
                                    {moment(booking.startDateTime)
                                      .add(DURATION_UNIT * booking.duration, "minute")
                                      .format("HH:mm")}
                                  </span>
                                  <span className="text-sm text-[rgba(0,0,0,0.6)]">
                                    {(booking as GoogleCalendarBlock).location}
                                  </span>
                                </div>
                              </div>
                              {/* {booking.description && <div className="border-t my-3" />} */}
                              <span className="text-sm">
                                {(booking as GoogleCalendarBlock).description}
                              </span>
                            </div>
                          ) : (
                            <div
                              style={{
                                display:
                                  clickedBookingIndex === index && clickedBookingDate === date
                                    ? "flex"
                                    : "none",
                                fontFamily: "Roboto",
                                boxShadow:
                                  "0px -5px 15px rgba(0,0,0,0.15), 0px 5px 15px rgba(0,0,0,0.15)",
                                right:
                                  dateIndex === 0 || dateIndex === 1 || dateIndex === 2
                                    ? -390
                                    : undefined,
                                left:
                                  dateIndex === 0 || dateIndex === 1 || dateIndex === 2
                                    ? undefined
                                    : -390,
                                top:
                                  getTopDistanceOfBlock(
                                    moment(booking.startDateTime).format("HH:mm")
                                  ) >=
                                  40 * 12
                                    ? undefined
                                    : 0,
                                bottom:
                                  getTopDistanceOfBlock(
                                    moment(booking.startDateTime).format("HH:mm")
                                  ) >=
                                  40 * 12
                                    ? 0
                                    : undefined,
                              }}
                              className="w-96 rounded-md border shadow-xl bg-white absolute p-6 flex-col z-50"
                            >
                              <div className="flex items-center ">
                                <span className="text-sm text-[rgba(0,0,0,0.6)] mr-3">Booking</span>
                                <div className="flex items-center ">
                                  <div
                                    style={{
                                      width: 14,
                                      height: 14,
                                      backgroundColor: (booking as GoogleCalendarBlock)
                                        .calendarColor,
                                    }}
                                    className="rounded-full mr-1"
                                  />
                                </div>
                              </div>
                              <span
                                className="font-medium"
                                style={{
                                  fontSize: 22,
                                  marginBottom: 2,
                                }}
                              >
                                {booking.previewTitle}
                              </span>
                              <div className="flex items-center">
                                <div className="flex items-center ">
                                  <span className="text-sm text-[rgba(0,0,0,0.6)] mr-5">
                                    {moment(booking.startDateTime).format("HH:mm")} -{" "}
                                    {moment(booking.startDateTime)
                                      .add(DURATION_UNIT * booking.duration, "minute")
                                      .format("HH:mm")}
                                  </span>
                                </div>
                              </div>
                              <div className="border-t my-3" />
                              <div className="flex items-center ">
                                {/* <img src={booking.customer.profilePictureUri}/> */}
                                <div className="flex flex-col ">
                                  <span className="text-sm text-[rgba(0,0,0,0.6)]">
                                    Customer Info
                                  </span>
                                  <span className="text-sm">
                                    {/* {booking.customer.firstName + " " + booking.customer.lastName} */}
                                  </span>
                                </div>
                              </div>
                              {/* <span className="text-sm">{booking.customer.email}</span> */}
                              <div className="flex flex-col mb-3">
                                {/* <span className="text-sm">{booking.customer.address.street1}</span>
                                <span className="text-sm">{booking.customer.address.street2}</span>
                                <span className="text-sm">{booking.customer.address.city}</span>
                                <span className="text-sm">{booking.customer.address.country}</span>
                                <span className="text-sm">{booking.customer.address.province}</span>
                                <span className="text-sm">{booking.customer.address.postal}</span> */}
                              </div>
                              <span className="text-sm text-[rgba(0,0,0,0.6)] mr-3">Services</span>
                              {(booking as any).services.map((item: any) => (
                                <div className="flex flex-col mb-5">
                                  <span className="text-lg mb-2">{item.name}</span>
                                  {item.cars.map((carItem: any) => (
                                    <div className="flex flex-col ">
                                      <div className="flex items-center mb-2">
                                        <img
                                          src={carItem.pictureUri}
                                          style={{
                                            width: 40,
                                            height: 40,
                                            marginRight: 8,
                                          }}
                                        />
                                        <div className="flex flex-col ">
                                          <span className="text-sm">{carItem.year}</span>
                                          <div className="flex items-center space-x-1">
                                            <span className="text-sm">{carItem.make}</span>
                                            <span className="text-sm">{carItem.model}</span>
                                            <span className="text-sm">({carItem.color})</span>
                                          </div>
                                        </div>
                                      </div>
                                      {/* {getCarAddons(carItem.id, booking.addons).length > 0 && (
                                        <span className="text-sm text-[rgba(0,0,0,0.6)] mr-3">
                                          Addons
                                        </span>
                                      )}
                                      {getCarAddons(carItem.id, booking.addons).map(
                                        (addon: any) => (
                                          <span className="text-sm">{addon.name}</span>
                                        )
                                      )} */}
                                    </div>
                                  ))}
                                </div>
                              ))}
                            </div>
                          )}
                          {booking.duration === 1 ? (
                            <div className="flex items-center">
                              <span className="text-sm mr-1">
                                {moment(booking.startDateTime).format("HH:mm")} -{" "}
                                {moment(booking.startDateTime)
                                  .add(DURATION_UNIT * booking.duration, "minute")
                                  .format("HH:mm")}
                              </span>
                              <span
                                className="text-sm"
                                style={{
                                  color:
                                    booking.type === CalendarBlockType.GOOGLE
                                      ? Color.GOOGLE
                                      : Color.ACCENT,
                                }}
                              >
                                {booking.previewTitle}
                              </span>
                            </div>
                          ) : (
                            <div className="flex flex-col ">
                              <span className="text-xs">
                                {booking.type === CalendarBlockType.GOOGLE ? "Google" : "Wheeleasy"}
                              </span>
                              <span className="text-sm">
                                {moment(booking.startDateTime).format("HH:mm")} -{" "}
                                {moment(booking.startDateTime)
                                  .add(DURATION_UNIT * booking.duration, "minute")
                                  .format("HH:mm")}
                              </span>
                              <span
                                className="text-sm"
                                style={{
                                  color:
                                    booking.type === CalendarBlockType.GOOGLE
                                      ? Color.GOOGLE
                                      : Color.ACCENT,
                                }}
                              >
                                {booking.previewTitle}
                              </span>
                            </div>
                          )}
                        </button>
                      )
                    )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </NoPaddingPaper>
  );
};

export default Calendar;
