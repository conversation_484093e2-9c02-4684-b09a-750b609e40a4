import { useEffect } from "react";
import Modal from "../Modal";
import CloseButton from "../atom/Buttons/CloseButton";
import WhiteButton from "../atom/Buttons/WhiteButton";
import SizeableImage from "../atom/SizeableImage";
import { Color } from "../../types/Color";
import ResizingModal from "../Modal/ResizingModal";

interface RemoveWarningModalProps {
  isOn: boolean;
  onOff: () => void;
  title: string;
  description: string;
  iconSrc: any;
  iconSize: number;
  isLoading?: boolean;
  onClickDelete: () => void;
}

const RemoveWarningModal = (props: RemoveWarningModalProps) => {
  function handleCloseModal() {
    props.onOff();
  }

  async function handleOnClickDelete() {
    await props.onClickDelete();
    props.onOff();
  }

  return (
    <ResizingModal
      isOn={props.isOn}
      onOff={handleCloseModal}
      modalSize={{
        mobile: {
          width: 375,
          height: 264,
        },
        web: {
          width: 375,
          height: 264,
        },
      }}
    >
      <div className="relative flex flex-col bg-white rounded-lg h-auto w-full max-w-[375px] p-6">
        <div className="absolute top-5 right-4">
          <CloseButton onClick={handleCloseModal} />
        </div>
        <div className="flex flex-col flex-1 h-full items-stretch">
          <SizeableImage src={props.iconSrc} size={props.iconSize} />
          <div className="flex flex-col pt-5 pb-8">
            <span
              className=""
              style={{
                marginBottom: 8,
                fontWeight: 500,
                fontSize: 18,
                lineHeight: "156%",
                color: Color.EBONY,
              }}
            >
              {props.title}
            </span>
            <span
              className=""
              style={{
                fontWeight: 400,
                fontSize: 14,
                lineHeight: "20px",
                color: Color.PALE_SKY,
              }}
            >
              {props.description}
            </span>
          </div>
          <div className="flex flex-row">
            <WhiteButton
              title="Cancel"
              onClick={props.onOff}
              style={{
                flex: 1,
                justifyContent: "center",
                height: 40,
                paddingTop: 10,
                paddingBottom: 10,
              }}
            />
            <div style={{ marginRight: 12 }} />
            <WhiteButton
              title="Delete"
              onClick={handleOnClickDelete}
              style={{
                flex: 1,
                justifyContent: "center",
                height: 40,
                paddingTop: 10,
                paddingBottom: 10,
                backgroundColor: Color.ALIZARIN_CRIMSON,
              }}
              titleStyle={{
                color: Color.WHITE,
                fontWeight: 500,
              }}
              isLoading={props.isLoading}
            />
          </div>
        </div>
      </div>
    </ResizingModal>
  );
};

export default RemoveWarningModal;
