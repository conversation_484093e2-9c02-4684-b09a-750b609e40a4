import Image from "next/image";
import { useContext, useEffect, useState } from "react";
import { carServiceContext } from "../../../contexts/CarServiceContext";
import { apiDeleteCar } from "../../../functions/api/car";
import { getCurrentUser } from "../../../functions/cognito/util";
import { loadCarLocalStorage, saveCarLocalStorage } from "../../../functions/local";
import { VehicleService } from "../../../services/VehicleService";
import { Color } from "../../../types/Color";
import { Car, isCarEqual } from "../../../types/car";
import ResizingFullModal from "../../Modal/ResizingFullModal";
import BackgroundSmallButton from "../../atom/Buttons/BackgroundSmallButton";
import SizeableImage from "../../atom/SizeableImage";
import Text14 from "../../atom/Texts/base/Text14";
import Title18 from "../../atom/Texts/base/Title18";
import VehicleAddonEditModal from "../VehicleAddonEditModal";

const SelectCarModal = () => {
  const context = useContext(carServiceContext);
  const [isNewPage, setIsNewPage] = useState<boolean>(false);
  const [ownedCarArray, setOwnedCarArray] = useState<Car[]>();
  const [selectedCars, setSelectedCars] = useState<Car[]>([]);
  const isOn = context!.isAddCarModalOn;

  const WEB_MODAL_WIDTH = 1280;
  const WEB_MODAL_HEIGHT = 600;

  function onOff() {
    if (context) {
      context.setIsAddCarModalOn(false);
    }
  }

  useEffect(() => {
    if (isOn) {
      if (context && context.carServices.length) {
        let newSelectedCars = [];

        for (let i = 0; i < context.carServices.length; i++) {
          newSelectedCars.push(context.carServices[i].car);
        }

        setSelectedCars(newSelectedCars);
      } else {
        setSelectedCars([]);
      }
    }
  }, [isOn]);

  async function fetchCar() {
    if ((await getCurrentUser()) === null) {
      const savedCars = await loadCarLocalStorage();
      if (savedCars === null) {
        await saveCarLocalStorage([]);
        setOwnedCarArray([]);
      } else {
        setOwnedCarArray(savedCars);
      }
    } else {
      const result = await VehicleService.getAll();
      setOwnedCarArray(result);
    }
  }

  useEffect(() => {
    fetchCar();
  }, []);

  async function handleOnClickConfirm() {
    onSelectOwnedCars(selectedCars);
    onOff();
  }

  function getIsCarInArray(target: Car) {
    for (let i = 0; i < selectedCars.length; i++) {
      if (isCarEqual(selectedCars[i], target)) {
        return true;
      }
    }

    return false;
  }

  function handleOnClickOwnedCar(target: Car) {
    if (getIsCarInArray(target)) {
      setSelectedCars((prev) => prev.filter((item) => !isCarEqual(item, target)));
    } else {
      setSelectedCars((prev) => prev.concat(target));
    }
  }

  function onSelectOwnedCars(cars: Car[]) {
    if (context) {
      context.setByCars(cars);
      if (cars.length > 0) {
        context.setSelectedCarServiceIndex(0);
      }
    }
  }

  return (
    <ResizingFullModal
      isOn={isOn}
      onOff={onOff}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      {!isNewPage ? (
        <>
          <div
            className="flex flex-col align-center justify-between bg-white pt-4 h-full w-full min-w-[320px] md:rounded-xl"
            style={{
              border: "1px solid rgba(0,0,0,0.15)",
              boxShadow: "0px 5px 15px rgba(0,0,0,0.05)",
            }}
          >
            <div className="h-full md:h-[400px] overflow-y-scroll">
              <div className="flex items-center sticky bg-white z-40 top-0 justify-between pb-4 md:mb-4 border-b border-b-gray">
                <div />
                <Title18>{isNewPage ? "Add New Vehicle" : "Select Cars"}</Title18>
                <button onClick={onOff} className="mr-5">
                  <SizeableImage src={require("../../../../public/close.png")} size={24} />
                </button>
              </div>
              <div className="flex flex-col overflow-y-scroll px-6 md:px-0 divide-t-[1px]">
                {ownedCarArray !== undefined ? (
                  ownedCarArray.map((item, index) => (
                    <CarButton
                      car={item}
                      key={index.toString()}
                      onClick={() => {
                        handleOnClickOwnedCar(item);
                      }}
                      onClickDelete={async () => {
                        await apiDeleteCar(item.id);
                        fetchCar();
                      }}
                      isSelected={getIsCarInArray(item)}
                    />
                  ))
                ) : (
                  <span>Loading...</span>
                )}
                <button
                  className="hidden justify-start self-stretch p-6 md:flex"
                  onClick={() => setIsNewPage(true)}
                >
                  <Image src={require("../../../../public/plus.png")} width={20} height={20} />
                  <Text14 style={{ fontWeight: 600, marginLeft: 12 }}>Add New Car</Text14>
                </button>
                <button
                  className="flex flex-col items-start justify-between py-7 md:hidden"
                  onClick={() => setIsNewPage(true)}
                >
                  <div className="flex flex-row items-center justify-center">
                    <Image src={require("../../../../public/cross.png")} width={20} height={20} />
                    <span
                      style={{
                        fontSize: 16,
                        fontWeight: 700,
                        lineHeight: 1,
                        color: Color.INDIGO_BLUE,
                        marginLeft: 12,
                      }}
                    >
                      Add New Vehicles
                    </span>
                  </div>
                </button>
              </div>
            </div>
            <div className="hidden items-center justify-end md:p-3 w-full px-4 pb-4 md:flex">
              <button
                onClick={handleOnClickConfirm}
                className="self-end text-sm px-4 py-2 rounded-md w-full md:w-fit"
                style={{
                  backgroundColor: Color.BLACK,
                  color: Color.WHITE,
                  minHeight: 44,
                }}
              >
                Confirm
              </button>
            </div>
            <div className="block md:hidden">
              <BackgroundSmallButton
                disabled={selectedCars.length <= 0}
                backgroundColor="ACCENT"
                title="Continue"
                onClick={handleOnClickConfirm}
                style={{
                  height: 52,
                  width: "100%",
                  borderRadius: 0,
                  fontSize: 16,
                }}
              />
            </div>
          </div>
        </>
      ) : (
        <VehicleAddonEditModal
          isOn={isNewPage}
          onOff={() => setIsNewPage(false)}
          disableBackground
          isEdit={false}
          onCompletion={() => {
            fetchCar();
            setIsNewPage(false);
          }}
        />
      )}
    </ResizingFullModal>
  );
};

const CarTextMobile = (props: { car: Car; isSelected: boolean }) => {
  return (
    <div className="flex flex-col items-start md:hidden">
      <span
        className="font-light leading-none"
        style={{
          fontSize: 16,
          fontWeight: 400,
          lineHeight: 1,
          color: Color.BLACK,
          marginBottom: 10,
        }}
      >
        {props.car.make}
      </span>
      <span
        className="font-semibold leading-none text-left"
        style={{
          fontSize: 16,
          fontWeight: 700,
          lineHeight: 1,
          color: Color.BLACK,
        }}
      >
        {props.car.model} {props.car.year} :{" "}
        <span
          style={{
            fontSize: 16,
            fontWeight: 500,
            lineHeight: 1,
          }}
        >
          ({props.car.color})
        </span>
      </span>
    </div>
  );
};

// need to refactor this and use VehicleItem component
// make new CheckableVehicleItem component

const CarButton = (props: {
  car: Car;
  onClick: () => void;
  onClickDelete: () => void;
  isSelected: boolean;
}) => (
  <button
    onClick={props.onClick}
    className="flex items-center justify-between py-4 md:px-6 md:py-[18px] hover:bg-[rgba(0,0,0,0.1)] border-b border-b-gray"
  >
    <div className="flex flex-row items-center md:gap-4">
      <div
        className="bg-gray-300 flex items-center justify-center relative overflow-hidden mr-6 md:mr-0"
        style={{
          width: 80,
          height: 80,
          borderRadius: 20,
        }}
      >
        {props.car.pictureUri ? (
          <img
            className="object-cover"
            src={props.car.pictureUri}
            style={{ width: 80, height: 80 }}
          />
        ) : (
          <SizeableImage
            size={50}
            src={require("../../../../public/car_placeholder.png")}
            style={{ opacity: 0.5 }}
          />
        )}
      </div>
      <div className="hidden flex-col items-start md:flex">
        <span
          className="font-light leading-none"
          style={{
            marginBottom: 10,
          }}
        >
          {props.car.make}
        </span>
        <span className="font-semibold leading-none text-left">
          {props.car.model} {props.car.year} :{" "}
          <span
            className="font-medium"
            style={{
              fontSize: 15,
            }}
          >
            ({props.car.color})
          </span>
        </span>
      </div>
      <CarTextMobile car={props.car} isSelected={props.isSelected} />
    </div>
    <div className="flex items-center">
      <input
        type="checkbox"
        checked={props.isSelected}
        onChange={() => {}}
        className="w-5 h-5"
        style={{
          accentColor: Color.ACCENT,
        }}
      />
    </div>
  </button>
);

export default SelectCarModal;
