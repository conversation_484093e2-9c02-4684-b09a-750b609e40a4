import { useEffect, useMemo, useState } from "react";
import { apiUpdateUserAddress } from "../../functions/api/user";
import { AddressService, NewAddress } from "../../services/AddressService";
import ConfirmCancelModal from "../Modal/ConfirmCancelModal";
import PlacesAutocomplete from "../atom/PlacesAutocomplete";
import TextInput from "../atom/TextInput";
import Text14 from "../atom/Texts/base/Text14";
import { Color } from "../../types/Color";

interface AddressModalProps {
  isOn: boolean;
  onOff: () => void;
  onUpdate: () => void;
  selectedAddress: NewAddress | null;
  selectedPhone: string | null;
}

const AddressModal = (props: AddressModalProps) => {
  const [street1, setStreet1] = useState<string>("");
  const [street2, setStreet2] = useState<string>("");
  const [city, setCity] = useState<string>("");
  const [province, setProvince] = useState<string>("");
  const [postal, setPostal] = useState<string>("");
  const [country, setCountry] = useState<string>("Canada");
  const [phone, setPhone] = useState<string>("");

  const isNew = props.selectedAddress === null;

  useEffect(() => {
    if (props.selectedAddress !== null) {
      setStreet1(props.selectedAddress.street1);
      setStreet2(props.selectedAddress.street2 || "");
      setCity(props.selectedAddress.city);
      setProvince(props.selectedAddress.province);
      setPostal(props.selectedAddress.postal);
      setCountry(props.selectedAddress.country);
    }
  }, [props.selectedAddress, props.isOn]);

  useEffect(() => {
    if (props.selectedPhone !== null) {
      setPhone(props.selectedPhone);
    }
  }, [props.selectedPhone, props.isOn]);

  const isContinuable = useMemo(() => {
    return [street1, city, province, postal, country, phone].every((x, index) => {
      console.log(x.length);
      return x.length > 0;
    });
  }, [street1, city, province, postal, country, phone]);

  function populateForms(
    addressLine1: string,
    addressLine2: string,
    city: string,
    province: string,
    postal: string,
    country: string
  ) {
    setStreet1(addressLine1);
    setStreet2(addressLine2);
    setCity(city);
    setProvince(province);
    setPostal(postal);
    setCountry(country);
  }

  return (
    <ConfirmCancelModal
      isOn={props.isOn}
      title="Edit Address"
      description=""
      onOff={props.onOff}
      content={
        <div className="space-y-3">
          <div className="space-y-1">
            <Text14>Address Line 1*</Text14>
            <PlacesAutocomplete
              isOn={props.isOn}
              initialValue={street1}
              populateForms={populateForms}
            />
          </div>
          <div className="space-y-1">
            <Text14>Address Line 2</Text14>
            <TextInput
              value={street2}
              onChange={(value) => setStreet2(value)}
              placeholder="Unit #101 etc."
            />
          </div>
          <div className="space-y-1">
            <Text14>City*</Text14>
            <TextInput value={city} onChange={(value) => setCity(value)} placeholder="London" />
          </div>
          <div className="space-y-1">
            <Text14>Province*</Text14>
            <TextInput
              value={province}
              onChange={(value) => setProvince(value)}
              placeholder="Ontario"
            />
          </div>
          <div className="space-y-1">
            <Text14>Country*</Text14>
            <TextInput
              value={"Canada"}
              onChange={(value) => value}
              placeholder="Canada"
              disabled={true}
              styleInput={{
                backgroundColor: Color.GALLERY,
                color: Color.BLACK_07,
              }}
            />
          </div>
          <div className="space-y-1">
            <Text14>Postal Code*</Text14>
            <TextInput value={postal} onChange={(value) => setPostal(value)} placeholder="Postal" />
          </div>
          <div className="space-y-1">
            <Text14>Phone*</Text14>
            <TextInput value={phone} onChange={(value) => setPhone(value)} placeholder="Phone" />
          </div>
        </div>
      }
      confirmButtonTitle="Save"
      cancelButtonTitle="Cancel"
      onClickConfirmButton={async () => {
        if (isNew) {
          await AddressService.create(street1, street2, city, province, postal, country);
        } else {
          await AddressService.update(
            props.selectedAddress!.addressId,
            street1,
            street2,
            city,
            province,
            postal,
            country
          );
        }
        await apiUpdateUserAddress({ phone: phone });
        props.onUpdate();
      }}
      onClickCancelButton={props.onOff}
      isConfirmDisabled={!isContinuable}
      isLoading={false}
    />
  );
};

export default AddressModal;
