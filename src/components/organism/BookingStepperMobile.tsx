import Image from "next/image";
import { Color } from "../../types/Color";

const steps = [
  {
    title: "Select Cars and Services",
    undoneIcon: <Image src={require("../../../public/select_date_undone.png")} />,
    doingIcon: <Image src={require("../../../public/select_service_doing.png")} />,
    doneIcon: <Image src={require("../../../public/booking_step_done.png")} />,
  },
  {
    title: "Add Service Location",
    undoneIcon: <Image src={require("../../../public/add_location_undone.png")} />,
    doingIcon: <Image src={require("../../../public/add_location_doing.png")} />,
    doneIcon: <Image src={require("../../../public/booking_step_done.png")} />,
  },
  {
    title: "Select Date and Time",
    undoneIcon: <Image src={require("../../../public/select_date_undone.png")} />,
    doingIcon: <Image src={require("../../../public/select_date_doing.png")} />,
    doneIcon: <Image src={require("../../../public/booking_step_done.png")} />,
  },
  {
    title: "Review and Pay",
    undoneIcon: <Image src={require("../../../public/review_pay_undone.png")} />,
    doingIcon: <Image src={require("../../../public/review_pay_doing.png")} />,
    doneIcon: <Image src={require("../../../public/booking_step_done.png")} />,
  },
];

const StepItem = (props: { icon: any; title: string }) => (
  <div className="flex flex-row items-center justify-self-stretch mr-2">
    <div
      className="flex flex-row justify-center rounded-full"
      style={{
        width: 32,
        height: 32,
        minWidth: 32,
      }}
    >
      {props.icon}
    </div>
  </div>
);

const BookingStepperMobile = (props: { style?: any; step: number }) => {
  return (
    <div className="flex flex-col justify-center bg-white h-12 w-full">
      <div
        className="flex px-5 py-2"
        style={{
          backgroundColor: Color.ATHENS_GRAY,
          borderBottomColor: Color.MISCHKA,
          borderBottomWidth: "0.5px",
          borderBottomStyle: "solid",
        }}
      >
        {steps.map((item, index) => (
          <>
            <StepItem
              key={index}
              title={item.title}
              icon={
                index < props.step
                  ? item.doneIcon
                  : index === props.step
                    ? item.doingIcon
                    : item.undoneIcon
              }
            />
            {index < 3 ? (
              <div className="flex flex-col justify-center flex-1 min-w-[20px] mr-2">
                <div
                  className="flex flex-row w-full h-0"
                  style={{
                    border: "0.5px solid #D0D5DD",
                    borderTop: 0,
                  }}
                />
              </div>
            ) : null}
          </>
        ))}
      </div>
    </div>
  );
};

export default BookingStepperMobile;
