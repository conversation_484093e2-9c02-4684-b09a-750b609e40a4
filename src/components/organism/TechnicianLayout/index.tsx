import { useRouter } from "next/router";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../../../@redux";
import { Color } from "../../../types/Color";
import MenuButton from "../../atom/Buttons/MenuButton";
import { ClipboardIcon, KeyIcon, PeopleIcon } from "../../atom/Icons";
import MenuSectionTitle from "../../atom/Texts/MenuSectionTitle";
import Layout from "../../Layout";
import Menu from "./../Menu/index";
import { useDispatch } from "react-redux";
import { thunkSignOutUser } from "../../../@redux/modules/auth/thunks";
import { RoleId } from "../../../types/User";

const TechnicianLayout = (props: { children: React.ReactNode }) => {
  const router = useRouter();
  const user = useSelector((state: RootState) => state.auth.user);
  const dispatch = useDispatch<any>();

  return (
    <Layout isTechnician>
      <div
        className="w-full h-full flex-1 flex"
        style={{
          backgroundColor: Color.LIGHT_BACKGROUND,
        }}
      >
        <div className="hidden lg:block h-full">
          <Menu>
            <div className="flex flex-col mx-8 self-stretch">
              {/* <div className="flex flex-col items-center self-stretch mb-7">
              <img src={user?.profilePictureUri} className="w-32 h-32 rounded-full mb-3" />
              <span className="font-medium mb-1">
                {user ? user.firstName + " " + user.lastName : ""}
              </span>
              <span className="text-xs text-[rgba(0,0,0,0.6)]">Technician</span>
            </div> */}
              <MenuSectionTitle>Booking</MenuSectionTitle>
              <MenuButton
                title="Bookings"
                icon={<ClipboardIcon />}
                onClick={() => {
                  router.push("/tech_booking");
                }}
                isSelected={router.pathname === "/tech_booking"}
              />
              {/* <MenuButton
                title="Reviews"
                icon={<StarIcon />}
                onClick={() => {
                  router.push("/");
                }}
                isSelected={router.pathname === "/"}
              /> */}

              <div
                className="self-stretch my-5"
                style={{
                  height: 1,
                  backgroundColor: Color.BLACK_01,
                }}
              />
              <MenuSectionTitle>Account</MenuSectionTitle>
              <MenuButton
                title="Profile"
                icon={<PeopleIcon />}
                onClick={() => {
                  router.push("/account");
                }}
                isSelected={
                  router.pathname === "/account" || router.pathname === "/change_password"
                }
              />
              {/* <MenuButton
                title="Settings"
                icon={<GearIcon />}
                onClick={() => {
                  router.push("/");
                }}
                isSelected={router.pathname === "/"}
              /> */}
              <div
                className="self-stretch my-5"
                style={{
                  height: 1,
                  backgroundColor: Color.BLACK_01,
                }}
              />
              {user?.roleId === RoleId.ADMIN && (
                <>
                  <MenuSectionTitle>Admin</MenuSectionTitle>
                  <MenuButton
                    title="Manage Technicians"
                    icon={<PeopleIcon />}
                    onClick={() => {
                      router.push("/manage_technicians");
                    }}
                    isSelected={router.pathname === "/manage_technicians"}
                  />
                  <MenuButton
                    title="Reset Password"
                    icon={<KeyIcon />}
                    onClick={() => {
                      router.push("/reset_passwords_admin");
                    }}
                    isSelected={router.pathname === "/reset_passwords_admin"}
                  />
                </>
              )}
              <MenuButton
                icon={undefined}
                title="Sign out"
                onClick={() => {
                  dispatch(thunkSignOutUser());
                  router.replace("/login");
                }}
                isSelected={false}
              />
              {/* <div className="flex flex-col mx-8">
            <MenuSectionTitle>Account</MenuSectionTitle>
            <MenuButton
              title="Rating and Reviews"
              onClick={() => {
                router.push("/tech_rating");
              }}
              isSelected={router.pathname === "/tech_rating"}
            />
            <MenuButton
              title="Earnings"
              onClick={() => {
                router.push("/tech_earning");
              }}
              isSelected={router.pathname === "/tech_earning"}
            />
          </div> */}
              {/* <div
            className="self-stretch my-5"
            style={{
              height: 1,
              backgroundColor: Color.BLACK_01,
            }}
          /> */}
            </div>
          </Menu>
        </div>
        <div className="flex-1 w-full">{props.children}</div>
      </div>
    </Layout>
  );
};

export default TechnicianLayout;
