import BackgroundSmallButton from "../../components/atom/Buttons/BackgroundSmallButton";
import SizeableImage from "../../components/atom/SizeableImage";
import { AddonCar } from "../../types/AddonCar";
import { BusinessRule } from "../../types/BusinessRule";
import { Color } from "../../types/Color";
import { ServiceCar } from "../../types/ServiceCar";
import { PRICE_PLACEHOLDER } from "../../values";
import WhiteButton from "../atom/Buttons/WhiteButton";
import Spinner from "../atom/Spinner";

interface MobileOrderSummaryProps {
  orderSummaryHook: any;
  serviceCars: ServiceCar[] | undefined;
  isFetchLoading?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  onClick: (e?: React.MouseEvent) => void;
  buttonTitle: string;
  GobackButton?: {
    onClick: () => void;
    isLoading?: boolean;
    disabled?: boolean;
  };
  fourPage?: {
    addonCars: AddonCar[] | undefined;
    remainingTime: string;
  };
}

const MobileOrderSummary = (props: MobileOrderSummaryProps) => {
  return (
    <div className="flex flex-col absolute w-full bottom-0">
      <div className="bg-[#FAFAFA] w-full md:hidden">
        {props.fourPage ? (
          <div
            className="flex flex-col border-t-[0.5px]"
            style={{
              borderColor: Color.SLIVER_GRAY,
            }}
          >
            <div className="flex items-center justify-between px-4 py-2">
              <div className="flex space-x-2 items-center">
                <span
                  className=""
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: Color.INDIGO_BLUE_07,
                  }}
                >
                  Complete your checkout in
                </span>
              </div>
              <div
                className="flex items-center justify-center rounded-full py-1"
                style={{
                  background: Color.CORAL_RED,
                  width: 60,
                }}
              >
                <span
                  style={{
                    fontWeight: 400,
                    fontSize: 14,
                    color: Color.WHITE,
                  }}
                >
                  {props.fourPage.remainingTime}
                </span>
              </div>
            </div>
            <div className="border-b" />
          </div>
        ) : null}
        <div
          className="flex flex-row justify-between p-4 pb-2"
          style={{
            borderTopWidth: props.fourPage ? 0 : 0.5,
            borderColor: Color.SLIVER_GRAY,
          }}
        >
          <span
            className=""
            style={{
              fontWeight: "600",
              fontStyle: "normal",
              fontSize: 20,
              letterSpacing: "-0.04em",
            }}
          >
            Order Summary
          </span>
          <button
            className="flex items-center justify-center"
            onClick={() => {
              props.orderSummaryHook.setIsFolded(!props.orderSummaryHook.isFolded);
            }}
          >
            {props.orderSummaryHook.isFolded ? (
              <SizeableImage
                src={require("../../../public/vector_stretched_black.png")}
                size={18}
              />
            ) : (
              <SizeableImage src={require("../../../public/vector_folded_black.png")} size={18} />
            )}
          </button>
        </div>
        {props.isFetchLoading && !props.orderSummaryHook.isFolded ? (
          <div className="w-full h-full flex flex-col justify-center items-center bg-[#f8fafb]">
            <div className="flex flex-col items-center">
              <Spinner />
            </div>
          </div>
        ) : (
          <div
            className="flex-col overflow-y-scroll px-4 min-h-[70px] justify-between"
            style={{
              display: props.orderSummaryHook.isFolded ? "none" : "flex",
              height:
                props.GobackButton && props.fourPage
                  ? "calc((var(--vh, 1vh) * 100 - 276px))"
                  : !props.GobackButton && props.fourPage
                  ? "calc((var(--vh, 1vh) * 100 - 276px))"
                  : props.GobackButton && !props.fourPage
                  ? "calc((var(--vh, 1vh) * 100 - 230px))"
                  : !props.GobackButton && !props.fourPage
                  ? "calc((var(--vh, 1vh) * 100 - 230px))"
                  : "",
            }}
          >
            {props.serviceCars && props.serviceCars.length !== 0 ? (
              <div className="flex flex-col">
                <span
                  className="text-sm font-bold"
                  style={{
                    color: Color.INDIGO_BLUE,
                    opacity: 0.8,
                  }}
                >
                  Items
                </span>
                <div className="border-t border-t-[#E6E6E8] w-full mt-2 mb-3" />
                <div className="flex flex-col space-y-3">
                  {props.serviceCars
                    ? props.serviceCars.map((carService, index) => (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center ">
                            <span
                              className="mr-3 text-sm"
                              style={{
                                color: Color.INDIGO_BLUE,
                                opacity: 0.6,
                              }}
                            >
                              {carService.cars.length}x
                            </span>
                            <span
                              className="font-medium text-sm"
                              style={{
                                color: Color.INDIGO_BLUE,
                              }}
                            >
                              {carService.service.name}
                            </span>
                          </div>
                          <span
                            className="text-sm font-bold"
                            style={{
                              color: Color.INDIGO_BLUE,
                            }}
                          >
                            $
                            {Number(
                              BusinessRule.calculateEachTargetPrice(
                                carService.service.price,
                                carService.cars.length
                              )
                            ).toFixed(2)}
                          </span>
                        </div>
                      ))
                    : null}
                </div>
              </div>
            ) : null}

            {props.fourPage ? (
              <>
                {props.fourPage.addonCars && props.fourPage.addonCars.length !== 0 ? (
                  <div className="flex flex-col">
                    <span
                      className="text-sm font-bold"
                      style={{
                        color: Color.INDIGO_BLUE,
                        opacity: 0.8,
                      }}
                    >
                      Add-Ons
                    </span>
                    <div className="border-t border-t-[#E6E6E8] w-full mt-2 mb-3" />
                    <div className="flex flex-col space-y-3">
                      {props.fourPage.addonCars.map((addonCar, index) => (
                        <div className="flex items-center justify-between ">
                          <div className="flex items-center ">
                            <span
                              className="mr-3 text-sm"
                              style={{
                                color: Color.INDIGO_BLUE,
                                opacity: 0.6,
                              }}
                            >
                              {addonCar.cars.length}x
                            </span>
                            <span
                              className="font-medium text-sm"
                              style={{
                                color: Color.INDIGO_BLUE,
                              }}
                            >
                              {addonCar.addon.name}
                            </span>
                          </div>
                          <span
                            className="text-sm font-bold"
                            style={{
                              color: Color.INDIGO_BLUE,
                            }}
                          >
                            $
                            {Number(
                              BusinessRule.calculateEachTargetAddonPrice(
                                addonCar.addon.cost,
                                addonCar.cars.length
                              )
                            ).toFixed(2)}
                          </span>
                        </div>
                      ))}
                    </div>
                    <div className="border-t border-t-[#E6E6E8] w-full mt-3 mb-3" />
                  </div>
                ) : null}
              </>
            ) : null}
            <div className="">
              <div className="border-t border-t-[#E6E6E8] w-full mt-3 mb-3" />
              <div className="flex justify-between items-center mb-3">
                <span
                  className="text-sm"
                  style={{
                    color: Color.INDIGO_BLUE,
                    opacity: 0.6,
                  }}
                >
                  Price
                </span>
                <span
                  className="text-sm font-medium"
                  style={{
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  {`$${Number(props.orderSummaryHook.orderSummaryPrices.servicePrice).toFixed(2)}`}
                </span>
              </div>
              <div className="flex justify-between items-center mb-3">
                <span
                  className="text-sm"
                  style={{
                    color: Color.INDIGO_BLUE,
                    opacity: 0.6,
                  }}
                >
                  Travel Fee
                </span>
                <span
                  className="text-sm font-medium"
                  style={{
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  {props.orderSummaryHook.orderSummaryPrices.travelFees > 0
                    ? `-$${Number(props.orderSummaryHook.orderSummaryPrices.travelFees).toFixed(2)}`
                    : PRICE_PLACEHOLDER}
                </span>
              </div>
              {props.fourPage ? (
                <>
                  <div
                    className="flex justify-between items-center"
                    style={{
                      marginBottom: 12,
                    }}
                  >
                    <span
                      className="text-sm"
                      style={{
                        color: Color.INDIGO_BLUE,
                        opacity: 0.6,
                      }}
                    >
                      Add-ons
                    </span>
                    <span
                      className="text-sm font-medium"
                      style={{
                        color: Color.INDIGO_BLUE,
                      }}
                    >
                      ${Number(props.orderSummaryHook.orderSummaryPrices.addonPrice).toFixed(2)}
                    </span>
                  </div>
                  <div
                    className="flex justify-between items-center"
                    style={{
                      marginBottom: 12,
                    }}
                  >
                    <span
                      className="text-sm"
                      style={{
                        color: Color.INDIGO_BLUE,
                        opacity: 0.6,
                      }}
                    >
                      Subtotal
                    </span>
                    <span
                      className="text-sm font-medium"
                      style={{
                        color: Color.INDIGO_BLUE,
                      }}
                    >
                      ${Number(props.orderSummaryHook.orderSummaryPrices.subtotalPrice).toFixed(2)}
                    </span>
                  </div>
                  <div
                    className="flex justify-between items-center"
                    style={{
                      marginBottom: 18,
                    }}
                  >
                    <span
                      className="text-sm"
                      style={{
                        color: Color.INDIGO_BLUE,
                        opacity: 0.6,
                      }}
                    >
                      Tax
                    </span>
                    <span
                      className="text-sm font-medium"
                      style={{
                        color: Color.INDIGO_BLUE,
                      }}
                    >
                      ${Number(props.orderSummaryHook.orderSummaryPrices.taxPrice).toFixed(2)}
                    </span>
                  </div>
                </>
              ) : null}
              <div className="flex justify-between items-center">
                <span
                  className="font-bold"
                  style={{
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  {props.fourPage ? "Total" : "Subtotal"}
                </span>
                <span
                  className="font-bold"
                  style={{
                    color: Color.INDIGO_BLUE,
                  }}
                >
                  $
                  {props.fourPage
                    ? Number(props.orderSummaryHook.orderSummaryPrices.totalPrice).toFixed(2)
                    : Number(props.orderSummaryHook.orderSummaryPrices.subtotalPrice).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        )}
        <div className="flex flex-col p-4 gap-3">
          {props.fourPage ? (
            <div className="flex flex-row justify-between items-center w-full gap-x-4">
              {props.GobackButton ? (
                <>
                  <WhiteButton
                    title="Go Back"
                    onClick={props.GobackButton.onClick}
                    isLoading={props.GobackButton.isLoading}
                    disabled={props.GobackButton.disabled}
                    style={{
                      width: "100%",
                      paddingTop: 10,
                      paddingBottom: 10,
                      justifyContent: "center",
                    }}
                  />
                  <BackgroundSmallButton
                    title={props.buttonTitle}
                    onClick={props.onClick}
                    backgroundColor="BLACK_PEARL"
                    style={{
                      height: 42,
                      width: "100%",
                    }}
                    isLoading={props.isLoading}
                    disabled={props.disabled}
                  />
                </>
              ) : (
                <BackgroundSmallButton
                  title={props.buttonTitle}
                  onClick={props.onClick}
                  backgroundColor="BLACK_PEARL"
                  style={{
                    width: "100%",
                    height: 42,
                  }}
                  isLoading={props.isLoading}
                  disabled={props.disabled}
                />
              )}
            </div>
          ) : (
            <div className="flex flex-row justify-between items-center w-full gap-x-4">
              {props.GobackButton ? (
                <>
                  <WhiteButton
                    title="Go Back"
                    onClick={props.GobackButton.onClick}
                    isLoading={props.GobackButton.isLoading}
                    disabled={props.GobackButton.disabled}
                    style={{
                      width: "100%",
                      paddingTop: 10,
                      paddingBottom: 10,
                      justifyContent: "center",
                    }}
                  />
                  <BackgroundSmallButton
                    title={props.buttonTitle}
                    onClick={props.onClick}
                    backgroundColor="BLACK_PEARL"
                    style={{
                      width: "100%",
                      height: 42,
                    }}
                    isLoading={props.isLoading}
                    disabled={props.disabled}
                  />
                </>
              ) : (
                <BackgroundSmallButton
                  title={props.buttonTitle}
                  onClick={props.onClick}
                  backgroundColor="BLACK_PEARL"
                  style={{
                    width: "100%",
                    height: 42,
                  }}
                  isLoading={props.isLoading}
                  disabled={props.disabled}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileOrderSummary;
