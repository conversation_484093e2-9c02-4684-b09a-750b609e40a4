import { useEffect, useState } from "react";
import { apiGetUser } from "../../functions/api/user";
import { useAddresses } from "../../hooks/useAddresses";
import BorderButton from "../atom/Buttons/BorderButton";
import { PhoneIcon } from "../atom/Icons";
import GrayText14 from "../atom/Texts/GrayText14";
import Text14 from "../atom/Texts/base/Text14";
import TitleWithIcon from "../atom/TitleWithIcon";
import AddressModal from "./AddressModal";

interface AddressSelectorProps {
  addressHook: ReturnType<typeof useAddresses>;
  style?: React.CSSProperties;
}

const AddressSelector = (props: AddressSelectorProps) => {
  const [isAddressModalOn, setIsAddressModalOn] = useState<boolean>(false);
  const { addresses, fetch } = props.addressHook;
  const [phone, setPhone] = useState<string>("");

  useEffect(() => {
    fetchPhone();
  }, []);

  async function fetchPhone() {
    const user = await apiGetUser();
    setPhone(user.phone === null ? "" : user.phone);
  }

  return (
    <div className="flex flex-col px-4 xl:px-8" style={props.style}>
      <AddressModal
        isOn={isAddressModalOn}
        onOff={() => {
          setIsAddressModalOn(false);
        }}
        onUpdate={() => {
          fetch();
          fetchPhone();
          setIsAddressModalOn(false);
        }}
        selectedAddress={addresses && addresses.length > 0 ? addresses[0] : null}
        selectedPhone={phone || ""}
      />
      <div className="flex flex-col mb-5">
        <TitleWithIcon
          title="Address"
          iconSrc={require("../../../public/title_icon_home.png")}
          style={{
            marginBottom: 20,
          }}
        />
        {addresses && addresses.length > 0 ? (
          <>
            <span>{addresses[0].street1}</span>
            <span>{addresses[0].street2}</span>
            <span>
              {addresses[0].city}, {addresses[0].province}, {addresses[0].country}
            </span>
            <span>{addresses[0].postal}</span>
          </>
        ) : null}
        <div className="flex flex-row gap-3">
          <PhoneIcon />
          <Text14>{phone}</Text14>
        </div>
      </div>
      <GrayText14
        style={{
          marginBottom: 20,
        }}
      >
        Address cannot be modified after this stage since technicians will be based on your
        location.
      </GrayText14>
      <BorderButton title="Edit" onClick={() => setIsAddressModalOn(true)} />
    </div>
  );
};

export default AddressSelector;
