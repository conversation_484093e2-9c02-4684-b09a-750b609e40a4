import { useState } from "react";
import { ReviewService } from "../../services/ReviewService";
import ResizingFullModal from "../Modal/ResizingFullModal";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import SizeableImage from "../atom/SizeableImage";
import Title18 from "../atom/Texts/base/Title18";

interface CustomerReviewModalProps {
  isOn: boolean;
  onOff: () => void;
  bookingId: number;
  // should be changed to processing on BE.
  serviceNames: string[];
  fetchBooking: () => void;
}

const CustomerReviewModal = (props: CustomerReviewModalProps) => {
  const [descriptionInput, setDescriptionInput] = useState<string>("");
  const [rating, setRating] = useState<number>(5);
  const [isDropDownShown, setIsDropDownShown] = useState<boolean>(false);

  const WEB_MODAL_WIDTH = 600;
  const WEB_MODAL_HEIGHT = 400;

  function getStar(count: number) {
    return (
      <div className="flex items-center">
        {Array(count)
          .fill(0)
          .map((_) => (
            <span className="text-lg">★</span>
          ))}
      </div>
    );
  }

  function onOffAndReset() {
    setDescriptionInput("");
    setRating(5);
    props.onOff();
  }

  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={onOffAndReset}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <div className="flex flex-col bg-white md:rounded-lg w-full h-full">
        <div className="relative flex items-center justify-center border-b p-4">
          <div className="flex flex-col items-center">
            <Title18 style={{ marginBottom: 4 }}>Leave a Review</Title18>
          </div>
          <button className="absolute right-8" onClick={props.onOff}>
            <SizeableImage src={require("../../../public/close.png")} size={24} />
          </button>
        </div>
        <div className="flex flex-col p-5 pr-4 overflow-y-scroll h-full pb-8">
          <div className="relative mb-5">
            <span className="text-sm mb-2">Rating</span>
            <div
              className="flex flex-row justify-between border px-2 pb-2 pt-1 items-center text-yellow-500"
              onClick={() => setIsDropDownShown((prev) => !prev)}
            >
              {getStar(rating)}
              <div className="pt-1">
                {isDropDownShown ? (
                  <SizeableImage src={require("../../../public/vector_folded.png")} size={24} />
                ) : (
                  <SizeableImage src={require("../../../public/vector_stretched.png")} size={24} />
                )}
              </div>
            </div>
            <div
              className="absolute border w-full flex-col "
              style={{
                top: 64,
                display: isDropDownShown ? "flex" : "none",
              }}
            >
              {[5, 4, 3, 2, 1].map((item) => (
                <button
                  className="cursor-default bg-white hover:bg-gray-200 text-yellow-500 px-2 z-10"
                  onClick={() => {
                    setRating(item);
                    setIsDropDownShown(false);
                  }}
                >
                  {getStar(item)}
                </button>
              ))}
            </div>
          </div>
          <span className="text-sm mb-1">Comment</span>
          <textarea
            className="border min-h-[90px]"
            value={descriptionInput}
            onChange={(e) => setDescriptionInput(e.target.value.trimStart())}
          />
          {/* <BorderButton
          title="Submit"
          onClick={async () => {
            await ReviewService.create(props.bookingId, rating, descriptionInput, props.serviceNames)
            props.onOff()
          }}
        /> */}
        </div>
        <div className="flex p-5 pr-8 border-t border-gray-200">
          <BackgroundSmallButton
            title="Submit"
            backgroundColor="DARK_BLUE"
            onClick={async () => {
              await ReviewService.create(
                props.bookingId,
                rating,
                descriptionInput,
                props.serviceNames
              );
              onOffAndReset();
              props.fetchBooking();
            }}
            disabled={descriptionInput === ""}
            style={{
              width: "100%",
            }}
          />
        </div>
      </div>
    </ResizingFullModal>
  );
};

export default CustomerReviewModal;
