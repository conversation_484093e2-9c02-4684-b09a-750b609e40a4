import Image from "next/image";
import { useRouter } from "next/router";
import LogoButton from "../../atom/Buttons/LogoButton";
import { Color } from "../../../types/Color";

const BecomeTechPart = (props: {}) => {
  return (
    <div
      className="flex flex-row items-center justify-between px-5 py-4"
      style={{
        height: 58,
        backgroundColor: Color.MYSTIC,
      }}
    >
      <div className="flex flex-row items-center justify-center">
        <span
          className="flex flex-col justify-center"
          style={{
            color: Color.SHUTTLE_GRAY,
            fontSize: 16,
            fontWeight: 400,
            marginRight: 4,
            lineHeight: 1,
          }}
        >
          Are you technician?
        </span>
        <span
          className="flex flex-col justify-center"
          style={{ color: Color.BLACK_PEARL, fontSize: 16, fontWeight: 600, lineHeight: 1 }}
        >
          Become a technician
        </span>
      </div>
      <button
        className="w-5 h-5"
        //TO DO onClick implement
      >
        <Image src={require("../../../../public/vector_right_auth.png")} />
      </button>
    </div>
  );
};

const AuthHeaderMobile = (props: { technicianHeader?: boolean }) => {
  const router = useRouter();

  return (
    <div className="flex flex-col w-full xl:hidden">
      <div
        className="flex justify-between items-center w-full px-4"
        style={{
          paddingTop: 15,
          paddingBottom: 15,
          height: 58,
          background: Color.BLACK_PEARL,
        }}
      >
        <div className="flex-1 mr-4">
          <LogoButton onClick={() => router.push("/login")} />
        </div>
        <p style={{ color: "white" }}>
          Have any questions? Call us at{" "}
          <a href={`tel:${+15198704546}`} className="underline">
            (*************
          </a>
        </p>
      </div>
      {/* {props.technicianHeader ? <BecomeTechPart /> : null} */}
    </div>
  );
};

export default AuthHeaderMobile;
