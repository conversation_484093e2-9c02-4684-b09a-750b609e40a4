import { useRouter } from "next/router";
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { thunkSignOutUser } from "../../../@redux/modules/auth/thunks";
import PrivacyPolicyModal from "../../Modal/PrivacyPolicyModal";
import TermsOfUseModal from "../../Modal/TermsOfUseModal";
import Layout from "../../Layout";
import MenuButton from "../../atom/Buttons/MenuButton";
import { ClipboardIcon, HeadphoneIcon, LockIcon, PeopleIcon } from "../../atom/Icons";
import MenuSectionTitle from "../../atom/Texts/MenuSectionTitle";
import Menu from "../Menu";
import { Color } from "../../../types/Color";
import { apiDeleteUser } from "../../../functions/cognito";

const CustomerLayout = (props: { children: React.ReactNode }) => {
  const router = useRouter();
  const dispatch = useDispatch<any>();

  const [isOnPrivacyPolicy, setIsOnPrivacyPolicy] = useState<boolean>(false);
  const [isOnTermsOfUse, setIsOnTermsOfuse] = useState<boolean>(false);

  return (
    <Layout>
      <PrivacyPolicyModal isOn={isOnPrivacyPolicy} onOff={() => setIsOnPrivacyPolicy(false)} />
      <TermsOfUseModal isOn={isOnTermsOfUse} onOff={() => setIsOnTermsOfuse(false)} />
      <div className="w-full h-full flex-1 flex bg-gray-50">
        <div className="hidden lg:block">
          <Menu>
            <div className="w-full flex flex-col self-stretch px-3">
              <MenuSectionTitle style={{ paddingLeft: 12, paddingRight: 12 }}>
                Our Booking
              </MenuSectionTitle>
              <MenuButton
                title="Bookings"
                icon={<ClipboardIcon />}
                onClick={() => {
                  router.push("/customer_booking");
                }}
                isSelected={router.pathname === "/customer_booking"}
              />
            </div>
            <div
              className="self-stretch my-5 mx-5"
              style={{
                height: 1,
                backgroundColor: Color.BLACK_01,
              }}
            />
            <div className="w-full flex flex-col self-stretch px-3">
              <MenuSectionTitle style={{ paddingLeft: 12, paddingRight: 12 }}>
                My Account
              </MenuSectionTitle>
              <MenuButton
                title="Profile"
                icon={<PeopleIcon />}
                onClick={() => {
                  router.push("/customer_profile");
                }}
                isSelected={
                  router.pathname === "/customer_profile" || router.pathname === "/change_password"
                }
              />
            </div>
            <div
              className="self-stretch my-5 mx-5"
              style={{
                height: 1,
                backgroundColor: Color.BLACK_01,
              }}
            />
            <div className="w-full flex flex-col self-stretch px-3">
              <MenuButton
                title="Privacy Policy"
                icon={<LockIcon />}
                onClick={() => {
                  setIsOnPrivacyPolicy(true);
                }}
                isSelected={false}
              />
              <MenuButton
                title="Terms of Use"
                icon={<HeadphoneIcon />}
                onClick={() => {
                  setIsOnTermsOfuse(true);
                }}
                isSelected={false}
              />
              <MenuButton
                icon={undefined}
                title="Sign out"
                onClick={() => {
                  dispatch(thunkSignOutUser());
                  router.replace("/login");
                }}
                isSelected={false}
              />
              {/* <MenuButton
                icon={undefined}
                title="Delete Account"
                onClick={() => {
                  apiDeleteUser();
                  router.replace("/login");
                }}
                isSelected={false}
              /> */}
            </div>
          </Menu>
        </div>
        {props.children}
      </div>
    </Layout>
  );
};

export default CustomerLayout;
