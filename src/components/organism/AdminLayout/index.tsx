import { useRouter } from "next/router";
import React from "react";
import Layout from "../../Layout";
import { Color } from "../../../types/Color";

const MenuItem = (props: { title: string; isSelected: boolean; onClick: () => void }) => (
  <button className="text-left mb-5" onClick={props.onClick}>
    <span
      style={{
        color: props.isSelected ? Color.WHITE : Color.WHITE_06,
        fontWeight: props.isSelected ? 500 : 400,
      }}
    >
      {props.title}
    </span>
  </button>
);

const AdminLayout = (props: { children: React.ReactNode }) => {
  const router = useRouter();

  return (
    <Layout>
      <div className="w-full h-full flex items-stretch">
        <div
          className="bg-[rgba(0,0,0,0.8)] pt-10 pl-8 flex flex-col"
          style={{
            width: 280,
          }}
        >
          <h4 className="text-xl text-white font-bold mb-10">Management</h4>
          <MenuItem
            title="Services"
            isSelected={router.pathname === "/admin_service"}
            onClick={() => {
              router.push("/admin_service");
            }}
          />
          <MenuItem
            title="Technicians"
            isSelected={router.pathname === "/admin_technicians"}
            onClick={() => {
              router.push("/admin_technicians");
            }}
          />
          <MenuItem
            title="Addons"
            isSelected={router.pathname === "/admin_addons"}
            onClick={() => {
              router.push("/admin_addons");
            }}
          />
        </div>
        {props.children}
      </div>
    </Layout>
  );
};

export default AdminLayout;
