import Image from "next/image";
import { useEffect, useMemo, useState } from "react";
import ReviewItem from "../../pageComponents/availability/ReviewItem";
import { ReviewService } from "../../services/ReviewService";
import { Color } from "../../types/Color";
import { Review } from "../../types/Review";
import { Technician } from "../../types/Technician";
import ResizingFullModal from "../Modal/ResizingFullModal";
import SizeableImage from "../atom/SizeableImage";
import Spinner from "../atom/Spinner";
import GrayText14 from "../atom/Texts/GrayText14";
import Title18 from "../atom/Texts/base/Title18";

interface PreviewReviewModalProps {
  isOn: boolean;
  onOff: () => void;
  selectedTechnician: Technician | null;
}

const PreviewReviewModal = (props: PreviewReviewModalProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [reviews, setReviews] = useState<Review[]>([]);

  const WEB_MODAL_WIDTH = 848;
  const WEB_MODAL_HEIGHT = 674;

  useEffect(() => {
    if (props.selectedTechnician !== null && props.selectedTechnician.technicianId) {
      fetchReviews(props.selectedTechnician.technicianId);
    }
  }, [props.selectedTechnician]);

  const technicianInfo = useMemo(() => {
    if (reviews.length > 0) {
      return reviews[0].technicianInfo;
    }
    return null;
  }, [reviews]);

  async function fetchReviews(id: number) {
    setIsLoading(true);
    const result = await ReviewService.getByTechnicianId(id);
    setReviews(result);
    setIsLoading(false);
  }

  return (
    <>
      <ResizingFullModal
        isOn={props.isOn}
        onOff={props.onOff}
        modalSize={{
          web: {
            width: WEB_MODAL_WIDTH,
            height: WEB_MODAL_HEIGHT,
          },
        }}
      >
        <div
          className="flex flex-col bg-white md:rounded-lg w-full h-full"
          style={{
            border: "1px solid rgba(0,0,0,0.15)",
            boxShadow: "0px 5px 15px rgba(0,0,0,0.05)",
          }}
        >
          <div className="relative flex items-center justify-center border-b p-4">
            <div className="flex flex-col items-center">
              <Title18 style={{ marginBottom: 4 }}>Reviews</Title18>
            </div>
            <button className="absolute right-5" onClick={props.onOff}>
              <SizeableImage src={require("../../../public/close.png")} size={24} />
            </button>
          </div>
          {!isLoading ? (
            <div className="overflow-y-scroll">
              {/* <div className="flex flex-col self-stretch p-6">
                {props.selectedTechnician !== undefined && technicianInfo !== null ? (
                  <>
                    <div className="flex flex-row mb-4">
                      {props.selectedTechnician?.profilePictureUri ? (
                        <img
                          src={props.selectedTechnician?.profilePictureUri}
                          style={{
                            width: 60,
                            height: 60,
                            marginRight: 16,
                          }}
                          className="rounded-md"
                        />
                      ) : (
                        <div
                          className="flex items-center justify-center rounded-md"
                          style={{
                            width: 60,
                            height: 60,
                            marginRight: 16,
                            backgroundColor: Color.GRAY,
                          }}
                        />
                      )}
                      <div className="flex flex-col text-left justify-center">
                        <dt className="mb-1" style={{ fontSize: 16, fontWeight: "500" }}>
                          {props.selectedTechnician?.firstName +
                            " " +
                            props.selectedTechnician?.lastName}
                        </dt>
                        <div className="flex items-center">
                          {technicianInfo.rating !== null ? (
                            <>
                              <div
                                className="flex items-center justify-center mr-1"
                                style={{
                                  width: 12,
                                  height: 12,
                                  marginBottom: 2,
                                }}
                              >
                                <Image src={require("../../../public/yellow_star.png")} />
                              </div>
                              <span
                                style={{
                                  fontSize: 13,
                                  marginRight: 8,
                                }}
                              >
                                {technicianInfo.rating}
                              </span>
                            </>
                          ) : null}
                          <div
                            style={{
                              color: Color.ACCENT,
                              fontSize: 13,
                            }}
                          >
                            ({technicianInfo.numOfReviews} Review
                            {technicianInfo.numOfReviews > 1 ? "s" : ""})
                          </div>
                        </div>
                      </div>
                    </div>
                    <span>{props.selectedTechnician?.biography}</span>
                  </>
                ) : null}
              </div>
              <div className="border-b" /> */}
              <div className="flex flex-col px-6 pt-6">
                <div className="flex flex-row mb-2">
                  <div className="mr-2">
                    <SizeableImage src={require("../../../public/review_star.png")} size={24} />
                  </div>
                  <span className="text-[18px]" style={{ fontWeight: 600 }}>
                    Reviews
                  </span>
                </div>
                {props.selectedTechnician !== undefined && technicianInfo !== null ? (
                  <GrayText14>
                    Result: {technicianInfo.numOfReviews} Review
                    {technicianInfo.numOfReviews > 1 ? "s" : ""}
                  </GrayText14>
                ) : null}
              </div>
              <div className="px-6">
                {reviews?.map((review, index) => (
                  <ReviewItem item={review} />
                ))}
              </div>
            </div>
          ) : (
            <div className="w-full h-full flex flex-col justify-center items-center bg-white">
              <div className="flex flex-col items-center">
                <Spinner />
              </div>
            </div>
          )}
        </div>
      </ResizingFullModal>
    </>
  );
};

export default PreviewReviewModal;
