import Image from "next/image";
import { Color } from "../../../types/Color";

const steps = [
  {
    title: "Select Cars and Services",
    undoneIcon: <Image src={require("../../../../public/select_date_undone.png")} />,
    doingIcon: <Image src={require("../../../../public/select_service_doing.png")} />,
    doneIcon: <Image src={require("../../../../public/booking_step_done.png")} />,
  },
  {
    title: "Add Service Location",
    undoneIcon: <Image src={require("../../../../public/add_location_undone.png")} />,
    doingIcon: <Image src={require("../../../../public/add_location_doing.png")} />,
    doneIcon: <Image src={require("../../../../public/booking_step_done.png")} />,
  },
  {
    title: "Select Date and Time",
    undoneIcon: <Image src={require("../../../../public/select_date_undone.png")} />,
    doingIcon: <Image src={require("../../../../public/select_date_doing.png")} />,
    doneIcon: <Image src={require("../../../../public/booking_step_done.png")} />,
  },
  {
    title: "Review and Pay",
    undoneIcon: <Image src={require("../../../../public/review_pay_undone.png")} />,
    doingIcon: <Image src={require("../../../../public/review_pay_doing.png")} />,
    doneIcon: <Image src={require("../../../../public/booking_step_done.png")} />,
  },
];

const StepItem = (props: { icon: any; title: string }) => (
  <div className="flex flex-row items-center justify-self-stretch">
    <div
      className="flex flex-row justify-center rounded-full mr-2"
      style={{
        width: 32,
        height: 32,
        minWidth: 32,
      }}
    >
      {props.icon}
    </div>
    <span
      className="hidden lg:flex"
      style={{
        fontWeight: 500,
        fontSize: 14,
        lineHeight: "135%",
        color: Color.INDIGO_BLUE,
      }}
    >
      {props.title}
    </span>
  </div>
);

const BookingStepper = (props: { style?: any; step: number }) => {
  // console.log("We're in  bookingstepper")
  return (
    <div
      className="flex flex-col justify-center bg-white h-14 w-full py-4 px-4"
      style={{
        border: "0.5px solid #E6E6E8",
      }}
    >
      <div className="flex mx-auto">
        {steps.map((item, index) => (
          <>
            <StepItem
              key={index}
              title={item.title}
              icon={
                index < props.step
                  ? item.doneIcon
                  : index === props.step
                  ? item.doingIcon
                  : item.undoneIcon
              }
            />
            {index < 3 ? (
              <div className="flex flex-col justify-center w-[120px] mx-4 lg:max-xl:w-[74px] lg:max-xl:mx-2">
                <div
                  className="flex flex-row h-0"
                  style={{
                    border: "0.5px solid #D0D5DD",
                    borderTop: 0,
                  }}
                />
              </div>
            ) : null}
          </>
        ))}
      </div>
    </div>
  );
};

export default BookingStepper;
