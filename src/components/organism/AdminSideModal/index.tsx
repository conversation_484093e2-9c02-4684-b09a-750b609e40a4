import React from "react";

interface AdminSideModalProps {
  isOn: boolean;
  children: React.ReactNode;
}

const AdminSideModal = (props: AdminSideModalProps) => {
  return (
    <div
      className="bg-[#f9fbfc] border-l p-10 flex flex-col justify-between"
      style={{
        width: 500,
        display: props.isOn ? "flex" : "none",
      }}
    >
      {props.children}
    </div>
  );
};

export default AdminSideModal;
