import { Color } from "../../../types/Color";
import ResizingFullModal from "../../Modal/ResizingFullModal";
import SizeableImage from "../../atom/SizeableImage";
import Text14 from "../../atom/Texts/base/Text14";
import Title18 from "../../atom/Texts/base/Title18";

const ServiceInfoModal = (props: {
  isOn: boolean;
  onOff: () => void;
  name: string;
  description: string;
  onClick: () => void;
  isSelected: boolean;
  price: number;
  servicePictureUri: string;
}) => {
  const WEB_MODAL_WIDTH = 400;
  const WEB_MODAL_HEIGHT = 600;

  function onCloseModal() {
    props.onOff();
  }
  return (
    <ResizingFullModal
      isOn={props.isOn}
      onOff={onCloseModal}
      modalSize={{
        web: {
          width: WEB_MODAL_WIDTH,
          height: WEB_MODAL_HEIGHT,
        },
      }}
    >
      <>
        <div
          className="flex flex-col align-center h-full md:h-5/6 justify-between bg-white pt-4"
          style={{
            boxShadow: "0px 5px 15px rgba(0,0,0,0.05)",
          }}
        >
          <div className="h-5/6 overflow-y-scroll">
            <div className="flex bg-white sticky top-0 items-center justify-between pb-4 border-b border-b-gray">
              <div />
              <Title18>Service Info</Title18>
              <button onClick={onCloseModal} className="mr-5">
                <SizeableImage src={require("../../../../public/close.png")} size={24} />
              </button>
            </div>
            <div className="flex flex-col overflow-y-scroll w-full divide-t-[1px]">
              <div className="w-full">
                <img
                  className="object-cover w-full"
                  src={props.servicePictureUri}
                  alt={`${props.name} service image`}
                />
              </div>
              <div className="flex flex-row justify-between px-4 py-5">
                <div className="flex flex-col">
                  <dt
                    className="leading-snug font-semibold"
                    style={{ fontSize: 16, marginBottom: 2, color: Color.BLACK_08 }}
                  >
                    {props.name}
                  </dt>
                  <dd
                    className="opacity-60"
                    style={{
                      fontSize: 14,
                    }}
                  >
                    Mechanical
                  </dd>
                </div>
                <div className="flex flex-row justify-end">
                  <Text14
                    style={{ marginTop: 5, marginRight: 1 }}
                    className="flex flex-col justify-center md:justify-start"
                  >
                    $
                  </Text14>
                  <dd
                    className="text-2xl font-semibold flex flex-col justify-center md:justify-start"
                    style={{
                      color: Color.NORMAL_BLACK,
                    }}
                  >
                    {(props.price / 100 + 15).toFixed(0)}
                  </dd>
                </div>
              </div>
              <p className="px-4 font-light">{props.description}</p>
            </div>
          </div>
          <div className="flex flex-col w-full p-4">
            <button
              className={`${
                props.isSelected ? "bg-[#FEE9E9] text-[#FF4242]" : "bg-[#FCA311] text-white"
              } py-3 px-4 rounded-sm flex flex-col place-items-center justify-center`}
              onClick={props.onClick}
            >
              {props.isSelected ? "Remove Service" : "Select Service"}
            </button>
          </div>
        </div>
      </>
    </ResizingFullModal>
  );
};

export default ServiceInfoModal;
