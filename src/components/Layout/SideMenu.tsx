import Image from "next/image";
import { useRouter } from "next/router";
import { useState } from "react";
import { useSelector } from "react-redux";
import "react-toastify/dist/ReactToastify.css";
import { RootState } from "../../@redux";
import PrivacyPolicyModal from "../Modal/PrivacyPolicyModal";
import TermsOfUseModal from "../Modal/TermsOfUseModal";
import BackgroundSmallButton from "../atom/Buttons/BackgroundSmallButton";
import MenuButton from "../atom/Buttons/MenuButton";
import { ClipboardIcon, HeadphoneIcon, LockIcon, PeopleIcon } from "../atom/Icons";
import MenuSectionTitle from "../atom/Texts/MenuSectionTitle";
import Menu from "../organism/Menu";
import { useDispatch } from "react-redux";
import { thunkSignOutUser } from "../../@redux/modules/auth/thunks";
import { Color } from "../../types/Color";
import ProfilePlaceholder from "../atom/ProfilePlaceholder";

const SideMenu = (props: { isOn: boolean; onOff: () => void; isTechnician?: boolean }) => {
  const dispatch = useDispatch<any>();
  const user = useSelector((state: RootState) => state.auth.user);
  const router = useRouter();

  const [isOnPrivacyPolicy, setIsOnPrivacyPolicy] = useState<boolean>(false);
  const [isOnTermsOfUse, setIsOnTermsOfuse] = useState<boolean>(false);

  return (
    <div className="lg:hidden">
      <PrivacyPolicyModal isOn={isOnPrivacyPolicy} onOff={() => setIsOnPrivacyPolicy(false)} />
      <TermsOfUseModal isOn={isOnTermsOfUse} onOff={() => setIsOnTermsOfuse(false)} />
      <div
        className="absolute w-full h-full top-0 right-0 z-20"
        style={{ display: props.isOn ? undefined : "none" }}
      >
        <div className="fixed w-full h-full bg-[rgba(0,0,0,0.4)]" onClick={props.onOff}></div>
        <div
          className="absolute right-0 bg-white h-full flex flex-col"
          style={{
            width: 280,
          }}
        >
          <div
            className="flex items-center px-4 py-3 justify-between"
            style={{
              height: 100,
              background: Color.BLACK_PEARL,
            }}
          >
            <div className="flex flex-col justify-between">
              <button className="self-baseline rotate-180" onClick={props.onOff}>
                <Image src={require("../../../public/white-back.png")} />
              </button>
              <span className="text-white text-lg font-medium ml-2">My Page</span>
            </div>
            <div className="flex flex-col items-end">
              <div className="w-10 h-10 rounded-full overflow-hidden flex items-center mb-1">
                {user?.profilePictureUri ? (
                  <img src={user?.profilePictureUri} className="w-10 h-10 object-cover" />
                ) : (
                  <ProfilePlaceholder size={32} />
                )}
              </div>

              <span className="text-white text-sm -mb-1 font-medium">
                {user ? user.firstName + " " + user.lastName : ""}
              </span>
              <span className="text-xs text-[rgba(255,255,255,0.6)]">
                {props.isTechnician ? "Technician" : "Customer"}
              </span>
            </div>
          </div>
          <div className="flex flex-col h-full overflow-y-scroll">
            {!props.isTechnician ? (
              <BackgroundSmallButton
                title="Book an Appointment"
                onClick={() => {
                  router.push("/");
                  props.onOff();
                }}
                backgroundColor="ACCENT"
                style={{ margin: 20, marginBottom: 0 }}
              />
            ) : null}
            <Menu>
              <div className="w-full flex flex-col self-stretch px-3">
                <MenuSectionTitle style={{ paddingLeft: 12, paddingRight: 12 }}>
                  Booking
                </MenuSectionTitle>
                <MenuButton
                  icon={<ClipboardIcon />}
                  title="Bookings"
                  onClick={() => {
                    router.push(props.isTechnician ? "/tech_booking" : "/customer_booking");
                  }}
                  isSelected={
                    router.pathname === "/customer_booking" || router.pathname === "/tech_booking"
                  }
                />
              </div>
              <div
                className="self-stretch my-5 border-b"
                style={{
                  height: 1,
                  backgroundColor: Color.BLACK_01,
                }}
              />
              <div className="w-full flex flex-col self-stretch px-3">
                <MenuSectionTitle style={{ paddingLeft: 12, paddingRight: 12 }}>
                  Account
                </MenuSectionTitle>
                <MenuButton
                  icon={<PeopleIcon />}
                  title="Profile"
                  onClick={() => {
                    router.push(props.isTechnician ? "/account" : "/customer_profile");
                  }}
                  isSelected={
                    router.pathname === "/customer_profile" || router.pathname === "/account"
                  }
                />
              </div>
              <div
                className="self-stretch my-5 border-b"
                style={{
                  height: 1,
                  backgroundColor: Color.BLACK_01,
                }}
              />
              <div className="w-full flex flex-col self-stretch px-3" style={{ paddingBottom: 60 }}>
                <MenuButton
                  icon={<LockIcon />}
                  title="Privacy Policy"
                  onClick={() => {
                    setIsOnPrivacyPolicy(true);
                    props.onOff();
                  }}
                  isSelected={false}
                />
                <MenuButton
                  icon={<HeadphoneIcon />}
                  title="Terms of Use"
                  onClick={() => {
                    setIsOnTermsOfuse(true);
                    props.onOff();
                  }}
                  isSelected={false}
                />
                <MenuButton
                  icon={undefined}
                  title="Sign out"
                  onClick={() => {
                    dispatch(thunkSignOutUser());
                    router.replace("/login");
                  }}
                  isSelected={false}
                />
              </div>
            </Menu>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideMenu;
