import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { RootState } from "../../@redux";
import { thunkFetchUserData } from "../../@redux/modules/auth/thunks";
import { getCurrentUser } from "../../functions/cognito/util";
import { RoleId } from "../../types/User";
import Spinner from "../atom/Spinner";
import GlobalHeader from "../organism/Header/general";
import SideMenu from "./SideMenu";

const technicianAvailablePaths = [
  "/tech_booking",
  "/tech_earning",
  "/tech_setting",
  "/account",
  "/change_password",
];

const adminAvailablePaths = ["/manage_technicians", "/reset_passwords_admin"];

const customerAvailablePaths = [
  "/",
  "/account",
  "/booking_address",
  "/booking_calendar",
  "/booking_date",
  "/customer_booking",
  "/information",
  "/customer_payment",
  "/customer_profile",
  "/customer_receipt",
  "/customer_review",
  "/customer_setting",
  "/reset_password",
  "/addons_information",
];

const technicianMainPath = "/tech_booking";
const customerMainPath = "/customer_booking";

const Layout = ({
  isTechnician,
  children,
}: {
  isTechnician?: boolean;
  children: React.ReactNode;
}) => {
  const router = useRouter();
  const [isShowingUserModal, setIsShowingUserModal] = useState<boolean>(false);

  const user = useSelector((state: RootState) => state.auth.user);

  const dispatch = useDispatch<any>();

  useEffect(() => {
    const fetchUserData = async () => {
      if ((await getCurrentUser()) === null) {
        router.push("/login");
      } else {
        dispatch(thunkFetchUserData(router));
      }
    };
    fetchUserData();
  }, []);

  if (user === undefined) {
    // return spinner
    return (
      <div className="w-full h-full flex flex-col justify-center items-center bg-[#f8fafb]">
        <div className="flex flex-col items-center">
          <Spinner />
        </div>
      </div>
    );
  } else {
    let pass = false;
    if (user.roleId === RoleId.TECHNICIAN) {
      for (const path of technicianAvailablePaths) {
        if (router.pathname === path) {
          pass = true;
        }
      }
    } else if (user.roleId === RoleId.ADMIN) {
      for (const path of [...technicianAvailablePaths, ...adminAvailablePaths]) {
        if (router.pathname === path) {
          pass = true;
        }
      }
    } else if (user.roleId === RoleId.CUSTOMER) {
      for (const path of customerAvailablePaths) {
        if (router.pathname === path) {
          pass = true;
        }
      }
    }
    if (!pass) {
      // return unavailable page and redirect to home
      return (
        <div className="w-full h-full flex flex-col justify-center items-center bg-[#f8fafb]">
          <div className="flex flex-col items-center">
            <span className="text-xl font-bold mb-4">Unavailable Page</span>

            <button
              className="bg-[#242424] flex items-center px-4 py-3 justify-between rounded-lg"
              onClick={() => {
                if (user.roleId === RoleId.TECHNICIAN || user.roleId === RoleId.ADMIN)
                  router.push(technicianMainPath);
                if (user.roleId === RoleId.CUSTOMER) router.push(customerMainPath);
              }}
            >
              <span className="text-white text-sm font-medium">Go to Home</span>
            </button>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="w-full h-full flex flex-col items-center">
      <SideMenu
        isOn={isShowingUserModal}
        onOff={() => setIsShowingUserModal(false)}
        isTechnician={isTechnician}
      />
      <GlobalHeader
        isTechnician={isTechnician}
        isShowingUserModal={isShowingUserModal}
        setIsShowingUserModal={setIsShowingUserModal}
      />
      <div
        className="self-stretch h-full bg-[#F8FAFB]"
        onClick={() => setIsShowingUserModal(false)}
      >
        {children}
      </div>
      <ToastContainer
        position="bottom-center"
        autoClose={3000}
        hideProgressBar
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </div>
  );
};

export default Layout;
