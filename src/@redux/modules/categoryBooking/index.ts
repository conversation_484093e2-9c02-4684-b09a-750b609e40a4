import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CategorySessionItem } from "../../../types/Category";

/**
 * Don't use this anymore.
 */
export interface CategoryBookingState {
  categorySessionItems: CategorySessionItem[];
  selectedCategoryIndex: number;
}

const initialState: CategoryBookingState = {
  categorySessionItems: [],
  selectedCategoryIndex: -1,
};

const categoryBookingSlice = createSlice({
  name: "categoryBooking",
  initialState,
  reducers: {
    setCategorySessionItems(state, action: PayloadAction<CategorySessionItem[]>) {
      state.categorySessionItems = action.payload;
    },
    setSelectedCategoryIndex(state, action: PayloadAction<number>) {
      state.selectedCategoryIndex = action.payload;
    },
    resetOccupation(state) {
      state.categorySessionItems = [];
      state.selectedCategoryIndex = -1;
    },
  },
});

export const { setCategorySessionItems, setSelectedCategoryIndex, resetOccupation } =
  categoryBookingSlice.actions;
export default categoryBookingSlice.reducer;
