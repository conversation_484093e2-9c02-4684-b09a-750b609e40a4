import { RootState } from "../..";

export function getSelectedCategoryTotalDuration(duck: RootState["categoryBooking"]) {
  // TODO : should handle multi category case
  if (duck.categorySessionItems.length > 0) {
    let result = 0;
    for (let i = 0; i < duck.categorySessionItems[0].services.length; i++) {
      result +=
        duck.categorySessionItems[0].services[i].cars.length *
        duck.categorySessionItems[0].services[i].duration;
    }

    return result;
  } else {
    return 0;
  }
}

export function getSelectedCategory(duck: RootState["categoryBooking"]) {
  if (duck.selectedCategoryIndex !== -1) {
    return duck.categorySessionItems[duck.selectedCategoryIndex];
  } else {
    return null;
  }
}
