import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { thunkFetchTechnicianAvailability } from "./thunk";

export interface Availability {
  dayOfWeek: string;
  slots: {
    startTime: string;
    endTime: string;
  }[];
}

interface TechBookingState {
  isLoading: boolean;
  dateOffset: Date;
  availabilities: Availability[] | undefined;
}

const initialState: TechBookingState = {
  isLoading: false,
  dateOffset: new Date(),
  availabilities: undefined,
};

const techBookingSlice = createSlice({
  name: "techBooking",
  initialState,
  reducers: {
    dateOffsetPrevDay: (state) => {
      state.dateOffset = new Date(
        state.dateOffset.getFullYear(),
        state.dateOffset.getMonth(),
        state.dateOffset.getDate() - 1,
      );
    },
    dateOffsetNextDay: (state) => {
      state.dateOffset = new Date(
        state.dateOffset.getFullYear(),
        state.dateOffset.getMonth(),
        state.dateOffset.getDate() + 1,
      );
    },
    dateOffsetToday: (state) => {
      state.dateOffset = new Date();
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(thunkFetchTechnicianAvailability.pending.type, (state, action) => {
        state.isLoading = true;
      })
      .addCase(
        thunkFetchTechnicianAvailability.fulfilled.type,
        (state, action: PayloadAction<Availability[]>) => {
          state.isLoading = false;
          state.availabilities = action.payload;
        },
      )
      .addCase(thunkFetchTechnicianAvailability.rejected.type, (state, action) => {
        state.isLoading = false;
        console.error(action);
      });
  },
});

export const { dateOffsetPrevDay, dateOffsetNextDay, dateOffsetToday } = techBookingSlice.actions;
export default techBookingSlice.reducer;
