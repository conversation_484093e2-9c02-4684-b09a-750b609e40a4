import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Addon } from "../../../types/Addon";
import { CarServiceExtension } from "../../../types/CarServiceExtension";

type SummaryState = {
  carServiceExtensions: CarServiceExtension[];
};

const initialState: SummaryState = {
  carServiceExtensions: [],
};

const summarySlice = createSlice({
  name: "summary",
  initialState,
  reducers: {
    setCarServiceExtensions: (state, action: PayloadAction<CarServiceExtension[]>) => {
      state.carServiceExtensions = action.payload;
    },
    addAddon: (
      state,
      action: PayloadAction<{
        carServiceExtensionId: number;
        carId: number;
        addon: Addon;
      }>,
    ) => {
      console.log(action.payload);

      const targetCarServiceExtension = state.carServiceExtensions.find(
        (item) => item.id === action.payload.carServiceExtensionId,
      );

      if (targetCarServiceExtension !== undefined) {
        const targetCar = targetCarServiceExtension.cars?.find(
          (item) => item.car.id === action.payload.carId,
        );

        if (targetCar !== undefined) {
          if (targetCar.addons) {
            targetCar.addons = targetCar.addons.concat(action.payload.addon);
          } else {
            targetCar.addons = [action.payload.addon];
          }
        }
      }
    },
    deleteAddon: (
      state,
      action: PayloadAction<{
        carServiceExtensionId: number;
        carId: number;
        addon: Addon;
      }>,
    ) => {
      const targetCarServiceExtension = state.carServiceExtensions.find(
        (item) => item.id === action.payload.carServiceExtensionId,
      );

      if (targetCarServiceExtension !== undefined) {
        const targetCar = targetCarServiceExtension.cars?.find(
          (item) => item.car.id === action.payload.carId,
        );

        if (targetCar !== undefined) {
          if (targetCar.addons) {
            targetCar.addons = targetCar.addons.filter(
              (addon) => addon.id !== action.payload.addon.id,
            );
          }
        }
      }
    },
  },
});

export const { setCarServiceExtensions, addAddon, deleteAddon } = summarySlice.actions;
export default summarySlice.reducer;
