import { createAsyncThunk } from "@reduxjs/toolkit";
import { Auth } from "aws-amplify";
import {
  apiGetUser,
  apiUpdateUserAddress,
  apiUpdateUserBiography,
} from "../../../functions/api/user";
import { apiSignOut } from "../../../functions/cognito";
import { NextRouter } from "next/router";

export const thunkFetchUserData = createAsyncThunk("fetchUserData", async (router: NextRouter) => {
  try {
    return await apiGetUser();
  } catch (error) {
    localStorage.clear();
    router.push("/login");
  }
});

export const thunkSignOutUser = createAsyncThunk("signOutUser", async () => {
  await Auth.signOut();
  await apiSignOut();
});

export const thunkUpdateUserNamePhone = createAsyncThunk(
  "updateUserNamePhone",
  async (payload: { firstName: string; lastName: string; phone: string }) => {
    return await apiUpdateUserAddress({
      firstName: payload.firstName,
      lastName: payload.lastName,
      phone: payload.phone,
    });
  },
);

export const thunkUpdateUserProfile = createAsyncThunk(
  "updateUserProfile",
  async (payload: string) => {
    return (
      await apiUpdateUserAddress({
        profilePictureUri: payload,
      })
    ).profilePictureUri;
  },
);

export const thunkUpdateUserBiography = createAsyncThunk(
  "updateUserBiography",
  async (payload: { biography: string }) => {
    return await apiUpdateUserBiography({
      biography: payload.biography,
    });
  },
);
