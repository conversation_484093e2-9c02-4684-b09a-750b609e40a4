import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { User } from "../../../types/User";
import {
  thunkFetchUserData,
  thunkSignOutUser,
  thunkUpdateUserBiography,
  thunkUpdateUserNamePhone,
  thunkUpdateUserProfile,
} from "./thunks";

interface AuthState {
  isLoading: boolean;
  user: User | undefined;
}

const initialState: AuthState = {
  isLoading: false,
  user: undefined,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(thunkFetchUserData.pending.type, (state) => {
        state.isLoading = true;
      })
      .addCase(thunkFetchUserData.fulfilled.type, (state, action: PayloadAction<User>) => {
        state.isLoading = false;
        state.user = action.payload;
      })
      .addCase(thunkFetchUserData.rejected.type, (state, action) => {
        state.isLoading = false;
        console.error(action);
      })
      .addCase(thunkSignOutUser.fulfilled.type, (state, action) => {
        state.user = undefined;
      })
      .addCase(thunkUpdateUserProfile.pending.type, (state) => {
        state.isLoading = true;
      })
      .addCase(thunkUpdateUserProfile.fulfilled.type, (state, action: PayloadAction<string>) => {
        if (state.user) {
          state.user = {
            ...state.user,
            profilePictureUri: action.payload,
          };
        }
        state.isLoading = false;
      })
      .addCase(thunkUpdateUserProfile.rejected.type, (state, action) => {
        state.isLoading = false;
        console.error(action);
      })
      .addCase(thunkUpdateUserNamePhone.pending.type, (state) => {
        state.isLoading = true;
      })
      .addCase(
        thunkUpdateUserNamePhone.fulfilled.type,
        (
          state,
          action: PayloadAction<{
            firstName: string;
            lastName: string;
            phone: string;
          }>,
        ) => {
          if (state.user) {
            state.user.firstName = action.payload.firstName;
            state.user.lastName = action.payload.lastName;
            state.user.phone = action.payload.phone;
            state.isLoading = false;
          }
        },
      )
      .addCase(thunkUpdateUserNamePhone.rejected, (state, action) => {
        state.isLoading = false;
        console.error(action);
      })
      .addCase(thunkUpdateUserBiography.pending.type, (state) => {
        state.isLoading = true;
      })
      .addCase(
        thunkUpdateUserBiography.fulfilled.type,
        (
          state,
          action: PayloadAction<{
            biography: string;
          }>,
        ) => {
          if (state.user) {
            state.user.biography = action.payload.biography;
            state.isLoading = false;
          }
        },
      )
      .addCase(thunkUpdateUserBiography.rejected, (state, action) => {
        state.isLoading = false;
        console.error(action);
      });
  },
});

export default authSlice.reducer;
