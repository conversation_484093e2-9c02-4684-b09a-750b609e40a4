import { combineReducers, configureStore, getDefaultMiddleware } from "@reduxjs/toolkit";
import logger from "redux-logger";
import authSlice from "./modules/auth";
import techBookingSlice from "./modules/techBooking";
import summarySlice from "./modules/summary";
import categoryBooking from "./modules/categoryBooking";

const reducers = combineReducers({
  auth: authSlice,
  techBooking: techBookingSlice,
  summary: summarySlice,
  categoryBooking: categoryBooking,
});

export const store =
  process.env.NEXT_PUBLIC_IS_DEV === "true"
    ? configureStore({
        reducer: reducers,
        middleware: [
          ...getDefaultMiddleware({
            serializableCheck: false,
          }),
          logger,
        ],
      })
    : configureStore({
        reducer: reducers,
        middleware: [
          ...getDefaultMiddleware({
            serializableCheck: false,
          }),
        ],
      });

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
