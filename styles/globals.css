@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --vh: 100%;
}

html {
  height: 100%;
  overflow: hidden;
}

body {
  height: 100%;
  overflow-y: hidden;
}

::-ms-reveal {
  display: none;
}

#__next {
  height: 100%;
}

@font-face {
  font-family: "Satoshi";
  font-weight: 300;
  src: url("/fonts/Satoshi-Light.otf");
}

@font-face {
  font-family: "Satoshi";
  font-weight: 400;
  src: url("/fonts/Satoshi-Regular.otf");
}

@font-face {
  font-family: "Satoshi";
  font-weight: 500;
  src: url("/fonts/Satoshi-Medium.otf");
}

@font-face {
  font-family: "Satoshi";
  font-weight: 600;
  src: url("/fonts/Satoshi-Bold.otf");
}

@font-face {
  font-family: "Satoshi";
  font-weight: 700;
  src: url("/fonts/Satoshi-Black.otf");
}

@font-face {
  font-family: "Spectral";
  font-weight: 400;
  src: url("/fonts/Spectral-Regular");
}

@font-face {
  font-family: "Spectral";
  font-weight: 500;
  src: url("/fonts/Spectral-Medium.ttf");
}

@font-face {
  font-family: "Spectral";
  font-weight: 600;
  src: url("/fonts/Spectral-SemiBold.ttf");
}

@font-face {
  font-family: "Spectral";
  font-weight: 700;
  src: url("/fonts/Spectral-Bold.ttf");
}

@font-face {
  font-family: "Proza Libre";
  font-weight: 400;
  src: url("/fonts/ProzaLibre-Regular.ttf");
}

@font-face {
  font-family: "Proza Libre";
  font-weight: 500;
  src: url("/fonts/ProzaLibre-Medium.ttf");
}

@font-face {
  font-family: "Proza Libre";
  font-weight: 600;
  src: url("/fonts/ProzaLibre-SemiBold.ttf");
}

@font-face {
  font-family: "Proza Libre";
  font-weight: 700;
  src: url("/fonts/ProzaLibre-Bold.ttf");
}

@font-face {
  font-family: "Roboto";
  font-weight: 400;
  src: url("/fonts/Roboto-Regular.ttf");
}

@font-face {
  font-family: "Roboto";
  font-weight: 500;
  src: url("/fonts/Roboto-Medium.ttf");
}

@font-face {
  font-family: "Roboto";
  font-weight: 700;
  src: url("/fonts/Roboto-Bold.ttf");
}

:root {
  --size: 55px;
  --clr-bg: #272324;
  --clr1: #bbbb88;
  --clr2: #ccc68d;
  --clr3: #eedd99;
  --clr4: #eec290;
  --clr5: #fca311;
}

.my-spinner {
  --animation-duration: 650ms;
  position: relative;
  width: var(--size);
  height: var(--size);
}

.my-spinner .spinner-item {
  position: absolute;
  width: var(--item-size);
  height: var(--item-size);
  top: calc(50% - var(--item-size) / 2);
  left: calc(50% - var(--item-size) / 2);
  border: 4px solid transparent;
  border-left: 4px solid var(--clr-spinner);
  border-right: 4px solid var(--clr-spinner);
  border-radius: 50%;
  animation: spinner2 var(--animation-duration) linear infinite;
}

@keyframes spinner2 {
  to {
    transform: rotate(360deg);
  }
}

.my-spinner .spinner-item:nth-of-type(1) {
  --item-size: var(--size);
  --clr-spinner: var(--clr1);
  border-top: 4px solid var(--clr1);
}

.my-spinner .spinner-item:nth-of-type(2) {
  --item-size: calc(var(--size) - 15px);
  --clr-spinner: var(--clr5);
  border-bottom: 4px solid var(--clr5);
}

.my-spinner .spinner-item:nth-of-type(3) {
  --item-size: calc(var(--size) - 30px);
  --clr-spinner: var(--clr3);
  border-top: 4px solid var(--clr3);
}

.sbl-circ-path {
  height: 20px;
  width: 20px;
  color: rgba(90, 90, 90, 0.2);
  position: relative;
  display: inline-block;
  border: 3.5px solid;
  border-radius: 50%;
  border-right-color: #5a5a5a;
  animation: rotate 0.5s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.line-clamp {
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-2 {
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-1 {
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-none {
  -webkit-line-clamp: unset;
}

pre {
  white-space: pre-wrap;
  /* CSS3*/
  white-space: -moz-pre-wrap;
  /* Mozilla, since 1999 */
  white-space: -pre-wrap;
  /* Opera 4-6 */
  white-space: -o-pre-wrap;
  /* Opera 7 */
  word-wrap: break-all;
  /* Internet Explorer 5.5+ */
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.custom-input::placeholder {
  font-weight: 400;
  font-size: 16;
  line-height: "150%";
  color: #667085;
}

.tooltip {
  visibility: hidden;
  opacity: 0;
}

.tooltip-parent:hover .tooltip {
  visibility: visible;
  opacity: 1;
}

input[type="radio"] {
  appearance: none;
  width: 16px;
  height: 16px;
  box-shadow: 0 0 0 1.5px rgba(52, 64, 84, 0.6);
  border-radius: 50%;
  background-color: white;
}

input[type="radio"]:checked {
  border: 2.5px solid white;
  box-shadow: 0 0 0 2px #fca311;
  background-color: #fca311;
}

@layer utilities {
  ::-webkit-scrollbar {
    width: 16px;
    height: 16px;
  }

  ::-webkit-scrollbar-thumb {
    height: 30%;
    /*color of scrollbar*/
    background: #898989;
    border-radius: 16px;
    border: 4px solid rgba(0, 0, 0, 0.01);
    background-clip: padding-box;
  }

  ::-webkit-scrollbar-track {
    display: none;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(137, 137, 137, 0.5);
    background-clip: padding-box;
  }

  ::-webkit-scrollbar-button {
    display: none;
  }
}
