{"name": "wheeleasy_web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest --watchAll"}, "dependencies": {"@aws-amplify/ui-react": "^4.1.0", "@babel/core": "^7.18.13", "@babel/plugin-transform-react-jsx": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-typescript": "^7.18.6", "@nodelib/fs.scandir": "^2.1.5", "@reduxjs/toolkit": "^1.8.5", "@stripe/stripe-js": "^1.34.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.4.3", "@types/axios": "^0.14.0", "@types/jest": "^29.5.0", "@types/mixpanel-browser": "^2.38.1", "@types/react-redux": "^7.1.24", "@types/redux-logger": "^3.0.9", "autoprefixer": "^10.4.12", "aws-amplify": "^5.0.4", "aws-sdk": "^2.1678.0", "axios": "^0.27.2", "browser-image-compression": "^2.0.0", "cookies-next": "^2.1.1", "jest": "^28.0.1", "jest-environment-jsdom": "^29.0.1", "mixpanel-browser": "^2.47.0", "moment": "^2.29.4", "moment-timezone": "^0.5.41", "next": "^12.2.3", "next-auth": "^4.18.6", "nextjs-google-analytics": "^2.3.3", "postcss": "^8.4.18", "react": "18.1.0", "react-cool-onclickoutside": "^1.7.0", "react-dom": "18.1.0", "react-google-recaptcha-v3": "^1.10.1", "react-loader-spinner": "^5.1.5", "react-redux": "^8.0.4", "react-test-renderer": "^18.2.0", "react-toastify": "^9.0.8", "redux-logger": "^3.0.6", "rrule": "^2.7.2", "sharp": "^0.32.4", "stripe": "^9.16.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.1.8", "timezone-mock": "^1.3.6", "ts-jest": "^28.0.8", "use-places-autocomplete": "^4.0.0"}, "devDependencies": {"@types/google.maps": "^3.52.5", "@types/node": "17.0.31", "@types/react": "18.0.9", "@types/react-dom": "18.0.3", "babel-loader": "^8.2.5", "eslint": "8.15.0", "eslint-config-next": "12.1.6", "prettier": "^3.3.3", "tailwind-scrollbar": "^3.0.5", "typescript": "4.6.4"}, "jest": {"automock": false, "transform": {"^.+\\.(t|j)sx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "json"], "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}, "globals": {"ts-jest": {"diagnostics": true, "isolatedModules": true}}, "setupFilesAfterEnv": ["./src/jest.setup.ts"]}}